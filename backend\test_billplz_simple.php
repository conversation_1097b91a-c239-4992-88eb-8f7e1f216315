<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\PaymentSetting;

echo "=== SIMPLE BILLPLZ TEST ===\n";

// Get settings
$enabled = PaymentSetting::get('billplz_enabled');
$sandbox = PaymentSetting::get('billplz_sandbox_mode');
$apiKey = PaymentSetting::get('billplz_api_key');
$collectionId = PaymentSetting::get('billplz_collection_id');

echo "Configuration:\n";
echo "- Enabled: " . ($enabled ? 'YES' : 'NO') . "\n";
echo "- Sandbox: " . ($sandbox ? 'YES' : 'NO') . "\n";
echo "- API Key: " . ($apiKey ? substr($apiKey, 0, 8) . '... (' . strlen($apiKey) . ' chars)' : 'NOT SET') . "\n";
echo "- Collection ID: " . ($collectionId ?: 'NOT SET') . "\n";

if (!$apiKey || !$collectionId) {
    echo "\n❌ Configuration incomplete!\n";
    exit(1);
}

// Test 1: Manual cURL test
echo "\n=== TEST 1: Manual cURL ===\n";

$endpoint = $sandbox ? 'https://www.billplz-sandbox.com/api/v3/collections' : 'https://www.billplz.com/api/v3/collections';
echo "Testing endpoint: $endpoint\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $endpoint);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
curl_setopt($ch, CURLOPT_USERPWD, $apiKey . ':');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: application/json',
    'User-Agent: Laravel-Test'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
if ($error) {
    echo "cURL Error: $error\n";
}

if ($httpCode === 200) {
    echo "✓ API connection successful!\n";
    $data = json_decode($response, true);
    if (isset($data['collections'])) {
        echo "✓ Found " . count($data['collections']) . " collections\n";
    }
} else {
    echo "❌ API connection failed\n";
    echo "Response: " . substr($response, 0, 200) . "\n";
}

// Test 2: Test specific collection
if ($httpCode === 200) {
    echo "\n=== TEST 2: Collection Access ===\n";
    
    $collectionEndpoint = $sandbox ? 
        "https://www.billplz-sandbox.com/api/v3/collections/$collectionId" : 
        "https://www.billplz.com/api/v3/collections/$collectionId";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $collectionEndpoint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    curl_setopt($ch, CURLOPT_USERPWD, $apiKey . ':');
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Accept: application/json',
        'User-Agent: Laravel-Test'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "Collection HTTP Code: $httpCode\n";
    
    if ($httpCode === 200) {
        echo "✓ Collection access successful!\n";
        $collection = json_decode($response, true);
        echo "Collection: {$collection['title']} (Status: {$collection['status']})\n";
    } else {
        echo "❌ Collection access failed\n";
        echo "Response: " . substr($response, 0, 200) . "\n";
    }
}

// Test 3: Test with Billplz SDK
echo "\n=== TEST 3: Billplz SDK ===\n";

try {
    $connect = new \Billplz\Connect($apiKey);
    $connect->setMode(!$sandbox); // true for production, false for staging
    $api = new \Billplz\API($connect);
    
    $response = $api->getCollectionIndex();
    echo "SDK HTTP Code: " . $response[0] . "\n";
    
    if ($response[0] === 200) {
        echo "✓ SDK connection successful!\n";
    } else {
        echo "❌ SDK connection failed\n";
        echo "SDK Response: " . $response[1] . "\n";
    }
} catch (Exception $e) {
    echo "❌ SDK Error: " . $e->getMessage() . "\n";
}

echo "\n=== TEST COMPLETED ===\n";

// Recommendations
if ($httpCode !== 200) {
    echo "\n=== RECOMMENDATIONS ===\n";
    echo "1. Verify you're logged into the correct Billplz environment:\n";
    if ($sandbox) {
        echo "   - Go to: https://www.billplz-sandbox.com/\n";
        echo "   - Check Settings → API Keys\n";
        echo "   - Ensure API key starts with: " . substr($apiKey, 0, 8) . "...\n";
    } else {
        echo "   - Go to: https://www.billplz.com/\n";
        echo "   - Check Settings → API Keys\n";
        echo "   - Ensure API key starts with: " . substr($apiKey, 0, 8) . "...\n";
    }
    echo "\n2. Check account status:\n";
    echo "   - Account should be verified\n";
    echo "   - API access should be enabled\n";
    echo "   - No account restrictions\n";
    echo "\n3. Try creating a new API key and collection\n";
}
