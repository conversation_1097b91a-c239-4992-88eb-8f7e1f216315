<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Page;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;

class PageController extends Controller
{
    /**
     * Display a listing of published pages.
     */
    public function index(): JsonResponse
    {
        try {
            $pages = Cache::remember('published_pages', 3600, function () {
                return Page::published()
                    ->ordered()
                    ->select(['id', 'title', 'slug', 'meta_title', 'meta_description', 'published_at'])
                    ->get();
            });

            return response()->json([
                'success' => true,
                'data' => $pages
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve pages',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Display the specified page by slug.
     */
    public function show(string $slug): JsonResponse
    {
        try {
            $page = Cache::remember("page_{$slug}", 3600, function () use ($slug) {
                return Page::where('slug', $slug)
                    ->published()
                    ->first();
            });

            if (!$page) {
                return response()->json([
                    'success' => false,
                    'message' => 'Page not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $page
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve page',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get page data for Puck editor (admin only).
     */
    public function getPuckData(string $slug): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user || $user->role !== 'admin') {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }

            $page = Page::where('slug', $slug)->first();

            if (!$page) {
                return response()->json([
                    'success' => false,
                    'message' => 'Page not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $page->id,
                    'title' => $page->title,
                    'slug' => $page->slug,
                    'content' => $page->content ?: [
                        'content' => [],
                        'root' => [
                            'title' => $page->title,
                            'metaDescription' => $page->meta_description ?: '',
                        ]
                    ],
                    'status' => $page->status,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve page data',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Save Puck data for a page (admin only).
     */
    public function savePuckData(Request $request, string $slug): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user || $user->role !== 'admin') {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }

            $validator = Validator::make($request->all(), [
                'content' => 'required|array',
                'title' => 'sometimes|string|max:255',
                'meta_description' => 'sometimes|string|max:500',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $page = Page::where('slug', $slug)->first();

            if (!$page) {
                return response()->json([
                    'success' => false,
                    'message' => 'Page not found'
                ], 404);
            }

            $updateData = [
                'content' => $request->input('content'),
                'updated_by' => $user->id,
            ];

            // Update title and meta description if provided
            if ($request->has('title')) {
                $updateData['title'] = $request->input('title');
            }

            if ($request->has('meta_description')) {
                $updateData['meta_description'] = $request->input('meta_description');
            }

            $page->update($updateData);

            // Clear cache
            Cache::forget("page_{$slug}");
            Cache::forget('published_pages');

            return response()->json([
                'success' => true,
                'message' => 'Page updated successfully',
                'data' => $page
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save page',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }
}
