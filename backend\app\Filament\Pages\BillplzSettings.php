<?php

namespace App\Filament\Pages;

use App\Models\PaymentSetting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Notifications\Notification;
use Filament\Actions\Action;
use Billplz\API;
use Billplz\Connect;

class BillplzSettings extends Page implements Forms\Contracts\HasForms
{
    use Forms\Concerns\InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-credit-card';
    protected static ?string $navigationGroup = 'Settings';
    protected static ?string $navigationLabel = 'Billplz Payment Settings';
    protected static ?int $navigationSort = 4;
    protected static string $view = 'filament.pages.billplz-settings';

    public ?array $data = [];

    protected function getHeaderActions(): array
    {
        return [
            Action::make('validate')
                ->label('Test Configuration')
                ->icon('heroicon-o-check-circle')
                ->action('validateConfiguration')
                ->color('info'),
        ];
    }

    public function mount(): void
    {
        try {
            // Ensure all values are properly cast for form fields
            $this->data = [
                'billplz_enabled' => (bool) PaymentSetting::get('billplz_enabled', false),
                'billplz_sandbox_mode' => (bool) PaymentSetting::get('billplz_sandbox_mode', true),
                'billplz_api_key' => (string) PaymentSetting::get('billplz_api_key', ''),
                'billplz_collection_id' => (string) PaymentSetting::get('billplz_collection_id', ''),
                'billplz_x_signature_key' => (string) PaymentSetting::get('billplz_x_signature_key', ''),
            ];
        } catch (\Exception $e) {
            // If there's an error loading settings, use defaults
            $this->data = [
                'billplz_enabled' => false,
                'billplz_sandbox_mode' => true,
                'billplz_api_key' => '',
                'billplz_collection_id' => '',
                'billplz_x_signature_key' => '',
            ];
        }
    }

    public function form(Form $form): Form
    {
        return $form
            ->statePath('data')
            ->schema([
                Forms\Components\Section::make('Billplz Configuration')
                    ->description('Configure your Billplz payment gateway settings')
                    ->schema([
                        Forms\Components\Toggle::make('billplz_enabled')
                            ->label('Enable Billplz Payment Gateway')
                            ->helperText('Enable or disable Billplz payment processing')
                            ->default(false),

                        Forms\Components\Toggle::make('billplz_sandbox_mode')
                            ->label('Sandbox Mode')
                            ->helperText('Enable for testing (use sandbox credentials), disable for production')
                            ->default(true),

                        Forms\Components\TextInput::make('billplz_api_key')
                            ->label('API Key')
                            ->helperText('Your Billplz API key (must match the environment - sandbox or production)')
                            ->password()
                            ->revealable()
                            ->required(fn (callable $get) => $get('billplz_enabled')),

                        Forms\Components\TextInput::make('billplz_collection_id')
                            ->label('Collection ID')
                            ->helperText('Your Billplz collection ID (must exist in the same environment as API key)')
                            ->required(fn (callable $get) => $get('billplz_enabled')),

                        Forms\Components\TextInput::make('billplz_x_signature_key')
                            ->label('X Signature Key')
                            ->helperText('Your Billplz X Signature key for webhook verification (optional but recommended)')
                            ->password()
                            ->revealable(),
                    ])->columns(2),

                Forms\Components\Section::make('Environment Guide')
                    ->description('Make sure you use credentials from the correct environment')
                    ->schema([
                        Forms\Components\Placeholder::make('sandbox_guide')
                            ->label('Sandbox Environment')
                            ->content('For testing: Use credentials from https://www.billplz-sandbox.com/')
                            ->helperText('Enable "Sandbox Mode" above when using sandbox credentials'),

                        Forms\Components\Placeholder::make('production_guide')
                            ->label('Production Environment')
                            ->content('For live payments: Use credentials from https://www.billplz.com/')
                            ->helperText('Disable "Sandbox Mode" above when using production credentials'),
                    ])->columns(2),

                Forms\Components\Section::make('Webhook Information')
                    ->description('Configure these URLs in your Billplz dashboard')
                    ->schema([
                        Forms\Components\Placeholder::make('callback_url')
                            ->label('Callback URL')
                            ->content(fn () => (string) route('api.billplz.callback', [], true))
                            ->helperText('Set this as your callback URL in Billplz dashboard'),

                        Forms\Components\Placeholder::make('redirect_url')
                            ->label('Redirect URL')
                            ->content(fn () => (string) url('/dashboard/credit'))
                            ->helperText('Users will be redirected here after payment'),
                    ])->columns(1),
            ]);
    }

    public function save(): void
    {
        try {
            $data = $this->form->getState();

            // Ensure data is properly formatted
            $billplzEnabled = isset($data['billplz_enabled']) ? (bool) $data['billplz_enabled'] : false;
            $billplzSandboxMode = isset($data['billplz_sandbox_mode']) ? (bool) $data['billplz_sandbox_mode'] : true;
            $billplzApiKey = isset($data['billplz_api_key']) ? (string) $data['billplz_api_key'] : '';
            $billplzCollectionId = isset($data['billplz_collection_id']) ? (string) $data['billplz_collection_id'] : '';
            $billplzXSignatureKey = isset($data['billplz_x_signature_key']) ? (string) $data['billplz_x_signature_key'] : '';

            // Save each setting
            PaymentSetting::set('billplz_enabled', $billplzEnabled, 'boolean');
            PaymentSetting::set('billplz_sandbox_mode', $billplzSandboxMode, 'boolean');
            PaymentSetting::set('billplz_api_key', $billplzApiKey, 'string', true);
            PaymentSetting::set('billplz_collection_id', $billplzCollectionId);
            PaymentSetting::set('billplz_x_signature_key', $billplzXSignatureKey, 'string', true);

            Notification::make()
                ->title('Settings saved successfully')
                ->success()
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Error saving settings')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function validateConfiguration()
    {
        $apiKey = PaymentSetting::get('billplz_api_key');
        $collectionId = PaymentSetting::get('billplz_collection_id');
        $sandboxMode = PaymentSetting::get('billplz_sandbox_mode', true);

        if (empty($apiKey)) {
            Notification::make()
                ->title('Validation Failed')
                ->body('API Key is required')
                ->danger()
                ->send();
            return;
        }

        if (empty($collectionId)) {
            Notification::make()
                ->title('Validation Failed')
                ->body('Collection ID is required')
                ->danger()
                ->send();
            return;
        }

        try {
            $connect = new Connect($apiKey);
            $connect->setMode(!$sandboxMode);
            $api = new API($connect);

            // Test collection access
            $response = $api->getCollection($collectionId);

            if ($response[0] === 200) {
                $data = json_decode($response[1], true);
                Notification::make()
                    ->title('Configuration Valid!')
                    ->body("Successfully connected to collection: {$data['title']}")
                    ->success()
                    ->send();
            } else {
                $errorMessage = "HTTP {$response[0]} - {$response[1]}";
                if ($response[0] === 403) {
                    $errorMessage .= "\n\nThis usually means:\n• API key is for different environment\n• API key is invalid\n• No access to this collection";
                }

                Notification::make()
                    ->title('Configuration Invalid')
                    ->body($errorMessage)
                    ->danger()
                    ->send();
            }
        } catch (\Exception $e) {
            Notification::make()
                ->title('Validation Error')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
}
