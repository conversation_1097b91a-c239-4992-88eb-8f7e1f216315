<?php

namespace App\Services;

use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class PuckValidationService
{
    /**
     * Validate Puck data structure
     */
    public function validatePuckData(array $data): array
    {
        $validator = Validator::make($data, [
            'content' => 'required|array',
            'content.*' => 'required|array',
            'content.*.type' => 'required|string',
            'content.*.props' => 'required|array',
            'content.*.props.id' => 'required|string',
            'root' => 'sometimes|array',
            'zones' => 'sometimes|array',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        // Ensure all components have valid structure
        $validatedData = $validator->validated();
        $validatedData['content'] = $this->validateAndFixComponents($validatedData['content']);

        return $validatedData;
    }

    /**
     * Validate and fix component structure
     */
    private function validateAndFixComponents(array $components): array
    {
        $validatedComponents = [];

        foreach ($components as $component) {
            $validatedComponent = $this->validateComponent($component);
            if ($validatedComponent) {
                $validatedComponents[] = $validatedComponent;
            }
        }

        return $validatedComponents;
    }

    /**
     * Validate a single component
     */
    private function validateComponent(array $component): ?array
    {
        // Ensure component has required fields
        if (!isset($component['type']) || !isset($component['props'])) {
            return null;
        }

        // Ensure props has an id
        if (!isset($component['props']['id'])) {
            $component['props']['id'] = $this->generateComponentId($component['type']);
        }

        // Validate component based on type
        $component['props'] = $this->validateComponentProps($component['type'], $component['props']);

        return $component;
    }

    /**
     * Validate component props based on component type
     */
    private function validateComponentProps(string $type, array $props): array
    {
        switch ($type) {
            case 'Hero':
                return $this->validateHeroProps($props);
            case 'Text':
                return $this->validateTextProps($props);
            case 'Container':
                return $this->validateContainerProps($props);
            case 'Image':
                return $this->validateImageProps($props);
            case 'FeaturesGrid':
                return $this->validateFeaturesGridProps($props);
            default:
                return $props; // Return as-is for unknown components
        }
    }

    /**
     * Validate Hero component props
     */
    private function validateHeroProps(array $props): array
    {
        $validator = Validator::make($props, [
            'id' => 'required|string',
            'title' => 'sometimes|string|max:255',
            'subtitle' => 'sometimes|string|max:500',
            'backgroundImage' => 'sometimes|string|max:255',
            'textAlign' => 'sometimes|in:left,center,right',
            'minHeight' => 'sometimes|string',
            'primaryButton' => 'sometimes|array',
            'primaryButton.text' => 'required_with:primaryButton|string|max:100',
            'primaryButton.href' => 'required_with:primaryButton|string|max:255',
            'primaryButton.variant' => 'required_with:primaryButton|in:primary,secondary,outline',
            'secondaryButton' => 'sometimes|array',
            'secondaryButton.text' => 'required_with:secondaryButton|string|max:100',
            'secondaryButton.href' => 'required_with:secondaryButton|string|max:255',
            'secondaryButton.variant' => 'required_with:secondaryButton|in:primary,secondary,outline',
        ]);

        return $validator->validated();
    }

    /**
     * Validate Text component props
     */
    private function validateTextProps(array $props): array
    {
        $validator = Validator::make($props, [
            'id' => 'required|string',
            'content' => 'sometimes|string',
            'textAlign' => 'sometimes|in:left,center,right',
            'fontSize' => 'sometimes|string',
            'color' => 'sometimes|string',
        ]);

        return $validator->validated();
    }

    /**
     * Validate Container component props
     */
    private function validateContainerProps(array $props): array
    {
        $validator = Validator::make($props, [
            'id' => 'required|string',
            'maxWidth' => 'sometimes|string',
            'padding' => 'sometimes|string',
            'backgroundColor' => 'sometimes|string',
        ]);

        return $validator->validated();
    }

    /**
     * Validate Image component props
     */
    private function validateImageProps(array $props): array
    {
        $validator = Validator::make($props, [
            'id' => 'required|string',
            'src' => 'required|string|max:255',
            'alt' => 'sometimes|string|max:255',
            'width' => 'sometimes|string',
            'height' => 'sometimes|string',
            'objectFit' => 'sometimes|in:cover,contain,fill,scale-down,none',
        ]);

        return $validator->validated();
    }

    /**
     * Validate FeaturesGrid component props
     */
    private function validateFeaturesGridProps(array $props): array
    {
        $validator = Validator::make($props, [
            'id' => 'required|string',
            'title' => 'sometimes|string|max:255',
            'features' => 'sometimes|array',
            'features.*' => 'array',
            'features.*.title' => 'required|string|max:255',
            'features.*.description' => 'sometimes|string',
            'features.*.icon' => 'sometimes|string',
            'columns' => 'sometimes|integer|min:1|max:6',
            'spacing' => 'sometimes|string',
        ]);

        return $validator->validated();
    }

    /**
     * Generate a unique component ID
     */
    private function generateComponentId(string $type): string
    {
        return strtolower($type) . '-' . uniqid();
    }

    /**
     * Ensure all components in Puck data have IDs
     */
    public function ensureComponentIds(array $puckData): array
    {
        if (!isset($puckData['content']) || !is_array($puckData['content'])) {
            return $puckData;
        }

        foreach ($puckData['content'] as &$component) {
            if (!isset($component['props']['id'])) {
                $component['props']['id'] = $this->generateComponentId($component['type'] ?? 'component');
            }
        }

        // Handle zones if they exist
        if (isset($puckData['zones']) && is_array($puckData['zones'])) {
            foreach ($puckData['zones'] as $zoneName => &$zoneComponents) {
                if (is_array($zoneComponents)) {
                    foreach ($zoneComponents as &$component) {
                        if (!isset($component['props']['id'])) {
                            $component['props']['id'] = $this->generateComponentId($component['type'] ?? 'component');
                        }
                    }
                }
            }
        }

        return $puckData;
    }

    /**
     * Validate Puck data and ensure component IDs
     */
    public function validateAndEnsureIds(array $puckData): array
    {
        // First ensure all components have IDs
        $puckData = $this->ensureComponentIds($puckData);
        
        // Then validate the structure
        return $this->validatePuckData($puckData);
    }

    /**
     * Check if data is valid Puck format
     */
    public function isPuckData($data): bool
    {
        if (!is_array($data)) {
            return false;
        }

        // Must have content array
        if (!isset($data['content']) || !is_array($data['content'])) {
            return false;
        }

        // Each content item must have type and props
        foreach ($data['content'] as $component) {
            if (!is_array($component) || !isset($component['type']) || !isset($component['props'])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get validation rules for Puck data in Laravel validation format
     */
    public function getPuckValidationRules(): array
    {
        return [
            'puck_data' => 'sometimes|array',
            'puck_data.content' => 'required_with:puck_data|array',
            'puck_data.content.*' => 'array',
            'puck_data.content.*.type' => 'required|string',
            'puck_data.content.*.props' => 'required|array',
            'puck_data.content.*.props.id' => 'required|string',
            'puck_data.root' => 'sometimes|array',
            'puck_data.zones' => 'sometimes|array',
            'puck_meta' => 'sometimes|array',
        ];
    }
}
