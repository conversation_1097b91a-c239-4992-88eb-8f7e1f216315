<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\PuckValidationService;
use App\Services\PuckContentTransformationService;
use Illuminate\Validation\ValidationException;

class PuckServicesTest extends TestCase
{
    private PuckValidationService $validationService;
    private PuckContentTransformationService $transformationService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->validationService = new PuckValidationService();
        $this->transformationService = new PuckContentTransformationService($this->validationService);
    }

    /** @test */
    public function validation_service_validates_correct_puck_data()
    {
        $validPuckData = [
            'content' => [
                [
                    'type' => 'Hero',
                    'props' => [
                        'id' => 'hero-123',
                        'title' => 'Test Hero',
                        'subtitle' => 'Test subtitle',
                    ]
                ]
            ],
            'root' => [
                'title' => 'Test Page',
            ]
        ];

        $result = $this->validationService->validatePuckData($validPuckData);
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('content', $result);
        $this->assertArrayHasKey('root', $result);
    }

    /** @test */
    public function validation_service_rejects_invalid_puck_data()
    {
        $invalidPuckData = [
            'content' => [
                [
                    'type' => 'Hero',
                    'props' => [
                        // Missing required 'id' field
                        'title' => 'Test Hero',
                    ]
                ]
            ]
        ];

        $this->expectException(ValidationException::class);
        $this->validationService->validatePuckData($invalidPuckData);
    }

    /** @test */
    public function validation_service_ensures_component_ids()
    {
        $puckDataWithoutIds = [
            'content' => [
                [
                    'type' => 'Hero',
                    'props' => [
                        'title' => 'Test Hero',
                        // No ID provided
                    ]
                ]
            ]
        ];

        $result = $this->validationService->ensureComponentIds($puckDataWithoutIds);
        
        $this->assertArrayHasKey('content', $result);
        $this->assertArrayHasKey('props', $result['content'][0]);
        $this->assertArrayHasKey('id', $result['content'][0]['props']);
        $this->assertNotEmpty($result['content'][0]['props']['id']);
    }

    /** @test */
    public function validation_service_validates_hero_component_props()
    {
        $heroProps = [
            'id' => 'hero-123',
            'title' => 'Test Hero',
            'subtitle' => 'Test subtitle',
            'textAlign' => 'center',
            'primaryButton' => [
                'text' => 'Click me',
                'href' => '/test',
                'variant' => 'primary',
            ]
        ];

        $reflection = new \ReflectionClass($this->validationService);
        $method = $reflection->getMethod('validateHeroProps');
        $method->setAccessible(true);

        $result = $method->invoke($this->validationService, $heroProps);
        
        $this->assertEquals($heroProps, $result);
    }

    /** @test */
    public function validation_service_detects_puck_data_format()
    {
        $validPuckData = [
            'content' => [
                [
                    'type' => 'Text',
                    'props' => [
                        'id' => 'text-123',
                        'content' => 'Hello',
                    ]
                ]
            ]
        ];

        $this->assertTrue($this->validationService->isPuckData($validPuckData));

        $invalidData = [
            'content' => 'not an array'
        ];

        $this->assertFalse($this->validationService->isPuckData($invalidData));
    }

    /** @test */
    public function transformation_service_converts_html_to_puck()
    {
        $html = '<h1>Welcome</h1><p>This is a paragraph</p>';
        $metadata = [
            'title' => 'Test Page',
            'meta_description' => 'Test description',
        ];

        $result = $this->transformationService->htmlToPuck($html, $metadata);
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('content', $result);
        $this->assertArrayHasKey('root', $result);
        $this->assertEquals('Test Page', $result['root']['title']);
        $this->assertEquals('Test description', $result['root']['metaDescription']);
        
        // Verify components have IDs
        foreach ($result['content'] as $component) {
            $this->assertArrayHasKey('props', $component);
            $this->assertArrayHasKey('id', $component['props']);
        }
    }

    /** @test */
    public function transformation_service_converts_puck_to_html()
    {
        $puckData = [
            'content' => [
                [
                    'type' => 'Hero',
                    'props' => [
                        'id' => 'hero-123',
                        'title' => 'Welcome',
                        'subtitle' => 'This is a test',
                        'textAlign' => 'center',
                        'minHeight' => '400px',
                    ]
                ],
                [
                    'type' => 'Text',
                    'props' => [
                        'id' => 'text-123',
                        'content' => '<p>This is a paragraph</p>',
                        'textAlign' => 'left',
                    ]
                ]
            ]
        ];

        $html = $this->transformationService->puckToHtml($puckData);
        
        $this->assertIsString($html);
        $this->assertStringContainsString('Welcome', $html);
        $this->assertStringContainsString('This is a test', $html);
        $this->assertStringContainsString('This is a paragraph', $html);
        $this->assertStringContainsString('text-align: center', $html);
    }

    /** @test */
    public function transformation_service_handles_empty_html()
    {
        $result = $this->transformationService->htmlToPuck('', []);
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('content', $result);
        $this->assertArrayHasKey('root', $result);
        $this->assertEmpty($result['content']);
    }

    /** @test */
    public function transformation_service_handles_invalid_puck_data()
    {
        $invalidPuckData = [
            'not_content' => 'invalid'
        ];

        $html = $this->transformationService->puckToHtml($invalidPuckData);
        
        $this->assertEquals('', $html);
    }

    /** @test */
    public function transformation_service_extracts_seo_data()
    {
        $puckData = [
            'content' => [
                [
                    'type' => 'Hero',
                    'props' => [
                        'id' => 'hero-123',
                        'title' => 'Page Title from Hero',
                    ]
                ]
            ],
            'root' => [
                'title' => 'Root Title',
                'metaDescription' => 'Root description',
                'metaKeywords' => 'test, keywords',
            ]
        ];

        $seoData = $this->transformationService->extractSeoData($puckData);
        
        $this->assertIsArray($seoData);
        $this->assertEquals('Root Title', $seoData['title']);
        $this->assertEquals('Root description', $seoData['meta_description']);
        $this->assertEquals('test, keywords', $seoData['meta_keywords']);
    }

    /** @test */
    public function transformation_service_falls_back_to_hero_title_for_seo()
    {
        $puckData = [
            'content' => [
                [
                    'type' => 'Hero',
                    'props' => [
                        'id' => 'hero-123',
                        'title' => 'Hero Title',
                    ]
                ]
            ],
            'root' => [
                // No title in root
                'metaDescription' => 'Description',
            ]
        ];

        $seoData = $this->transformationService->extractSeoData($puckData);
        
        $this->assertEquals('Hero Title', $seoData['title']);
    }

    /** @test */
    public function transformation_service_renders_hero_component_correctly()
    {
        $puckData = [
            'content' => [
                [
                    'type' => 'Hero',
                    'props' => [
                        'id' => 'hero-123',
                        'title' => 'Test Hero',
                        'subtitle' => 'Test Subtitle',
                        'textAlign' => 'center',
                        'minHeight' => '500px',
                        'backgroundImage' => '/test-image.jpg',
                        'primaryButton' => [
                            'text' => 'Click Me',
                            'href' => '/test',
                            'variant' => 'primary',
                        ]
                    ]
                ]
            ]
        ];

        $html = $this->transformationService->puckToHtml($puckData);
        
        $this->assertStringContainsString('Test Hero', $html);
        $this->assertStringContainsString('Test Subtitle', $html);
        $this->assertStringContainsString('text-align: center', $html);
        $this->assertStringContainsString('min-height: 500px', $html);
        $this->assertStringContainsString('background-image: url(/test-image.jpg)', $html);
        $this->assertStringContainsString('Click Me', $html);
        $this->assertStringContainsString('href="/test"', $html);
    }

    /** @test */
    public function transformation_service_renders_text_component_correctly()
    {
        $puckData = [
            'content' => [
                [
                    'type' => 'Text',
                    'props' => [
                        'id' => 'text-123',
                        'content' => '<p>Test content</p>',
                        'textAlign' => 'right',
                        'fontSize' => '18px',
                        'color' => '#333333',
                    ]
                ]
            ]
        ];

        $html = $this->transformationService->puckToHtml($puckData);
        
        $this->assertStringContainsString('Test content', $html);
        $this->assertStringContainsString('text-align: right', $html);
        $this->assertStringContainsString('font-size: 18px', $html);
        $this->assertStringContainsString('color: #333333', $html);
    }

    /** @test */
    public function transformation_service_renders_image_component_correctly()
    {
        $puckData = [
            'content' => [
                [
                    'type' => 'Image',
                    'props' => [
                        'id' => 'image-123',
                        'src' => '/test-image.jpg',
                        'alt' => 'Test Image',
                        'width' => '300px',
                        'height' => '200px',
                        'objectFit' => 'contain',
                    ]
                ]
            ]
        ];

        $html = $this->transformationService->puckToHtml($puckData);
        
        $this->assertStringContainsString('src="/test-image.jpg"', $html);
        $this->assertStringContainsString('alt="Test Image"', $html);
        $this->assertStringContainsString('width: 300px', $html);
        $this->assertStringContainsString('height: 200px', $html);
        $this->assertStringContainsString('object-fit: contain', $html);
    }

    /** @test */
    public function transformation_service_handles_unknown_components()
    {
        $puckData = [
            'content' => [
                [
                    'type' => 'UnknownComponent',
                    'props' => [
                        'id' => 'unknown-123',
                        'someProperty' => 'value',
                    ]
                ]
            ]
        ];

        $html = $this->transformationService->puckToHtml($puckData);
        
        $this->assertStringContainsString('<!-- Unknown component: UnknownComponent -->', $html);
    }

    /** @test */
    public function transformation_service_parses_hero_like_html_elements()
    {
        $html = '<section class="hero" style="background-image: url(/bg.jpg); min-height: 400px;">
                    <h1>Hero Title</h1>
                    <h2>Hero Subtitle</h2>
                 </section>';

        $result = $this->transformationService->htmlToPuck($html, []);

        $this->assertNotEmpty($result['content']);

        $heroComponent = null;
        foreach ($result['content'] as $component) {
            if ($component['type'] === 'Hero') {
                $heroComponent = $component;
                break;
            }
        }

        $this->assertNotNull($heroComponent);
        $this->assertEquals('Hero Title', $heroComponent['props']['title']);
        $this->assertEquals('Hero Subtitle', $heroComponent['props']['subtitle']);
    }

    /** @test */
    public function validation_service_provides_correct_validation_rules()
    {
        $rules = $this->validationService->getPuckValidationRules();

        $this->assertIsArray($rules);
        $this->assertArrayHasKey('puck_data', $rules);
        $this->assertArrayHasKey('puck_data.content', $rules);
        $this->assertArrayHasKey('puck_data.content.*.props.id', $rules);

        $this->assertStringContainsString('required', $rules['puck_data.content.*.props.id']);
    }
}
