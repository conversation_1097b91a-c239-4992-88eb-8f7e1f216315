<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('printing_products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('printing_category_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('slug')->unique();
            $table->decimal('base_price', 10, 2);
            $table->string('image')->nullable();
            $table->json('specifications')->nullable(); // Size, material, finish options
            $table->json('options')->nullable(); // Pricing tiers, customization options
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->integer('min_quantity')->default(1);
            $table->integer('max_quantity')->nullable();
            $table->integer('production_time_days')->default(3);
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->timestamps();

            $table->index(['printing_category_id', 'is_active']);
            $table->index(['is_active', 'sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('printing_products');
    }
};
