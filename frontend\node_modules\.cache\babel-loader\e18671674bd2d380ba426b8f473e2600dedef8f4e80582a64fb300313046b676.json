{"ast": null, "code": "import React from 'react';\n\n// Comprehensive Puck configuration with all essential components\nexport const puckConfig = {\n  components: {\n    // Layout Components\n    Hero: {\n      label: 'Hero Section',\n      defaultProps: {\n        title: 'Hero Title',\n        subtitle: 'Hero subtitle text',\n        textAlign: 'center',\n        minHeight: '400px',\n        backgroundType: 'gradient',\n        backgroundColor: '#667eea',\n        backgroundGradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        textColor: '#ffffff'\n      },\n      fields: {\n        title: {\n          type: 'text',\n          label: 'Title'\n        },\n        subtitle: {\n          type: 'textarea',\n          label: 'Subtitle'\n        },\n        textAlign: {\n          type: 'select',\n          label: 'Text Alignment',\n          options: [{\n            label: 'Left',\n            value: 'left'\n          }, {\n            label: 'Center',\n            value: 'center'\n          }, {\n            label: 'Right',\n            value: 'right'\n          }]\n        },\n        minHeight: {\n          type: 'text',\n          label: 'Min Height (px)'\n        },\n        backgroundType: {\n          type: 'select',\n          label: 'Background Type',\n          options: [{\n            label: 'Solid Color',\n            value: 'solid'\n          }, {\n            label: 'Gradient',\n            value: 'gradient'\n          }, {\n            label: 'Image',\n            value: 'image'\n          }]\n        },\n        backgroundColor: {\n          type: 'text',\n          label: 'Background Color'\n        },\n        backgroundGradient: {\n          type: 'text',\n          label: 'Background Gradient'\n        },\n        backgroundImage: {\n          type: 'text',\n          label: 'Background Image URL'\n        },\n        textColor: {\n          type: 'text',\n          label: 'Text Color'\n        }\n      },\n      render: ({\n        title,\n        subtitle,\n        textAlign,\n        minHeight,\n        backgroundType,\n        backgroundColor,\n        backgroundGradient,\n        backgroundImage,\n        textColor\n      }) => {\n        let backgroundStyle = {};\n        switch (backgroundType) {\n          case 'solid':\n            backgroundStyle = {\n              backgroundColor: backgroundColor || '#667eea'\n            };\n            break;\n          case 'gradient':\n            backgroundStyle = {\n              background: backgroundGradient || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n            };\n            break;\n          case 'image':\n            backgroundStyle = {\n              backgroundImage: `url(${backgroundImage})`,\n              backgroundSize: 'cover',\n              backgroundPosition: 'center',\n              backgroundRepeat: 'no-repeat'\n            };\n            break;\n          default:\n            backgroundStyle = {\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n            };\n        }\n        return /*#__PURE__*/React.createElement('section', {\n          style: {\n            minHeight: minHeight || '400px',\n            display: 'flex',\n            flexDirection: 'column',\n            justifyContent: 'center',\n            alignItems: textAlign === 'center' ? 'center' : textAlign === 'right' ? 'flex-end' : 'flex-start',\n            textAlign: textAlign || 'center',\n            padding: '4rem 2rem',\n            color: textColor || 'white',\n            position: 'relative',\n            ...backgroundStyle\n          }\n        }, /*#__PURE__*/React.createElement('div', {\n          style: {\n            position: 'relative',\n            zIndex: 1,\n            maxWidth: '800px'\n          }\n        }, [/*#__PURE__*/React.createElement('h1', {\n          key: 'title',\n          style: {\n            fontSize: 'clamp(2rem, 5vw, 3.5rem)',\n            marginBottom: '1rem',\n            fontWeight: 'bold',\n            lineHeight: 1.2\n          }\n        }, title || 'Hero Title'), /*#__PURE__*/React.createElement('p', {\n          key: 'subtitle',\n          style: {\n            fontSize: 'clamp(1rem, 2.5vw, 1.25rem)',\n            opacity: 0.9,\n            lineHeight: 1.6,\n            marginBottom: '2rem'\n          }\n        }, subtitle || 'Hero subtitle text')]));\n      }\n    },\n    // Text Component\n    Text: {\n      label: 'Text Block',\n      defaultProps: {\n        content: '<p>Add your text content here...</p>',\n        textAlign: 'left',\n        fontSize: '16px',\n        color: '#333333',\n        lineHeight: '1.6',\n        marginTop: '0px',\n        marginBottom: '20px'\n      },\n      fields: {\n        content: {\n          type: 'textarea',\n          label: 'Content (HTML)'\n        },\n        textAlign: {\n          type: 'select',\n          label: 'Text Alignment',\n          options: [{\n            label: 'Left',\n            value: 'left'\n          }, {\n            label: 'Center',\n            value: 'center'\n          }, {\n            label: 'Right',\n            value: 'right'\n          }, {\n            label: 'Justify',\n            value: 'justify'\n          }]\n        },\n        fontSize: {\n          type: 'text',\n          label: 'Font Size'\n        },\n        color: {\n          type: 'text',\n          label: 'Text Color'\n        },\n        lineHeight: {\n          type: 'text',\n          label: 'Line Height'\n        },\n        marginTop: {\n          type: 'text',\n          label: 'Margin Top'\n        },\n        marginBottom: {\n          type: 'text',\n          label: 'Margin Bottom'\n        }\n      },\n      render: ({\n        content,\n        textAlign,\n        fontSize,\n        color,\n        lineHeight,\n        marginTop,\n        marginBottom\n      }) => {\n        return /*#__PURE__*/React.createElement('div', {\n          style: {\n            padding: '0 1rem',\n            marginTop: marginTop || '0px',\n            marginBottom: marginBottom || '20px'\n          }\n        }, /*#__PURE__*/React.createElement('div', {\n          style: {\n            textAlign: textAlign || 'left',\n            fontSize: fontSize || '16px',\n            color: color || '#333333',\n            lineHeight: lineHeight || '1.6'\n          },\n          dangerouslySetInnerHTML: {\n            __html: content || '<p>Add your text content here...</p>'\n          }\n        }));\n      }\n    },\n    // Heading Component\n    Heading: {\n      label: 'Heading',\n      defaultProps: {\n        text: 'Heading Text',\n        level: 'h2',\n        textAlign: 'left',\n        color: '#333333',\n        marginTop: '0px',\n        marginBottom: '20px'\n      },\n      fields: {\n        text: {\n          type: 'text',\n          label: 'Heading Text'\n        },\n        level: {\n          type: 'select',\n          label: 'Heading Level',\n          options: [{\n            label: 'H1',\n            value: 'h1'\n          }, {\n            label: 'H2',\n            value: 'h2'\n          }, {\n            label: 'H3',\n            value: 'h3'\n          }, {\n            label: 'H4',\n            value: 'h4'\n          }, {\n            label: 'H5',\n            value: 'h5'\n          }, {\n            label: 'H6',\n            value: 'h6'\n          }]\n        },\n        textAlign: {\n          type: 'select',\n          label: 'Text Alignment',\n          options: [{\n            label: 'Left',\n            value: 'left'\n          }, {\n            label: 'Center',\n            value: 'center'\n          }, {\n            label: 'Right',\n            value: 'right'\n          }]\n        },\n        color: {\n          type: 'text',\n          label: 'Text Color'\n        },\n        marginTop: {\n          type: 'text',\n          label: 'Margin Top'\n        },\n        marginBottom: {\n          type: 'text',\n          label: 'Margin Bottom'\n        }\n      },\n      render: ({\n        text,\n        level,\n        textAlign,\n        color,\n        marginTop,\n        marginBottom\n      }) => {\n        const HeadingTag = level || 'h2';\n        const fontSizes = {\n          h1: '2.5rem',\n          h2: '2rem',\n          h3: '1.75rem',\n          h4: '1.5rem',\n          h5: '1.25rem',\n          h6: '1rem'\n        };\n        const fontSize = fontSizes[level] || '2rem';\n        return /*#__PURE__*/React.createElement(HeadingTag, {\n          style: {\n            textAlign: textAlign || 'left',\n            color: color || '#333333',\n            fontSize,\n            fontWeight: 'bold',\n            marginTop: marginTop || '0px',\n            marginBottom: marginBottom || '20px',\n            padding: '0 1rem',\n            lineHeight: 1.2\n          }\n        }, text || 'Heading Text');\n      }\n    },\n    // Layout Components\n    Grid: {\n      label: 'Grid Layout',\n      defaultProps: {\n        columns: '3',\n        gap: '20px',\n        alignItems: 'stretch',\n        justifyItems: 'stretch',\n        padding: '20px',\n        backgroundColor: 'transparent',\n        minHeight: 'auto'\n      },\n      fields: {\n        columns: {\n          type: 'select',\n          label: 'Columns',\n          options: [{\n            label: '1 Column',\n            value: '1'\n          }, {\n            label: '2 Columns',\n            value: '2'\n          }, {\n            label: '3 Columns',\n            value: '3'\n          }, {\n            label: '4 Columns',\n            value: '4'\n          }, {\n            label: '5 Columns',\n            value: '5'\n          }, {\n            label: '6 Columns',\n            value: '6'\n          }]\n        },\n        gap: {\n          type: 'text',\n          label: 'Gap (px)'\n        },\n        alignItems: {\n          type: 'select',\n          label: 'Align Items',\n          options: [{\n            label: 'Stretch',\n            value: 'stretch'\n          }, {\n            label: 'Start',\n            value: 'start'\n          }, {\n            label: 'Center',\n            value: 'center'\n          }, {\n            label: 'End',\n            value: 'end'\n          }]\n        },\n        justifyItems: {\n          type: 'select',\n          label: 'Justify Items',\n          options: [{\n            label: 'Stretch',\n            value: 'stretch'\n          }, {\n            label: 'Start',\n            value: 'start'\n          }, {\n            label: 'Center',\n            value: 'center'\n          }, {\n            label: 'End',\n            value: 'end'\n          }]\n        },\n        padding: {\n          type: 'text',\n          label: 'Padding'\n        },\n        backgroundColor: {\n          type: 'text',\n          label: 'Background Color'\n        },\n        minHeight: {\n          type: 'text',\n          label: 'Min Height'\n        }\n      },\n      render: ({\n        columns,\n        gap,\n        alignItems,\n        justifyItems,\n        padding,\n        backgroundColor,\n        minHeight\n      }) => {\n        return /*#__PURE__*/React.createElement('div', {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: `repeat(${columns || 3}, 1fr)`,\n            gap: gap || '20px',\n            alignItems: alignItems || 'stretch',\n            justifyItems: justifyItems || 'stretch',\n            padding: padding || '20px',\n            backgroundColor: backgroundColor || 'transparent',\n            minHeight: minHeight || 'auto',\n            width: '100%'\n          }\n        }, /*#__PURE__*/React.createElement('div', {\n          style: {\n            gridColumn: '1 / -1',\n            textAlign: 'center',\n            padding: '40px 20px',\n            border: '2px dashed #e2e8f0',\n            borderRadius: '8px',\n            color: '#64748b',\n            fontSize: '14px'\n          }\n        }, `Grid with ${columns || 3} columns - Drop components here`));\n      }\n    },\n    Flex: {\n      label: 'Flex Container',\n      defaultProps: {\n        direction: 'row',\n        justifyContent: 'flex-start',\n        alignItems: 'stretch',\n        flexWrap: 'nowrap',\n        gap: '16px',\n        padding: '20px',\n        backgroundColor: 'transparent',\n        minHeight: 'auto'\n      },\n      fields: {\n        direction: {\n          type: 'select',\n          label: 'Direction',\n          options: [{\n            label: 'Row',\n            value: 'row'\n          }, {\n            label: 'Column',\n            value: 'column'\n          }, {\n            label: 'Row Reverse',\n            value: 'row-reverse'\n          }, {\n            label: 'Column Reverse',\n            value: 'column-reverse'\n          }]\n        },\n        justifyContent: {\n          type: 'select',\n          label: 'Justify Content',\n          options: [{\n            label: 'Flex Start',\n            value: 'flex-start'\n          }, {\n            label: 'Center',\n            value: 'center'\n          }, {\n            label: 'Flex End',\n            value: 'flex-end'\n          }, {\n            label: 'Space Between',\n            value: 'space-between'\n          }, {\n            label: 'Space Around',\n            value: 'space-around'\n          }, {\n            label: 'Space Evenly',\n            value: 'space-evenly'\n          }]\n        },\n        alignItems: {\n          type: 'select',\n          label: 'Align Items',\n          options: [{\n            label: 'Stretch',\n            value: 'stretch'\n          }, {\n            label: 'Flex Start',\n            value: 'flex-start'\n          }, {\n            label: 'Center',\n            value: 'center'\n          }, {\n            label: 'Flex End',\n            value: 'flex-end'\n          }, {\n            label: 'Baseline',\n            value: 'baseline'\n          }]\n        },\n        flexWrap: {\n          type: 'select',\n          label: 'Flex Wrap',\n          options: [{\n            label: 'No Wrap',\n            value: 'nowrap'\n          }, {\n            label: 'Wrap',\n            value: 'wrap'\n          }, {\n            label: 'Wrap Reverse',\n            value: 'wrap-reverse'\n          }]\n        },\n        gap: {\n          type: 'text',\n          label: 'Gap'\n        },\n        padding: {\n          type: 'text',\n          label: 'Padding'\n        },\n        backgroundColor: {\n          type: 'text',\n          label: 'Background Color'\n        },\n        minHeight: {\n          type: 'text',\n          label: 'Min Height'\n        }\n      },\n      render: ({\n        direction,\n        justifyContent,\n        alignItems,\n        flexWrap,\n        gap,\n        padding,\n        backgroundColor,\n        minHeight\n      }) => {\n        return /*#__PURE__*/React.createElement('div', {\n          style: {\n            display: 'flex',\n            flexDirection: direction || 'row',\n            justifyContent: justifyContent || 'flex-start',\n            alignItems: alignItems || 'stretch',\n            flexWrap: flexWrap || 'nowrap',\n            gap: gap || '16px',\n            padding: padding || '20px',\n            backgroundColor: backgroundColor || 'transparent',\n            minHeight: minHeight || 'auto',\n            width: '100%'\n          }\n        }, /*#__PURE__*/React.createElement('div', {\n          style: {\n            flex: '1',\n            textAlign: 'center',\n            padding: '40px 20px',\n            border: '2px dashed #e2e8f0',\n            borderRadius: '8px',\n            color: '#64748b',\n            fontSize: '14px'\n          }\n        }, 'Flex Container - Drop components here'));\n      }\n    },\n    Space: {\n      label: 'Spacer',\n      defaultProps: {\n        height: '40px',\n        width: '100%',\n        backgroundColor: 'transparent'\n      },\n      fields: {\n        height: {\n          type: 'text',\n          label: 'Height'\n        },\n        width: {\n          type: 'text',\n          label: 'Width'\n        },\n        backgroundColor: {\n          type: 'text',\n          label: 'Background Color'\n        }\n      },\n      render: ({\n        height,\n        width,\n        backgroundColor\n      }) => {\n        return /*#__PURE__*/React.createElement('div', {\n          style: {\n            height: height || '40px',\n            width: width || '100%',\n            backgroundColor: backgroundColor || 'transparent',\n            display: 'block'\n          }\n        });\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["React", "puckConfig", "components", "Hero", "label", "defaultProps", "title", "subtitle", "textAlign", "minHeight", "backgroundType", "backgroundColor", "backgroundGradient", "textColor", "fields", "type", "options", "value", "backgroundImage", "render", "backgroundStyle", "background", "backgroundSize", "backgroundPosition", "backgroundRepeat", "createElement", "style", "display", "flexDirection", "justifyContent", "alignItems", "padding", "color", "position", "zIndex", "max<PERSON><PERSON><PERSON>", "key", "fontSize", "marginBottom", "fontWeight", "lineHeight", "opacity", "Text", "content", "marginTop", "dangerouslySetInnerHTML", "__html", "Heading", "text", "level", "HeadingTag", "fontSizes", "h1", "h2", "h3", "h4", "h5", "h6", "Grid", "columns", "gap", "justifyItems", "gridTemplateColumns", "width", "gridColumn", "border", "borderRadius", "Flex", "direction", "flexWrap", "flex", "Space", "height"], "sources": ["C:/laragon/www/frontend/src/components/puck/puckConfig.ts"], "sourcesContent": ["import React from 'react';\n\n// Comprehensive Puck configuration with all essential components\nexport const puckConfig = {\n  components: {\n    // Layout Components\n    Hero: {\n      label: 'Hero Section',\n      defaultProps: {\n        title: 'Hero Title',\n        subtitle: 'Hero subtitle text',\n        textAlign: 'center',\n        minHeight: '400px',\n        backgroundType: 'gradient',\n        backgroundColor: '#667eea',\n        backgroundGradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        textColor: '#ffffff',\n      },\n      fields: {\n        title: { type: 'text' as const, label: 'Title' },\n        subtitle: { type: 'textarea' as const, label: 'Subtitle' },\n        textAlign: {\n          type: 'select' as const,\n          label: 'Text Alignment',\n          options: [\n            { label: 'Left', value: 'left' },\n            { label: 'Center', value: 'center' },\n            { label: 'Right', value: 'right' },\n          ],\n        },\n        minHeight: { type: 'text' as const, label: 'Min Height (px)' },\n        backgroundType: {\n          type: 'select' as const,\n          label: 'Background Type',\n          options: [\n            { label: 'Solid Color', value: 'solid' },\n            { label: 'Gradient', value: 'gradient' },\n            { label: 'Image', value: 'image' },\n          ],\n        },\n        backgroundColor: { type: 'text' as const, label: 'Background Color' },\n        backgroundGradient: { type: 'text' as const, label: 'Background Gradient' },\n        backgroundImage: { type: 'text' as const, label: 'Background Image URL' },\n        textColor: { type: 'text' as const, label: 'Text Color' },\n      },\n      render: ({ title, subtitle, textAlign, minHeight, backgroundType, backgroundColor, backgroundGradient, backgroundImage, textColor }: any) => {\n        let backgroundStyle = {};\n\n        switch (backgroundType) {\n          case 'solid':\n            backgroundStyle = { backgroundColor: backgroundColor || '#667eea' };\n            break;\n          case 'gradient':\n            backgroundStyle = { background: backgroundGradient || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' };\n            break;\n          case 'image':\n            backgroundStyle = {\n              backgroundImage: `url(${backgroundImage})`,\n              backgroundSize: 'cover',\n              backgroundPosition: 'center',\n              backgroundRepeat: 'no-repeat'\n            };\n            break;\n          default:\n            backgroundStyle = { background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' };\n        }\n\n        return React.createElement('section', {\n          style: {\n            minHeight: minHeight || '400px',\n            display: 'flex',\n            flexDirection: 'column',\n            justifyContent: 'center',\n            alignItems: textAlign === 'center' ? 'center' : textAlign === 'right' ? 'flex-end' : 'flex-start',\n            textAlign: textAlign || 'center',\n            padding: '4rem 2rem',\n            color: textColor || 'white',\n            position: 'relative',\n            ...backgroundStyle,\n          }\n        }, React.createElement('div', {\n          style: { position: 'relative', zIndex: 1, maxWidth: '800px' }\n        }, [\n          React.createElement('h1', {\n            key: 'title',\n            style: {\n              fontSize: 'clamp(2rem, 5vw, 3.5rem)',\n              marginBottom: '1rem',\n              fontWeight: 'bold',\n              lineHeight: 1.2,\n            }\n          }, title || 'Hero Title'),\n          React.createElement('p', {\n            key: 'subtitle',\n            style: {\n              fontSize: 'clamp(1rem, 2.5vw, 1.25rem)',\n              opacity: 0.9,\n              lineHeight: 1.6,\n              marginBottom: '2rem',\n            }\n          }, subtitle || 'Hero subtitle text')\n        ]));\n      },\n    },\n\n    // Text Component\n    Text: {\n      label: 'Text Block',\n      defaultProps: {\n        content: '<p>Add your text content here...</p>',\n        textAlign: 'left',\n        fontSize: '16px',\n        color: '#333333',\n        lineHeight: '1.6',\n        marginTop: '0px',\n        marginBottom: '20px',\n      },\n      fields: {\n        content: { type: 'textarea' as const, label: 'Content (HTML)' },\n        textAlign: {\n          type: 'select' as const,\n          label: 'Text Alignment',\n          options: [\n            { label: 'Left', value: 'left' },\n            { label: 'Center', value: 'center' },\n            { label: 'Right', value: 'right' },\n            { label: 'Justify', value: 'justify' },\n          ],\n        },\n        fontSize: { type: 'text' as const, label: 'Font Size' },\n        color: { type: 'text' as const, label: 'Text Color' },\n        lineHeight: { type: 'text' as const, label: 'Line Height' },\n        marginTop: { type: 'text' as const, label: 'Margin Top' },\n        marginBottom: { type: 'text' as const, label: 'Margin Bottom' },\n      },\n      render: ({ content, textAlign, fontSize, color, lineHeight, marginTop, marginBottom }: any) => {\n        return React.createElement('div', {\n          style: {\n            padding: '0 1rem',\n            marginTop: marginTop || '0px',\n            marginBottom: marginBottom || '20px',\n          }\n        }, React.createElement('div', {\n          style: {\n            textAlign: textAlign || 'left',\n            fontSize: fontSize || '16px',\n            color: color || '#333333',\n            lineHeight: lineHeight || '1.6',\n          },\n          dangerouslySetInnerHTML: { __html: content || '<p>Add your text content here...</p>' }\n        }));\n      },\n    },\n\n    // Heading Component\n    Heading: {\n      label: 'Heading',\n      defaultProps: {\n        text: 'Heading Text',\n        level: 'h2',\n        textAlign: 'left',\n        color: '#333333',\n        marginTop: '0px',\n        marginBottom: '20px',\n      },\n      fields: {\n        text: { type: 'text' as const, label: 'Heading Text' },\n        level: {\n          type: 'select' as const,\n          label: 'Heading Level',\n          options: [\n            { label: 'H1', value: 'h1' },\n            { label: 'H2', value: 'h2' },\n            { label: 'H3', value: 'h3' },\n            { label: 'H4', value: 'h4' },\n            { label: 'H5', value: 'h5' },\n            { label: 'H6', value: 'h6' },\n          ],\n        },\n        textAlign: {\n          type: 'select' as const,\n          label: 'Text Alignment',\n          options: [\n            { label: 'Left', value: 'left' },\n            { label: 'Center', value: 'center' },\n            { label: 'Right', value: 'right' },\n          ],\n        },\n        color: { type: 'text' as const, label: 'Text Color' },\n        marginTop: { type: 'text' as const, label: 'Margin Top' },\n        marginBottom: { type: 'text' as const, label: 'Margin Bottom' },\n      },\n      render: ({ text, level, textAlign, color, marginTop, marginBottom }: any) => {\n        const HeadingTag = level || 'h2';\n        const fontSizes: { [key: string]: string } = {\n          h1: '2.5rem',\n          h2: '2rem',\n          h3: '1.75rem',\n          h4: '1.5rem',\n          h5: '1.25rem',\n          h6: '1rem',\n        };\n        const fontSize = fontSizes[level] || '2rem';\n\n        return React.createElement(\n          HeadingTag,\n          {\n            style: {\n              textAlign: textAlign || 'left',\n              color: color || '#333333',\n              fontSize,\n              fontWeight: 'bold',\n              marginTop: marginTop || '0px',\n              marginBottom: marginBottom || '20px',\n              padding: '0 1rem',\n              lineHeight: 1.2,\n            }\n          },\n          text || 'Heading Text'\n        );\n      },\n    },\n\n    // Layout Components\n    Grid: {\n      label: 'Grid Layout',\n      defaultProps: {\n        columns: '3',\n        gap: '20px',\n        alignItems: 'stretch',\n        justifyItems: 'stretch',\n        padding: '20px',\n        backgroundColor: 'transparent',\n        minHeight: 'auto',\n      },\n      fields: {\n        columns: {\n          type: 'select' as const,\n          label: 'Columns',\n          options: [\n            { label: '1 Column', value: '1' },\n            { label: '2 Columns', value: '2' },\n            { label: '3 Columns', value: '3' },\n            { label: '4 Columns', value: '4' },\n            { label: '5 Columns', value: '5' },\n            { label: '6 Columns', value: '6' },\n          ],\n        },\n        gap: { type: 'text' as const, label: 'Gap (px)' },\n        alignItems: {\n          type: 'select' as const,\n          label: 'Align Items',\n          options: [\n            { label: 'Stretch', value: 'stretch' },\n            { label: 'Start', value: 'start' },\n            { label: 'Center', value: 'center' },\n            { label: 'End', value: 'end' },\n          ],\n        },\n        justifyItems: {\n          type: 'select' as const,\n          label: 'Justify Items',\n          options: [\n            { label: 'Stretch', value: 'stretch' },\n            { label: 'Start', value: 'start' },\n            { label: 'Center', value: 'center' },\n            { label: 'End', value: 'end' },\n          ],\n        },\n        padding: { type: 'text' as const, label: 'Padding' },\n        backgroundColor: { type: 'text' as const, label: 'Background Color' },\n        minHeight: { type: 'text' as const, label: 'Min Height' },\n      },\n      render: ({ columns, gap, alignItems, justifyItems, padding, backgroundColor, minHeight }: any) => {\n        return React.createElement('div', {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: `repeat(${columns || 3}, 1fr)`,\n            gap: gap || '20px',\n            alignItems: alignItems || 'stretch',\n            justifyItems: justifyItems || 'stretch',\n            padding: padding || '20px',\n            backgroundColor: backgroundColor || 'transparent',\n            minHeight: minHeight || 'auto',\n            width: '100%',\n          }\n        }, React.createElement('div', {\n          style: {\n            gridColumn: '1 / -1',\n            textAlign: 'center',\n            padding: '40px 20px',\n            border: '2px dashed #e2e8f0',\n            borderRadius: '8px',\n            color: '#64748b',\n            fontSize: '14px',\n          }\n        }, `Grid with ${columns || 3} columns - Drop components here`));\n      },\n    },\n\n    Flex: {\n      label: 'Flex Container',\n      defaultProps: {\n        direction: 'row',\n        justifyContent: 'flex-start',\n        alignItems: 'stretch',\n        flexWrap: 'nowrap',\n        gap: '16px',\n        padding: '20px',\n        backgroundColor: 'transparent',\n        minHeight: 'auto',\n      },\n      fields: {\n        direction: {\n          type: 'select' as const,\n          label: 'Direction',\n          options: [\n            { label: 'Row', value: 'row' },\n            { label: 'Column', value: 'column' },\n            { label: 'Row Reverse', value: 'row-reverse' },\n            { label: 'Column Reverse', value: 'column-reverse' },\n          ],\n        },\n        justifyContent: {\n          type: 'select' as const,\n          label: 'Justify Content',\n          options: [\n            { label: 'Flex Start', value: 'flex-start' },\n            { label: 'Center', value: 'center' },\n            { label: 'Flex End', value: 'flex-end' },\n            { label: 'Space Between', value: 'space-between' },\n            { label: 'Space Around', value: 'space-around' },\n            { label: 'Space Evenly', value: 'space-evenly' },\n          ],\n        },\n        alignItems: {\n          type: 'select' as const,\n          label: 'Align Items',\n          options: [\n            { label: 'Stretch', value: 'stretch' },\n            { label: 'Flex Start', value: 'flex-start' },\n            { label: 'Center', value: 'center' },\n            { label: 'Flex End', value: 'flex-end' },\n            { label: 'Baseline', value: 'baseline' },\n          ],\n        },\n        flexWrap: {\n          type: 'select' as const,\n          label: 'Flex Wrap',\n          options: [\n            { label: 'No Wrap', value: 'nowrap' },\n            { label: 'Wrap', value: 'wrap' },\n            { label: 'Wrap Reverse', value: 'wrap-reverse' },\n          ],\n        },\n        gap: { type: 'text' as const, label: 'Gap' },\n        padding: { type: 'text' as const, label: 'Padding' },\n        backgroundColor: { type: 'text' as const, label: 'Background Color' },\n        minHeight: { type: 'text' as const, label: 'Min Height' },\n      },\n      render: ({ direction, justifyContent, alignItems, flexWrap, gap, padding, backgroundColor, minHeight }: any) => {\n        return React.createElement('div', {\n          style: {\n            display: 'flex',\n            flexDirection: direction || 'row',\n            justifyContent: justifyContent || 'flex-start',\n            alignItems: alignItems || 'stretch',\n            flexWrap: flexWrap || 'nowrap',\n            gap: gap || '16px',\n            padding: padding || '20px',\n            backgroundColor: backgroundColor || 'transparent',\n            minHeight: minHeight || 'auto',\n            width: '100%',\n          }\n        }, React.createElement('div', {\n          style: {\n            flex: '1',\n            textAlign: 'center',\n            padding: '40px 20px',\n            border: '2px dashed #e2e8f0',\n            borderRadius: '8px',\n            color: '#64748b',\n            fontSize: '14px',\n          }\n        }, 'Flex Container - Drop components here'));\n      },\n    },\n\n    Space: {\n      label: 'Spacer',\n      defaultProps: {\n        height: '40px',\n        width: '100%',\n        backgroundColor: 'transparent',\n      },\n      fields: {\n        height: { type: 'text' as const, label: 'Height' },\n        width: { type: 'text' as const, label: 'Width' },\n        backgroundColor: { type: 'text' as const, label: 'Background Color' },\n      },\n      render: ({ height, width, backgroundColor }: any) => {\n        return React.createElement('div', {\n          style: {\n            height: height || '40px',\n            width: width || '100%',\n            backgroundColor: backgroundColor || 'transparent',\n            display: 'block',\n          }\n        });\n      },\n    },\n  },\n};\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,OAAO,MAAMC,UAAU,GAAG;EACxBC,UAAU,EAAE;IACV;IACAC,IAAI,EAAE;MACJC,KAAK,EAAE,cAAc;MACrBC,YAAY,EAAE;QACZC,KAAK,EAAE,YAAY;QACnBC,QAAQ,EAAE,oBAAoB;QAC9BC,SAAS,EAAE,QAAQ;QACnBC,SAAS,EAAE,OAAO;QAClBC,cAAc,EAAE,UAAU;QAC1BC,eAAe,EAAE,SAAS;QAC1BC,kBAAkB,EAAE,mDAAmD;QACvEC,SAAS,EAAE;MACb,CAAC;MACDC,MAAM,EAAE;QACNR,KAAK,EAAE;UAAES,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAQ,CAAC;QAChDG,QAAQ,EAAE;UAAEQ,IAAI,EAAE,UAAmB;UAAEX,KAAK,EAAE;QAAW,CAAC;QAC1DI,SAAS,EAAE;UACTO,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,gBAAgB;UACvBY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,MAAM;YAAEa,KAAK,EAAE;UAAO,CAAC,EAChC;YAAEb,KAAK,EAAE,QAAQ;YAAEa,KAAK,EAAE;UAAS,CAAC,EACpC;YAAEb,KAAK,EAAE,OAAO;YAAEa,KAAK,EAAE;UAAQ,CAAC;QAEtC,CAAC;QACDR,SAAS,EAAE;UAAEM,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAkB,CAAC;QAC9DM,cAAc,EAAE;UACdK,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,iBAAiB;UACxBY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,aAAa;YAAEa,KAAK,EAAE;UAAQ,CAAC,EACxC;YAAEb,KAAK,EAAE,UAAU;YAAEa,KAAK,EAAE;UAAW,CAAC,EACxC;YAAEb,KAAK,EAAE,OAAO;YAAEa,KAAK,EAAE;UAAQ,CAAC;QAEtC,CAAC;QACDN,eAAe,EAAE;UAAEI,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAmB,CAAC;QACrEQ,kBAAkB,EAAE;UAAEG,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAsB,CAAC;QAC3Ec,eAAe,EAAE;UAAEH,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAuB,CAAC;QACzES,SAAS,EAAE;UAAEE,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAa;MAC1D,CAAC;MACDe,MAAM,EAAEA,CAAC;QAAEb,KAAK;QAAEC,QAAQ;QAAEC,SAAS;QAAEC,SAAS;QAAEC,cAAc;QAAEC,eAAe;QAAEC,kBAAkB;QAAEM,eAAe;QAAEL;MAAe,CAAC,KAAK;QAC3I,IAAIO,eAAe,GAAG,CAAC,CAAC;QAExB,QAAQV,cAAc;UACpB,KAAK,OAAO;YACVU,eAAe,GAAG;cAAET,eAAe,EAAEA,eAAe,IAAI;YAAU,CAAC;YACnE;UACF,KAAK,UAAU;YACbS,eAAe,GAAG;cAAEC,UAAU,EAAET,kBAAkB,IAAI;YAAoD,CAAC;YAC3G;UACF,KAAK,OAAO;YACVQ,eAAe,GAAG;cAChBF,eAAe,EAAE,OAAOA,eAAe,GAAG;cAC1CI,cAAc,EAAE,OAAO;cACvBC,kBAAkB,EAAE,QAAQ;cAC5BC,gBAAgB,EAAE;YACpB,CAAC;YACD;UACF;YACEJ,eAAe,GAAG;cAAEC,UAAU,EAAE;YAAoD,CAAC;QACzF;QAEA,oBAAOrB,KAAK,CAACyB,aAAa,CAAC,SAAS,EAAE;UACpCC,KAAK,EAAE;YACLjB,SAAS,EAAEA,SAAS,IAAI,OAAO;YAC/BkB,OAAO,EAAE,MAAM;YACfC,aAAa,EAAE,QAAQ;YACvBC,cAAc,EAAE,QAAQ;YACxBC,UAAU,EAAEtB,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAGA,SAAS,KAAK,OAAO,GAAG,UAAU,GAAG,YAAY;YACjGA,SAAS,EAAEA,SAAS,IAAI,QAAQ;YAChCuB,OAAO,EAAE,WAAW;YACpBC,KAAK,EAAEnB,SAAS,IAAI,OAAO;YAC3BoB,QAAQ,EAAE,UAAU;YACpB,GAAGb;UACL;QACF,CAAC,eAAEpB,KAAK,CAACyB,aAAa,CAAC,KAAK,EAAE;UAC5BC,KAAK,EAAE;YAAEO,QAAQ,EAAE,UAAU;YAAEC,MAAM,EAAE,CAAC;YAAEC,QAAQ,EAAE;UAAQ;QAC9D,CAAC,EAAE,cACDnC,KAAK,CAACyB,aAAa,CAAC,IAAI,EAAE;UACxBW,GAAG,EAAE,OAAO;UACZV,KAAK,EAAE;YACLW,QAAQ,EAAE,0BAA0B;YACpCC,YAAY,EAAE,MAAM;YACpBC,UAAU,EAAE,MAAM;YAClBC,UAAU,EAAE;UACd;QACF,CAAC,EAAElC,KAAK,IAAI,YAAY,CAAC,eACzBN,KAAK,CAACyB,aAAa,CAAC,GAAG,EAAE;UACvBW,GAAG,EAAE,UAAU;UACfV,KAAK,EAAE;YACLW,QAAQ,EAAE,6BAA6B;YACvCI,OAAO,EAAE,GAAG;YACZD,UAAU,EAAE,GAAG;YACfF,YAAY,EAAE;UAChB;QACF,CAAC,EAAE/B,QAAQ,IAAI,oBAAoB,CAAC,CACrC,CAAC,CAAC;MACL;IACF,CAAC;IAED;IACAmC,IAAI,EAAE;MACJtC,KAAK,EAAE,YAAY;MACnBC,YAAY,EAAE;QACZsC,OAAO,EAAE,sCAAsC;QAC/CnC,SAAS,EAAE,MAAM;QACjB6B,QAAQ,EAAE,MAAM;QAChBL,KAAK,EAAE,SAAS;QAChBQ,UAAU,EAAE,KAAK;QACjBI,SAAS,EAAE,KAAK;QAChBN,YAAY,EAAE;MAChB,CAAC;MACDxB,MAAM,EAAE;QACN6B,OAAO,EAAE;UAAE5B,IAAI,EAAE,UAAmB;UAAEX,KAAK,EAAE;QAAiB,CAAC;QAC/DI,SAAS,EAAE;UACTO,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,gBAAgB;UACvBY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,MAAM;YAAEa,KAAK,EAAE;UAAO,CAAC,EAChC;YAAEb,KAAK,EAAE,QAAQ;YAAEa,KAAK,EAAE;UAAS,CAAC,EACpC;YAAEb,KAAK,EAAE,OAAO;YAAEa,KAAK,EAAE;UAAQ,CAAC,EAClC;YAAEb,KAAK,EAAE,SAAS;YAAEa,KAAK,EAAE;UAAU,CAAC;QAE1C,CAAC;QACDoB,QAAQ,EAAE;UAAEtB,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAY,CAAC;QACvD4B,KAAK,EAAE;UAAEjB,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAa,CAAC;QACrDoC,UAAU,EAAE;UAAEzB,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAc,CAAC;QAC3DwC,SAAS,EAAE;UAAE7B,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAa,CAAC;QACzDkC,YAAY,EAAE;UAAEvB,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAgB;MAChE,CAAC;MACDe,MAAM,EAAEA,CAAC;QAAEwB,OAAO;QAAEnC,SAAS;QAAE6B,QAAQ;QAAEL,KAAK;QAAEQ,UAAU;QAAEI,SAAS;QAAEN;MAAkB,CAAC,KAAK;QAC7F,oBAAOtC,KAAK,CAACyB,aAAa,CAAC,KAAK,EAAE;UAChCC,KAAK,EAAE;YACLK,OAAO,EAAE,QAAQ;YACjBa,SAAS,EAAEA,SAAS,IAAI,KAAK;YAC7BN,YAAY,EAAEA,YAAY,IAAI;UAChC;QACF,CAAC,eAAEtC,KAAK,CAACyB,aAAa,CAAC,KAAK,EAAE;UAC5BC,KAAK,EAAE;YACLlB,SAAS,EAAEA,SAAS,IAAI,MAAM;YAC9B6B,QAAQ,EAAEA,QAAQ,IAAI,MAAM;YAC5BL,KAAK,EAAEA,KAAK,IAAI,SAAS;YACzBQ,UAAU,EAAEA,UAAU,IAAI;UAC5B,CAAC;UACDK,uBAAuB,EAAE;YAAEC,MAAM,EAAEH,OAAO,IAAI;UAAuC;QACvF,CAAC,CAAC,CAAC;MACL;IACF,CAAC;IAED;IACAI,OAAO,EAAE;MACP3C,KAAK,EAAE,SAAS;MAChBC,YAAY,EAAE;QACZ2C,IAAI,EAAE,cAAc;QACpBC,KAAK,EAAE,IAAI;QACXzC,SAAS,EAAE,MAAM;QACjBwB,KAAK,EAAE,SAAS;QAChBY,SAAS,EAAE,KAAK;QAChBN,YAAY,EAAE;MAChB,CAAC;MACDxB,MAAM,EAAE;QACNkC,IAAI,EAAE;UAAEjC,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAe,CAAC;QACtD6C,KAAK,EAAE;UACLlC,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,eAAe;UACtBY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,IAAI;YAAEa,KAAK,EAAE;UAAK,CAAC,EAC5B;YAAEb,KAAK,EAAE,IAAI;YAAEa,KAAK,EAAE;UAAK,CAAC,EAC5B;YAAEb,KAAK,EAAE,IAAI;YAAEa,KAAK,EAAE;UAAK,CAAC,EAC5B;YAAEb,KAAK,EAAE,IAAI;YAAEa,KAAK,EAAE;UAAK,CAAC,EAC5B;YAAEb,KAAK,EAAE,IAAI;YAAEa,KAAK,EAAE;UAAK,CAAC,EAC5B;YAAEb,KAAK,EAAE,IAAI;YAAEa,KAAK,EAAE;UAAK,CAAC;QAEhC,CAAC;QACDT,SAAS,EAAE;UACTO,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,gBAAgB;UACvBY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,MAAM;YAAEa,KAAK,EAAE;UAAO,CAAC,EAChC;YAAEb,KAAK,EAAE,QAAQ;YAAEa,KAAK,EAAE;UAAS,CAAC,EACpC;YAAEb,KAAK,EAAE,OAAO;YAAEa,KAAK,EAAE;UAAQ,CAAC;QAEtC,CAAC;QACDe,KAAK,EAAE;UAAEjB,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAa,CAAC;QACrDwC,SAAS,EAAE;UAAE7B,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAa,CAAC;QACzDkC,YAAY,EAAE;UAAEvB,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAgB;MAChE,CAAC;MACDe,MAAM,EAAEA,CAAC;QAAE6B,IAAI;QAAEC,KAAK;QAAEzC,SAAS;QAAEwB,KAAK;QAAEY,SAAS;QAAEN;MAAkB,CAAC,KAAK;QAC3E,MAAMY,UAAU,GAAGD,KAAK,IAAI,IAAI;QAChC,MAAME,SAAoC,GAAG;UAC3CC,EAAE,EAAE,QAAQ;UACZC,EAAE,EAAE,MAAM;UACVC,EAAE,EAAE,SAAS;UACbC,EAAE,EAAE,QAAQ;UACZC,EAAE,EAAE,SAAS;UACbC,EAAE,EAAE;QACN,CAAC;QACD,MAAMpB,QAAQ,GAAGc,SAAS,CAACF,KAAK,CAAC,IAAI,MAAM;QAE3C,oBAAOjD,KAAK,CAACyB,aAAa,CACxByB,UAAU,EACV;UACExB,KAAK,EAAE;YACLlB,SAAS,EAAEA,SAAS,IAAI,MAAM;YAC9BwB,KAAK,EAAEA,KAAK,IAAI,SAAS;YACzBK,QAAQ;YACRE,UAAU,EAAE,MAAM;YAClBK,SAAS,EAAEA,SAAS,IAAI,KAAK;YAC7BN,YAAY,EAAEA,YAAY,IAAI,MAAM;YACpCP,OAAO,EAAE,QAAQ;YACjBS,UAAU,EAAE;UACd;QACF,CAAC,EACDQ,IAAI,IAAI,cACV,CAAC;MACH;IACF,CAAC;IAED;IACAU,IAAI,EAAE;MACJtD,KAAK,EAAE,aAAa;MACpBC,YAAY,EAAE;QACZsD,OAAO,EAAE,GAAG;QACZC,GAAG,EAAE,MAAM;QACX9B,UAAU,EAAE,SAAS;QACrB+B,YAAY,EAAE,SAAS;QACvB9B,OAAO,EAAE,MAAM;QACfpB,eAAe,EAAE,aAAa;QAC9BF,SAAS,EAAE;MACb,CAAC;MACDK,MAAM,EAAE;QACN6C,OAAO,EAAE;UACP5C,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,SAAS;UAChBY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,UAAU;YAAEa,KAAK,EAAE;UAAI,CAAC,EACjC;YAAEb,KAAK,EAAE,WAAW;YAAEa,KAAK,EAAE;UAAI,CAAC,EAClC;YAAEb,KAAK,EAAE,WAAW;YAAEa,KAAK,EAAE;UAAI,CAAC,EAClC;YAAEb,KAAK,EAAE,WAAW;YAAEa,KAAK,EAAE;UAAI,CAAC,EAClC;YAAEb,KAAK,EAAE,WAAW;YAAEa,KAAK,EAAE;UAAI,CAAC,EAClC;YAAEb,KAAK,EAAE,WAAW;YAAEa,KAAK,EAAE;UAAI,CAAC;QAEtC,CAAC;QACD2C,GAAG,EAAE;UAAE7C,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAW,CAAC;QACjD0B,UAAU,EAAE;UACVf,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,aAAa;UACpBY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,SAAS;YAAEa,KAAK,EAAE;UAAU,CAAC,EACtC;YAAEb,KAAK,EAAE,OAAO;YAAEa,KAAK,EAAE;UAAQ,CAAC,EAClC;YAAEb,KAAK,EAAE,QAAQ;YAAEa,KAAK,EAAE;UAAS,CAAC,EACpC;YAAEb,KAAK,EAAE,KAAK;YAAEa,KAAK,EAAE;UAAM,CAAC;QAElC,CAAC;QACD4C,YAAY,EAAE;UACZ9C,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,eAAe;UACtBY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,SAAS;YAAEa,KAAK,EAAE;UAAU,CAAC,EACtC;YAAEb,KAAK,EAAE,OAAO;YAAEa,KAAK,EAAE;UAAQ,CAAC,EAClC;YAAEb,KAAK,EAAE,QAAQ;YAAEa,KAAK,EAAE;UAAS,CAAC,EACpC;YAAEb,KAAK,EAAE,KAAK;YAAEa,KAAK,EAAE;UAAM,CAAC;QAElC,CAAC;QACDc,OAAO,EAAE;UAAEhB,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAU,CAAC;QACpDO,eAAe,EAAE;UAAEI,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAmB,CAAC;QACrEK,SAAS,EAAE;UAAEM,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAa;MAC1D,CAAC;MACDe,MAAM,EAAEA,CAAC;QAAEwC,OAAO;QAAEC,GAAG;QAAE9B,UAAU;QAAE+B,YAAY;QAAE9B,OAAO;QAAEpB,eAAe;QAAEF;MAAe,CAAC,KAAK;QAChG,oBAAOT,KAAK,CAACyB,aAAa,CAAC,KAAK,EAAE;UAChCC,KAAK,EAAE;YACLC,OAAO,EAAE,MAAM;YACfmC,mBAAmB,EAAE,UAAUH,OAAO,IAAI,CAAC,QAAQ;YACnDC,GAAG,EAAEA,GAAG,IAAI,MAAM;YAClB9B,UAAU,EAAEA,UAAU,IAAI,SAAS;YACnC+B,YAAY,EAAEA,YAAY,IAAI,SAAS;YACvC9B,OAAO,EAAEA,OAAO,IAAI,MAAM;YAC1BpB,eAAe,EAAEA,eAAe,IAAI,aAAa;YACjDF,SAAS,EAAEA,SAAS,IAAI,MAAM;YAC9BsD,KAAK,EAAE;UACT;QACF,CAAC,eAAE/D,KAAK,CAACyB,aAAa,CAAC,KAAK,EAAE;UAC5BC,KAAK,EAAE;YACLsC,UAAU,EAAE,QAAQ;YACpBxD,SAAS,EAAE,QAAQ;YACnBuB,OAAO,EAAE,WAAW;YACpBkC,MAAM,EAAE,oBAAoB;YAC5BC,YAAY,EAAE,KAAK;YACnBlC,KAAK,EAAE,SAAS;YAChBK,QAAQ,EAAE;UACZ;QACF,CAAC,EAAE,aAAasB,OAAO,IAAI,CAAC,iCAAiC,CAAC,CAAC;MACjE;IACF,CAAC;IAEDQ,IAAI,EAAE;MACJ/D,KAAK,EAAE,gBAAgB;MACvBC,YAAY,EAAE;QACZ+D,SAAS,EAAE,KAAK;QAChBvC,cAAc,EAAE,YAAY;QAC5BC,UAAU,EAAE,SAAS;QACrBuC,QAAQ,EAAE,QAAQ;QAClBT,GAAG,EAAE,MAAM;QACX7B,OAAO,EAAE,MAAM;QACfpB,eAAe,EAAE,aAAa;QAC9BF,SAAS,EAAE;MACb,CAAC;MACDK,MAAM,EAAE;QACNsD,SAAS,EAAE;UACTrD,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,WAAW;UAClBY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,KAAK;YAAEa,KAAK,EAAE;UAAM,CAAC,EAC9B;YAAEb,KAAK,EAAE,QAAQ;YAAEa,KAAK,EAAE;UAAS,CAAC,EACpC;YAAEb,KAAK,EAAE,aAAa;YAAEa,KAAK,EAAE;UAAc,CAAC,EAC9C;YAAEb,KAAK,EAAE,gBAAgB;YAAEa,KAAK,EAAE;UAAiB,CAAC;QAExD,CAAC;QACDY,cAAc,EAAE;UACdd,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,iBAAiB;UACxBY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,YAAY;YAAEa,KAAK,EAAE;UAAa,CAAC,EAC5C;YAAEb,KAAK,EAAE,QAAQ;YAAEa,KAAK,EAAE;UAAS,CAAC,EACpC;YAAEb,KAAK,EAAE,UAAU;YAAEa,KAAK,EAAE;UAAW,CAAC,EACxC;YAAEb,KAAK,EAAE,eAAe;YAAEa,KAAK,EAAE;UAAgB,CAAC,EAClD;YAAEb,KAAK,EAAE,cAAc;YAAEa,KAAK,EAAE;UAAe,CAAC,EAChD;YAAEb,KAAK,EAAE,cAAc;YAAEa,KAAK,EAAE;UAAe,CAAC;QAEpD,CAAC;QACDa,UAAU,EAAE;UACVf,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,aAAa;UACpBY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,SAAS;YAAEa,KAAK,EAAE;UAAU,CAAC,EACtC;YAAEb,KAAK,EAAE,YAAY;YAAEa,KAAK,EAAE;UAAa,CAAC,EAC5C;YAAEb,KAAK,EAAE,QAAQ;YAAEa,KAAK,EAAE;UAAS,CAAC,EACpC;YAAEb,KAAK,EAAE,UAAU;YAAEa,KAAK,EAAE;UAAW,CAAC,EACxC;YAAEb,KAAK,EAAE,UAAU;YAAEa,KAAK,EAAE;UAAW,CAAC;QAE5C,CAAC;QACDoD,QAAQ,EAAE;UACRtD,IAAI,EAAE,QAAiB;UACvBX,KAAK,EAAE,WAAW;UAClBY,OAAO,EAAE,CACP;YAAEZ,KAAK,EAAE,SAAS;YAAEa,KAAK,EAAE;UAAS,CAAC,EACrC;YAAEb,KAAK,EAAE,MAAM;YAAEa,KAAK,EAAE;UAAO,CAAC,EAChC;YAAEb,KAAK,EAAE,cAAc;YAAEa,KAAK,EAAE;UAAe,CAAC;QAEpD,CAAC;QACD2C,GAAG,EAAE;UAAE7C,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAM,CAAC;QAC5C2B,OAAO,EAAE;UAAEhB,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAU,CAAC;QACpDO,eAAe,EAAE;UAAEI,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAmB,CAAC;QACrEK,SAAS,EAAE;UAAEM,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAa;MAC1D,CAAC;MACDe,MAAM,EAAEA,CAAC;QAAEiD,SAAS;QAAEvC,cAAc;QAAEC,UAAU;QAAEuC,QAAQ;QAAET,GAAG;QAAE7B,OAAO;QAAEpB,eAAe;QAAEF;MAAe,CAAC,KAAK;QAC9G,oBAAOT,KAAK,CAACyB,aAAa,CAAC,KAAK,EAAE;UAChCC,KAAK,EAAE;YACLC,OAAO,EAAE,MAAM;YACfC,aAAa,EAAEwC,SAAS,IAAI,KAAK;YACjCvC,cAAc,EAAEA,cAAc,IAAI,YAAY;YAC9CC,UAAU,EAAEA,UAAU,IAAI,SAAS;YACnCuC,QAAQ,EAAEA,QAAQ,IAAI,QAAQ;YAC9BT,GAAG,EAAEA,GAAG,IAAI,MAAM;YAClB7B,OAAO,EAAEA,OAAO,IAAI,MAAM;YAC1BpB,eAAe,EAAEA,eAAe,IAAI,aAAa;YACjDF,SAAS,EAAEA,SAAS,IAAI,MAAM;YAC9BsD,KAAK,EAAE;UACT;QACF,CAAC,eAAE/D,KAAK,CAACyB,aAAa,CAAC,KAAK,EAAE;UAC5BC,KAAK,EAAE;YACL4C,IAAI,EAAE,GAAG;YACT9D,SAAS,EAAE,QAAQ;YACnBuB,OAAO,EAAE,WAAW;YACpBkC,MAAM,EAAE,oBAAoB;YAC5BC,YAAY,EAAE,KAAK;YACnBlC,KAAK,EAAE,SAAS;YAChBK,QAAQ,EAAE;UACZ;QACF,CAAC,EAAE,uCAAuC,CAAC,CAAC;MAC9C;IACF,CAAC;IAEDkC,KAAK,EAAE;MACLnE,KAAK,EAAE,QAAQ;MACfC,YAAY,EAAE;QACZmE,MAAM,EAAE,MAAM;QACdT,KAAK,EAAE,MAAM;QACbpD,eAAe,EAAE;MACnB,CAAC;MACDG,MAAM,EAAE;QACN0D,MAAM,EAAE;UAAEzD,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAS,CAAC;QAClD2D,KAAK,EAAE;UAAEhD,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAQ,CAAC;QAChDO,eAAe,EAAE;UAAEI,IAAI,EAAE,MAAe;UAAEX,KAAK,EAAE;QAAmB;MACtE,CAAC;MACDe,MAAM,EAAEA,CAAC;QAAEqD,MAAM;QAAET,KAAK;QAAEpD;MAAqB,CAAC,KAAK;QACnD,oBAAOX,KAAK,CAACyB,aAAa,CAAC,KAAK,EAAE;UAChCC,KAAK,EAAE;YACL8C,MAAM,EAAEA,MAAM,IAAI,MAAM;YACxBT,KAAK,EAAEA,KAAK,IAAI,MAAM;YACtBpD,eAAe,EAAEA,eAAe,IAAI,aAAa;YACjDgB,OAAO,EAAE;UACX;QACF,CAAC,CAAC;MACJ;IACF;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}