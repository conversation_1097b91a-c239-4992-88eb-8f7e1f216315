<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;

abstract class BaseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Override in child classes if needed
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(Validator $validator): void
    {
        $errors = $validator->errors();

        throw new HttpResponseException(
            response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $errors->toArray(),
                'error_count' => $errors->count(),
            ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY)
        );
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'required' => 'The :attribute field is required.',
            'email' => 'The :attribute must be a valid email address.',
            'min' => 'The :attribute must be at least :min characters.',
            'max' => 'The :attribute may not be greater than :max characters.',
            'unique' => 'The :attribute has already been taken.',
            'confirmed' => 'The :attribute confirmation does not match.',
            'numeric' => 'The :attribute must be a number.',
            'integer' => 'The :attribute must be an integer.',
            'boolean' => 'The :attribute field must be true or false.',
            'date' => 'The :attribute is not a valid date.',
            'before' => 'The :attribute must be a date before :date.',
            'after' => 'The :attribute must be a date after :date.',
            'in' => 'The selected :attribute is invalid.',
            'file' => 'The :attribute must be a file.',
            'image' => 'The :attribute must be an image.',
            'mimes' => 'The :attribute must be a file of type: :values.',
            'regex' => 'The :attribute format is invalid.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'email' => 'email address',
            'password' => 'password',
            'password_confirmation' => 'password confirmation',
            'current_password' => 'current password',
            'name' => 'name',
            'phone' => 'phone number',
            'date_of_birth' => 'date of birth',
            'bio' => 'biography',
            'avatar' => 'profile picture',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Sanitize input data
        $this->sanitizeInput();
    }

    /**
     * Sanitize input data to prevent XSS and other attacks
     */
    protected function sanitizeInput(): void
    {
        $input = $this->all();
        
        foreach ($input as $key => $value) {
            if (is_string($value)) {
                // Remove null bytes
                $value = str_replace("\0", '', $value);
                
                // Trim whitespace
                $value = trim($value);
                
                // For specific fields that should allow HTML, skip sanitization
                if (!in_array($key, $this->getAllowedHtmlFields())) {
                    // Basic XSS prevention - strip tags except for allowed ones
                    $value = strip_tags($value, $this->getAllowedTags());
                }
                
                $input[$key] = $value;
            }
        }
        
        $this->replace($input);
    }

    /**
     * Get fields that are allowed to contain HTML
     */
    protected function getAllowedHtmlFields(): array
    {
        return [
            'content',
            'body_html',
            'description',
            'bio',
            'notes',
            'special_instructions',
        ];
    }

    /**
     * Get allowed HTML tags for sanitization
     */
    protected function getAllowedTags(): string
    {
        return '<p><br><strong><em><u><a><ul><ol><li><h1><h2><h3><h4><h5><h6>';
    }

    /**
     * Common validation rules
     */
    protected function getCommonRules(): array
    {
        return [
            'email_rule' => 'required|email|max:255',
            'password_rule' => 'required|string|min:8|confirmed',
            'name_rule' => 'required|string|max:255',
            'phone_rule' => 'nullable|string|max:20|regex:/^[\+]?[0-9\s\-\(\)]+$/',
            'date_rule' => 'nullable|date',
            'currency_rule' => 'required|numeric|min:0|max:999999.99',
            'text_rule' => 'nullable|string|max:1000',
            'long_text_rule' => 'nullable|string|max:5000',
            'slug_rule' => 'required|string|max:255|regex:/^[a-z0-9\-]+$/',
            'boolean_rule' => 'required|boolean',
            'id_rule' => 'required|integer|min:1',
            'optional_id_rule' => 'nullable|integer|min:1',
        ];
    }
}
