<?php

namespace App\Http\Requests\Auth;

use App\Http\Requests\BaseRequest;

class LoginRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'email' => 'required|email|max:255',
            'password' => 'required|string|min:1', // Don't enforce min length on login
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return array_merge(parent::messages(), [
            'email.required' => 'Please enter your email address.',
            'password.required' => 'Please enter your password.',
        ]);
    }

    /**
     * Fields that are allowed to contain HTML (none for login)
     */
    protected function getAllowedHtmlFields(): array
    {
        return [];
    }
}
