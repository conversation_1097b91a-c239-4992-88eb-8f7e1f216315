<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
// CreditPackage model removed - no longer needed
use App\Models\WalletTransaction;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CreditController extends Controller
{
    /**
     * Get user's wallet balance
     */
    public function balance(Request $request): JsonResponse
    {
        $user = $request->user();

        return response()->json([
            'balance' => $user->wallet_balance,
            'wallet_balance' => $user->wallet_balance, // For backward compatibility
            'credit_balance' => $user->wallet_balance, // For backward compatibility
            'formatted_balance' => $user->getFormattedWalletBalance(),
            'currency' => 'MYR',
            'user_id' => $user->id,
        ]);
    }

    /**
     * Get available credit packages
     * @deprecated Credit packages have been removed from the system
     */
    public function packages(): JsonResponse
    {
        // Credit packages are no longer available
        // Return empty array for backward compatibility
        return response()->json([
            'packages' => [],
            'message' => 'Credit packages are no longer available. Please use direct wallet top-up methods.',
        ]);
    }

    /**
     * Get user's wallet transaction history
     */
    public function transactions(Request $request): JsonResponse
    {
        $user = $request->user();

        $transactions = $user->walletTransactions()
            ->with('package')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        $transactions->getCollection()->transform(function ($transaction) {
            return [
                'id' => $transaction->id,
                'type' => $transaction->type,
                'amount' => $transaction->amount,
                'credit_amount' => $transaction->amount, // For backward compatibility
                'formatted_amount' => $transaction->getFormattedAmount(),
                'amount_paid' => $transaction->amount_paid,
                'formatted_amount_paid' => $transaction->getFormattedAmountPaid(),
                'payment_method' => $transaction->payment_method,
                'payment_status' => $transaction->payment_status,
                'description' => $transaction->description,
                'package_name' => $transaction->package?->name,
                'is_credit' => $transaction->isCredit(),
                'is_debit' => $transaction->isDebit(),
                'withdrawal_method' => $transaction->withdrawal_method,
                'withdrawal_reference' => $transaction->withdrawal_reference,
                'processed_at' => $transaction->processed_at?->format('Y-m-d H:i:s'),
                'withdrawal_processed_at' => $transaction->withdrawal_processed_at?->format('Y-m-d H:i:s'),
                'created_at' => $transaction->created_at->format('Y-m-d H:i:s'),
            ];
        });

        return response()->json([
            'transactions' => $transactions,
        ]);
    }

    /**
     * Get wallet statistics for user
     */
    public function statistics(Request $request): JsonResponse
    {
        $user = $request->user();

        // Calculate totals for each transaction type
        $totalTopUp = $user->walletTransactions()
            ->where('type', 'top_up')
            ->where('payment_status', 'completed')
            ->sum('amount') ?? 0;

        $totalPayments = abs($user->walletTransactions()
            ->where('type', 'payment')
            ->where('payment_status', 'completed')
            ->sum('amount') ?? 0);

        $totalWithdrawals = abs($user->walletTransactions()
            ->where('type', 'withdrawal')
            ->where('payment_status', 'completed')
            ->sum('amount') ?? 0);

        $totalRefunded = $user->walletTransactions()
            ->where('type', 'refund')
            ->where('payment_status', 'completed')
            ->sum('amount') ?? 0;

        $totalBonus = $user->walletTransactions()
            ->where('type', 'bonus')
            ->where('payment_status', 'completed')
            ->sum('amount') ?? 0;

        $totalAdjustments = $user->walletTransactions()
            ->where('type', 'adjustment')
            ->where('payment_status', 'completed')
            ->sum('amount') ?? 0;

        // Calculate total spent (money used from wallet for payments)
        $totalSpent = abs($user->walletTransactions()
            ->where('type', 'payment')
            ->where('payment_status', 'completed')
            ->sum('amount') ?? 0);

        // Calculate expected balance for verification
        $expectedBalance = $totalTopUp + $totalRefunded + $totalBonus + $totalAdjustments - $totalPayments - $totalWithdrawals;

        $recentTransactions = $user->walletTransactions()
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($transaction) {
                return [
                    'id' => $transaction->id,
                    'type' => $transaction->type,
                    'amount' => $transaction->amount,
                    'credit_amount' => $transaction->amount, // For backward compatibility
                    'formatted_amount' => $transaction->getFormattedAmount(),
                    'amount_paid' => $transaction->amount_paid,
                    'description' => $transaction->description,
                    'created_at' => $transaction->created_at->format('Y-m-d H:i:s'),
                ];
            });

        return response()->json([
            'current_balance' => $user->wallet_balance,
            'wallet_balance' => $user->wallet_balance,
            'credit_balance' => $user->wallet_balance, // For backward compatibility
            'formatted_balance' => $user->getFormattedWalletBalance(),
            'currency' => 'MYR',
            'total_top_up' => $totalTopUp,
            'total_purchased' => $totalTopUp, // For backward compatibility
            'total_payments' => $totalPayments,
            'total_used' => $totalPayments, // For backward compatibility
            'total_withdrawals' => $totalWithdrawals,
            'total_refunded' => $totalRefunded,
            'total_bonus' => $totalBonus,
            'total_adjustments' => $totalAdjustments,
            'total_spent' => $totalSpent,
            'expected_balance' => $expectedBalance,
            'balance_matches' => abs($user->wallet_balance - $expectedBalance) < 0.01,
            'recent_transactions' => $recentTransactions,
        ]);
    }
}
