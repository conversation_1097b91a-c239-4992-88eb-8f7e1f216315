<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PrintingOrder;
use App\Models\OrderFile;
use App\Models\OrderFileHistory;
use App\Models\FileUploadSetting;
use App\Services\FileValidationService;
use App\Services\FileCompressionService;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class FileUploadController extends Controller
{
    protected FileValidationService $validationService;
    protected FileCompressionService $compressionService;

    public function __construct(FileValidationService $validationService, FileCompressionService $compressionService)
    {
        $this->validationService = $validationService;
        $this->compressionService = $compressionService;
    }

    /**
     * Upload files for an order
     */
    public function upload(Request $request, $orderId)
    {
        // Get dynamic validation rules from settings
        $maxFiles = FileUploadSetting::get('max_files_per_order', 10);
        $maxFileSizeMB = FileUploadSetting::get('max_file_size_mb', 50);
        $allowedTypes = FileUploadSetting::getTyped('allowed_file_types', ['pdf', 'png', 'jpg', 'jpeg']);

        // Ensure allowed_file_types is always an array
        if (!is_array($allowedTypes)) {
            if (is_string($allowedTypes)) {
                $allowedTypes = json_decode($allowedTypes, true) ?: ['pdf', 'png', 'jpg', 'jpeg'];
            } else {
                $allowedTypes = ['pdf', 'png', 'jpg', 'jpeg'];
            }
        }

        $request->validate([
            'files' => "required|array|min:1|max:{$maxFiles}",
            'files.*' => "required|file|max:" . ($maxFileSizeMB * 1024), // Convert MB to KB
            'file_type' => 'required|in:artwork,reference,proof',
        ]);

        $order = PrintingOrder::where('user_id', $request->user()->id)
            ->findOrFail($orderId);

        $files = $request->file('files');

        // Validate total upload size
        $totalSizeValidation = $this->validationService->validateTotalUploadSize($files, $orderId);
        if (!$totalSizeValidation['valid']) {
            return response()->json([
                'success' => false,
                'message' => $totalSizeValidation['message']
            ], 400);
        }

        // Validate file count
        $fileCountValidation = $this->validationService->validateFileCount($files, $orderId);
        if (!$fileCountValidation['valid']) {
            return response()->json([
                'success' => false,
                'message' => $fileCountValidation['message']
            ], 400);
        }

        $uploadedFiles = [];
        $warnings = [];

        foreach ($files as $file) {
            try {
                // Validate individual file
                $validation = $this->validationService->validateFile($file);
                if (!$validation['valid']) {
                    return response()->json([
                        'success' => false,
                        'message' => 'File validation failed: ' . implode(', ', $validation['errors'])
                    ], 400);
                }

                // Collect warnings
                if (!empty($validation['warnings'])) {
                    $warnings = array_merge($warnings, $validation['warnings']);
                }

                // Generate unique filename
                $fileName = Str::uuid() . '.' . $file->getClientOriginalExtension();
                $filePath = "order-files/{$orderId}/" . $fileName;

                // Store file
                $storedPath = $file->storeAs('order-files/' . $orderId, $fileName, 'public');

                // Compress file if needed
                $compressionResult = $this->compressionService->compressIfNeeded($file, $storedPath);
                $finalPath = $compressionResult['path'];

                // Get file dimensions and DPI for images
                $dimensions = null;
                $dpi = null;

                if (in_array($file->getMimeType(), ['image/jpeg', 'image/png'])) {
                    $imageInfo = $this->getImageInfo($file);
                    $dimensions = $imageInfo['dimensions'];
                    $dpi = $imageInfo['dpi'];
                }

                // Create file record
                $orderFile = OrderFile::create([
                    'printing_order_id' => $order->id,
                    'original_name' => $file->getClientOriginalName(),
                    'file_name' => $fileName,
                    'file_path' => $finalPath,
                    'file_size' => $compressionResult['compressed_size'],
                    'mime_type' => $file->getMimeType(),
                    'file_type' => $request->file_type,
                    'dimensions' => $dimensions,
                    'dpi' => $dpi,
                    'uploaded_by' => $request->user()->id,
                ]);

                // Add compression info to the response
                if ($compressionResult['compressed']) {
                    $orderFile->compression_stats = $this->compressionService->getCompressionStats(
                        $compressionResult['original_size'],
                        $compressionResult['compressed_size']
                    );
                }

                $uploadedFiles[] = $orderFile;

            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to upload file: ' . $e->getMessage()
                ], 500);
            }
        }

        $response = [
            'success' => true,
            'message' => 'Files uploaded successfully',
            'data' => $uploadedFiles
        ];

        // Include warnings if any
        if (!empty($warnings)) {
            $response['warnings'] = array_unique($warnings);
        }

        return response()->json($response);
    }

    /**
     * Get file upload settings for frontend validation
     */
    public function getUploadSettings()
    {
        $allowedFileTypes = FileUploadSetting::getTyped('allowed_file_types', ['pdf', 'png', 'jpg', 'jpeg']);

        // Ensure allowed_file_types is always an array
        if (!is_array($allowedFileTypes)) {
            $allowedFileTypes = ['pdf', 'png', 'jpg', 'jpeg'];
        }

        $settings = [
            'allowed_file_types' => $allowedFileTypes,
            'max_file_size_mb' => FileUploadSetting::get('max_file_size_mb', 50),
            'max_total_upload_size_mb' => FileUploadSetting::get('max_total_upload_size_mb', 200),
            'max_files_per_order' => FileUploadSetting::get('max_files_per_order', 10),
            'min_dpi_requirement' => FileUploadSetting::get('min_dpi_requirement', 300),
            'dpi_warning_threshold' => FileUploadSetting::get('dpi_warning_threshold', 150),
            'enable_dpi_validation' => FileUploadSetting::get('enable_dpi_validation', true),
            'min_width_px' => FileUploadSetting::get('min_width_px', 100),
            'min_height_px' => FileUploadSetting::get('min_height_px', 100),
            'max_width_px' => FileUploadSetting::get('max_width_px', 10000),
            'max_height_px' => FileUploadSetting::get('max_height_px', 10000),
            'enable_dimension_validation' => FileUploadSetting::get('enable_dimension_validation', true),
        ];

        return response()->json([
            'success' => true,
            'data' => $settings
        ]);
    }

    /**
     * Get files for an order
     */
    public function getFiles(Request $request, $orderId)
    {
        $order = PrintingOrder::where('user_id', $request->user()->id)
            ->findOrFail($orderId);

        $files = OrderFile::where('printing_order_id', $order->id)
            ->with('uploader:id,name')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $files
        ]);
    }

    /**
     * Delete a file
     */
    public function deleteFile(Request $request, $orderId, $fileId)
    {
        $order = PrintingOrder::where('user_id', $request->user()->id)
            ->findOrFail($orderId);

        $file = OrderFile::where('printing_order_id', $order->id)
            ->where('uploaded_by', $request->user()->id)
            ->findOrFail($fileId);

        // Delete from storage
        if (Storage::disk('public')->exists($file->file_path)) {
            Storage::disk('public')->delete($file->file_path);
        }

        // Delete record
        $file->delete();

        return response()->json([
            'success' => true,
            'message' => 'File deleted successfully'
        ]);
    }

    /**
     * Download a file (Admin only)
     */
    public function downloadFile(Request $request, $orderId, $fileId)
    {
        // Check if user is authenticated and is admin
        $user = auth()->user();
        if (!$user || !$user->isAdmin()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Admin access required.'
                ], 403);
            }
            abort(403, 'Unauthorized. Admin access required.');
        }

        // Find the order (no user restriction for admin)
        $order = PrintingOrder::findOrFail($orderId);

        // Find the file
        $file = OrderFile::where('printing_order_id', $order->id)
            ->findOrFail($fileId);

        // Check if file exists in storage
        if (!Storage::disk('public')->exists($file->file_path)) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'File not found in storage'
                ], 404);
            }
            abort(404, 'File not found in storage');
        }

        // Get the full path to the file
        $filePath = Storage::disk('public')->path($file->file_path);

        // Return file download response
        return response()->download($filePath, $file->original_name, [
            'Content-Type' => $file->mime_type,
        ]);
    }

    /**
     * Download all files for an order as ZIP (Admin only)
     */
    public function downloadAllFiles(Request $request, $orderId)
    {
        // Check if user is authenticated and is admin
        $user = auth()->user();
        if (!$user || !$user->isAdmin()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Admin access required.'
                ], 403);
            }
            abort(403, 'Unauthorized. Admin access required.');
        }

        // Check if ZIP extension is available
        if (!extension_loaded('zip')) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'ZIP extension is not available. Please download files individually or contact system administrator.'
                ], 500);
            }
            abort(500, 'ZIP extension is not available. Please download files individually or contact system administrator.');
        }

        // Find the order (no user restriction for admin)
        $order = PrintingOrder::with('files')->findOrFail($orderId);

        if ($order->files->isEmpty()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'No files found for this order'
                ], 404);
            }
            abort(404, 'No files found for this order');
        }

        // Create a temporary ZIP file
        $zipFileName = "order_{$order->order_number}_files.zip";
        $zipPath = storage_path('app/temp/' . $zipFileName);

        // Ensure temp directory exists
        if (!file_exists(dirname($zipPath))) {
            mkdir(dirname($zipPath), 0755, true);
        }

        $zip = new \ZipArchive();
        if ($zip->open($zipPath, \ZipArchive::CREATE | \ZipArchive::OVERWRITE) !== TRUE) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Could not create ZIP file'
                ], 500);
            }
            abort(500, 'Could not create ZIP file');
        }

        // Add each file to the ZIP
        foreach ($order->files as $file) {
            $filePath = Storage::disk('public')->path($file->file_path);

            if (file_exists($filePath)) {
                // Create a safe filename for the ZIP entry
                $zipEntryName = $file->file_type . '_' . $file->original_name;
                $zip->addFile($filePath, $zipEntryName);
            }
        }

        $zip->close();

        // Return the ZIP file for download and delete it after sending
        return response()->download($zipPath, $zipFileName)->deleteFileAfterSend(true);
    }

    /**
     * Serve a file with CORS headers (Public access for viewing)
     */
    public function serveFile(Request $request, $orderId, $fileId)
    {
        try {
            \Log::info('File serve request', [
                'order_id' => $orderId,
                'file_id' => $fileId,
                'user_agent' => $request->userAgent(),
                'origin' => $request->header('Origin'),
            ]);

            // Find the file
            $file = OrderFile::where('printing_order_id', $orderId)
                ->findOrFail($fileId);

            \Log::info('File found', [
                'file_path' => $file->file_path,
                'original_name' => $file->original_name,
                'mime_type' => $file->mime_type,
            ]);

            // Check if file exists in storage
            if (!Storage::disk('public')->exists($file->file_path)) {
                \Log::error('File not found in storage', ['file_path' => $file->file_path]);
                abort(404, 'File not found in storage');
            }

            // Get the full path to the file
            $filePath = Storage::disk('public')->path($file->file_path);

            \Log::info('Serving file', [
                'full_path' => $filePath,
                'file_exists' => file_exists($filePath),
                'file_size' => file_exists($filePath) ? filesize($filePath) : 'N/A',
            ]);

            // Return file response with proper headers
            return response()->file($filePath, [
                'Content-Type' => $file->mime_type,
                'Content-Disposition' => 'inline; filename="' . $file->original_name . '"',
            ]);
        } catch (\Exception $e) {
            \Log::error('File serve error', [
                'order_id' => $orderId,
                'file_id' => $fileId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'error' => 'Failed to serve file',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get image information including dimensions and DPI
     */
    private function getImageInfo($file)
    {
        $dimensions = null;
        $dpi = null;

        try {
            if (extension_loaded('gd') || extension_loaded('imagick')) {
                $imageSize = getimagesize($file->getPathname());
                if ($imageSize) {
                    $dimensions = [
                        'width' => $imageSize[0],
                        'height' => $imageSize[1]
                    ];

                    // Try to get DPI from EXIF data
                    if (function_exists('exif_read_data') && in_array($file->getMimeType(), ['image/jpeg', 'image/tiff'])) {
                        $exif = @exif_read_data($file->getPathname());
                        if ($exif && isset($exif['XResolution'])) {
                            $dpi = $this->parseDpi($exif['XResolution']);
                        }
                    }

                    // If no DPI found, calculate based on common print sizes
                    if (!$dpi && $dimensions) {
                        // Estimate DPI based on dimensions (this is a rough estimate)
                        $dpi = $this->estimateDpi($dimensions);
                    }
                }
            }
        } catch (\Exception $e) {
            // If we can't get image info, continue without it
        }

        return [
            'dimensions' => $dimensions,
            'dpi' => $dpi
        ];
    }

    /**
     * Parse DPI from EXIF data
     */
    private function parseDpi($dpiString)
    {
        if (is_string($dpiString) && strpos($dpiString, '/') !== false) {
            $parts = explode('/', $dpiString);
            if (count($parts) === 2 && $parts[1] != 0) {
                return round($parts[0] / $parts[1]);
            }
        }
        return is_numeric($dpiString) ? (int)$dpiString : null;
    }

    /**
     * Estimate DPI based on image dimensions
     */
    private function estimateDpi($dimensions)
    {
        // This is a rough estimation - in reality, DPI depends on intended print size
        // For business cards (3.5" x 2"), 300 DPI would be about 1050x600 pixels
        $width = $dimensions['width'];
        $height = $dimensions['height'];

        // Common print sizes and their 300 DPI pixel dimensions
        $printSizes = [
            ['width' => 1050, 'height' => 600, 'dpi' => 300], // Business card
            ['width' => 2100, 'height' => 1200, 'dpi' => 300], // Business card 2x
            ['width' => 1240, 'height' => 1754, 'dpi' => 300], // A5 flyer
            ['width' => 2480, 'height' => 3508, 'dpi' => 300], // A4 flyer
        ];

        foreach ($printSizes as $size) {
            $widthRatio = $width / $size['width'];
            $heightRatio = $height / $size['height'];

            if (abs($widthRatio - $heightRatio) < 0.1) { // Similar aspect ratio
                return round($size['dpi'] * $widthRatio);
            }
        }

        // Default estimation: assume 72 DPI base and scale
        return round(72 * min($width / 800, $height / 600));
    }

    /**
     * Get file history
     */
    public function getFileHistory(Request $request, $orderId, $fileId)
    {
        // Verify user owns the order
        $order = PrintingOrder::where('user_id', $request->user()->id)
            ->findOrFail($orderId);

        // Verify file belongs to the order
        $file = OrderFile::where('printing_order_id', $order->id)
            ->findOrFail($fileId);

        // Get file history
        $history = OrderFileHistory::where('order_file_id', $file->id)
            ->with('performedByUser:id,name')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $history
        ]);
    }

    /**
     * Get re-upload permissions for a file
     */
    public function getReUploadPermissions(Request $request, $orderId, $fileId)
    {
        // Verify user owns the order
        $order = PrintingOrder::where('user_id', $request->user()->id)
            ->findOrFail($orderId);

        // Verify file belongs to the order
        $file = OrderFile::where('printing_order_id', $order->id)
            ->findOrFail($fileId);

        // Check permissions based on order status and file state
        $canReUpload = in_array($order->status, ['pending', 'processing', 'revision_required']);
        $reason = '';

        if (!$canReUpload) {
            switch ($order->status) {
                case 'completed':
                    $reason = 'Order has been completed';
                    break;
                case 'cancelled':
                    $reason = 'Order has been cancelled';
                    break;
                case 'shipped':
                    $reason = 'Order has been shipped';
                    break;
                default:
                    $reason = 'Re-upload not allowed for current order status';
            }
        }

        return response()->json([
            'success' => true,
            'data' => [
                'can_reupload' => $canReUpload,
                'reason' => $reason,
                'order_status' => $order->status,
                'file_approved' => $file->is_approved,
            ]
        ]);
    }
}
