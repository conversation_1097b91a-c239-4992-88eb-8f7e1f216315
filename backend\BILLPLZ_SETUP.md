# Billplz Payment Gateway Setup Guide

## Current Issue: 403 Forbidden Error

The payment integration is failing because the Billplz API is returning a 403 Forbidden error. This typically happens when:

1. **Environment Mismatch**: Using production API credentials in sandbox mode (or vice versa)
2. **Invalid API Key**: The API key is incorrect, expired, or revoked
3. **Collection Access**: The API key doesn't have access to the specified collection ID

## How to Fix

### Step 1: Verify Your Billplz Account

1. **Login to Billplz Dashboard**:
   - Sandbox: https://www.billplz-sandbox.com/
   - Production: https://www.billplz.com/

2. **Check Your Environment**:
   - Make sure you're using the correct dashboard for your environment
   - Current setting: **SANDBOX MODE** (as configured in the system)

### Step 2: Get Correct API Credentials

#### For Sandbox Environment (Current Setting):

1. Go to https://www.billplz-sandbox.com/
2. Login to your sandbox account
3. Navigate to **Settings** > **API**
4. Copy your **Sandbox API Key**
5. Navigate to **Billing** > **Collections**
6. Note down a **Collection ID** from your sandbox collections

#### For Production Environment:

1. Go to https://www.billplz.com/
2. Login to your production account
3. Navigate to **Settings** > **API**
4. Copy your **Production API Key**
5. Navigate to **Billing** > **Collections**
6. Note down a **Collection ID** from your production collections

### Step 3: Update Configuration

1. **Access Admin Panel**: Go to `/admin/billplz-settings`
2. **Update Settings**:
   - **Enable Billplz Payment Gateway**: ✓ Checked
   - **Sandbox Mode**: ✓ Checked (for testing) or ✗ Unchecked (for production)
   - **API Key**: Enter the API key from the correct environment
   - **Collection ID**: Enter a collection ID from the same environment
   - **X Signature Key**: Enter the X Signature key (optional but recommended)

### Step 4: Test Configuration

Run the validation command to test your configuration:

```bash
php artisan billplz:validate
```

This will:
- Test API connectivity
- List available collections
- Verify collection access
- Test service initialization

### Step 5: Common Issues and Solutions

#### Issue: "API key is for different environment"
- **Solution**: Make sure your API key matches the sandbox/production mode setting

#### Issue: "Collection not found"
- **Solution**: Use a collection ID that exists in the same environment as your API key

#### Issue: "Access denied to collection"
- **Solution**: Make sure the API key has access to the specified collection

### Step 6: Webhook Configuration

After fixing the API credentials, configure webhooks in your Billplz dashboard:

1. **Callback URL**: `https://yourdomain.com/api/billplz/callback`
2. **Redirect URL**: `https://yourdomain.com/dashboard/credit`

## Testing the Payment Flow

Once configured correctly:

1. **Frontend Test**: Click "Proceed to Payment" on a credit package
2. **Expected Behavior**: User should be redirected to Billplz payment page
3. **After Payment**: User should be redirected back to your application

## Current Configuration Status

- **Billplz Enabled**: ✓ YES
- **Sandbox Mode**: ✓ YES
- **API Key**: ✓ SET (but getting 403 error)
- **Collection ID**: ydmzmo0c (may not exist in sandbox)
- **X Signature Key**: ✓ SET

## Next Steps

1. **Verify Billplz Account**: Make sure you have a valid sandbox account
2. **Get Correct Credentials**: Obtain sandbox API key and collection ID
3. **Update Configuration**: Use the admin panel to update settings
4. **Test Again**: Run `php artisan billplz:validate` to verify

## Support

If you continue to have issues:
1. Check Billplz documentation: https://www.billplz.com/api
2. Contact Billplz support for API key issues
3. Verify your account status and permissions
