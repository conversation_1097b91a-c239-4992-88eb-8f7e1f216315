<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Step 1: Remove old credit_amount column if it still exists
        if (Schema::hasColumn('wallet_transactions', 'credit_amount')) {
            Schema::table('wallet_transactions', function (Blueprint $table) {
                $table->dropColumn('credit_amount');
            });
        }

        // Step 2: Remove old credit_balance column from users if it still exists
        if (Schema::hasColumn('users', 'credit_balance')) {
            Schema::table('users', function (Blueprint $table) {
                $table->dropColumn('credit_balance');
            });
        }

        // Step 3: Update credit_package_id to package_id if needed
        if (Schema::hasColumn('wallet_transactions', 'credit_package_id')) {
            Schema::table('wallet_transactions', function (Blueprint $table) {
                $table->renameColumn('credit_package_id', 'package_id');
            });
        }

        // Step 4: Ensure amount column is not nullable
        Schema::table('wallet_transactions', function (Blueprint $table) {
            $table->decimal('amount', 10, 2)->nullable(false)->change();
        });

        // Step 5: Update type enum to include all wallet transaction types
        // First, let's check what types exist and update them
        $typeMapping = [
            'purchase' => 'top_up',
            'usage' => 'payment',
            'refund' => 'refund',
            'bonus' => 'bonus',
            'adjustment' => 'adjustment',
        ];

        foreach ($typeMapping as $oldType => $newType) {
            DB::table('wallet_transactions')
                ->where('type', $oldType)
                ->update(['type' => $newType]);
        }

        // Temporarily change type column to varchar to avoid enum constraint issues
        DB::statement("ALTER TABLE wallet_transactions MODIFY COLUMN type VARCHAR(20) NOT NULL");

        // Now update the enum constraint
        DB::statement("ALTER TABLE wallet_transactions MODIFY COLUMN type ENUM('top_up', 'payment', 'withdrawal', 'refund', 'bonus', 'adjustment') NOT NULL");

        // Step 6: Add indexes for better performance
        Schema::table('wallet_transactions', function (Blueprint $table) {
            if (!$this->indexExists('wallet_transactions', 'wallet_transactions_user_id_type_payment_status_index')) {
                $table->index(['user_id', 'type', 'payment_status']);
            }
            if (!$this->indexExists('wallet_transactions', 'wallet_transactions_type_created_at_index')) {
                $table->index(['type', 'created_at']);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove indexes
        Schema::table('wallet_transactions', function (Blueprint $table) {
            if ($this->indexExists('wallet_transactions', 'wallet_transactions_user_id_type_payment_status_index')) {
                $table->dropIndex(['user_id', 'type', 'payment_status']);
            }
            if ($this->indexExists('wallet_transactions', 'wallet_transactions_type_created_at_index')) {
                $table->dropIndex(['type', 'created_at']);
            }
        });

        // Revert type enum
        DB::statement("ALTER TABLE wallet_transactions MODIFY COLUMN type ENUM('purchase', 'usage', 'refund', 'bonus', 'adjustment') NOT NULL");

        // Revert type mapping
        $typeMapping = [
            'top_up' => 'purchase',
            'payment' => 'usage',
        ];

        foreach ($typeMapping as $newType => $oldType) {
            DB::table('wallet_transactions')
                ->where('type', $newType)
                ->update(['type' => $oldType]);
        }

        // Make amount nullable again
        Schema::table('wallet_transactions', function (Blueprint $table) {
            $table->decimal('amount', 10, 2)->nullable()->change();
        });

        // Restore package_id to credit_package_id
        if (Schema::hasColumn('wallet_transactions', 'package_id')) {
            Schema::table('wallet_transactions', function (Blueprint $table) {
                $table->renameColumn('package_id', 'credit_package_id');
            });
        }

        // Restore credit_balance column
        Schema::table('users', function (Blueprint $table) {
            $table->integer('credit_balance')->default(0)->after('wallet_balance');
        });

        // Restore credit_amount column
        Schema::table('wallet_transactions', function (Blueprint $table) {
            $table->integer('credit_amount')->after('amount');
        });
    }

    /**
     * Check if an index exists
     */
    private function indexExists(string $table, string $index): bool
    {
        $indexes = DB::select("SHOW INDEX FROM {$table}");
        foreach ($indexes as $indexInfo) {
            if ($indexInfo->Key_name === $index) {
                return true;
            }
        }
        return false;
    }
};
