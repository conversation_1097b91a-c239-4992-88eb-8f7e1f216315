<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PrintingCategory;
use App\Models\PrintingProduct;

class PrintingController extends Controller
{
    /**
     * Get all printing categories
     */
    public function categories()
    {
        $categories = PrintingCategory::active()
            ->ordered()
            ->withCount('activeProducts')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $categories
        ]);
    }

    /**
     * Get products by category
     */
    public function products(Request $request, $categorySlug = null)
    {
        $query = PrintingProduct::with('category')->active()->ordered();

        if ($categorySlug) {
            $category = PrintingCategory::where('slug', $categorySlug)->firstOrFail();
            $query->where('printing_category_id', $category->id);
        }

        $products = $query->get();

        return response()->json([
            'success' => true,
            'data' => $products
        ]);
    }

    /**
     * Get single product details
     */
    public function product($slug)
    {
        $product = PrintingProduct::with('category')
            ->where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        return response()->json([
            'success' => true,
            'data' => $product
        ]);
    }

    /**
     * Calculate price for product with options
     */
    public function calculatePrice(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:printing_products,id',
            'quantity' => 'required|integer|min:1',
            'options' => 'array',
        ]);

        $product = PrintingProduct::findOrFail($request->product_id);

        // Validate quantity limits
        if ($request->quantity < $product->min_quantity) {
            return response()->json([
                'success' => false,
                'message' => "Minimum quantity is {$product->min_quantity}"
            ], 400);
        }

        if ($product->max_quantity && $request->quantity > $product->max_quantity) {
            return response()->json([
                'success' => false,
                'message' => "Maximum quantity is {$product->max_quantity}"
            ], 400);
        }

        $totalPrice = $product->calculatePrice($request->quantity, $request->options ?? []);

        return response()->json([
            'success' => true,
            'data' => [
                'product_id' => (int) $product->id,
                'quantity' => (int) $request->quantity,
                'unit_price' => (float) ($totalPrice / $request->quantity),
                'total_price' => (float) $totalPrice,
                'formatted_total_price' => 'RM ' . number_format($totalPrice, 2),
                'production_time_days' => (int) $product->production_time_days,
            ]
        ]);
    }
}
