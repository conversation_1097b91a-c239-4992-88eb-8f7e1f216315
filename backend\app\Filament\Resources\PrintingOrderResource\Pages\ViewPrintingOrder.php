<?php

namespace App\Filament\Resources\PrintingOrderResource\Pages;

use App\Filament\Resources\PrintingOrderResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\KeyValueEntry;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Support\Enums\FontWeight;
use Illuminate\Support\HtmlString;

class ViewPrintingOrder extends ViewRecord
{
    protected static string $resource = PrintingOrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Load the order with all necessary relationships
        $order = $this->getRecord()->load(['user', 'items.product', 'files.uploader']);

        return $order->toArray();
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Order Information')
                    ->schema([
                        TextEntry::make('order_number')
                            ->label('Order Number')
                            ->weight(FontWeight::Bold),

                        TextEntry::make('user.name')
                            ->label('Customer'),

                        TextEntry::make('status')
                            ->badge()
                            ->color(fn (string $state): string => match ($state) {
                                'pending' => 'warning',
                                'confirmed' => 'info',
                                'in_production' => 'primary',
                                'quality_check' => 'secondary',
                                'completed' => 'success',
                                'shipped' => 'success',
                                'delivered' => 'success',
                                'cancelled' => 'danger',
                                default => 'gray',
                            }),

                        TextEntry::make('payment_status')
                            ->badge()
                            ->color(fn (string $state): string => match ($state) {
                                'pending' => 'warning',
                                'paid' => 'success',
                                'failed' => 'danger',
                                'refunded' => 'info',
                                default => 'gray',
                            }),

                        TextEntry::make('formatted_total_amount')
                            ->label('Total Amount')
                            ->weight(FontWeight::Bold),

                        TextEntry::make('created_at')
                            ->label('Order Date')
                            ->dateTime(),
                    ])->columns(3),

                Section::make('Payment Information')
                    ->schema([
                        TextEntry::make('payment_method')
                            ->label('Payment Method'),

                        TextEntry::make('payment_reference')
                            ->label('Payment Reference'),
                    ])->columns(2),

                Section::make('Delivery Information')
                    ->schema([
                        TextEntry::make('delivery_method')
                            ->label('Delivery Method'),

                        KeyValueEntry::make('delivery_address')
                            ->label('Delivery Address'),
                    ])->columns(1),

                Section::make('Order Items')
                    ->schema([
                        RepeatableEntry::make('items')
                            ->schema([
                                TextEntry::make('product.name')
                                    ->label('Product'),

                                TextEntry::make('quantity')
                                    ->label('Quantity'),

                                TextEntry::make('unit_price')
                                    ->label('Unit Price')
                                    ->money('MYR'),

                                TextEntry::make('total_price')
                                    ->label('Total Price')
                                    ->money('MYR'),
                            ])->columns(4),
                    ]),

                Section::make('Order Files')
                    ->schema([
                        RepeatableEntry::make('files')
                            ->schema([
                                TextEntry::make('original_name')
                                    ->label('File Name')
                                    ->weight(FontWeight::Bold),

                                TextEntry::make('file_type_label')
                                    ->label('Type')
                                    ->badge(),

                                TextEntry::make('formatted_file_size')
                                    ->label('Size'),

                                TextEntry::make('dpi')
                                    ->label('DPI')
                                    ->badge()
                                    ->color(fn ($state): string => match (true) {
                                        $state >= 300 => 'success',
                                        $state >= 150 => 'warning',
                                        $state > 0 => 'danger',
                                        default => 'gray',
                                    }),

                                TextEntry::make('is_approved')
                                    ->label('Approved')
                                    ->badge()
                                    ->color(fn (bool $state): string => $state ? 'success' : 'warning')
                                    ->formatStateUsing(fn (bool $state): string => $state ? 'Yes' : 'No'),

                                TextEntry::make('uploader.name')
                                    ->label('Uploaded By'),

                                TextEntry::make('created_at')
                                    ->label('Upload Date')
                                    ->dateTime(),

                                TextEntry::make('download_link')
                                    ->label('Actions')
                                    ->formatStateUsing(function ($record) {
                                        $downloadUrl = route('admin.orders.files.download', [
                                            'orderId' => $record->printing_order_id,
                                            'fileId' => $record->id
                                        ]);

                                        return new HtmlString(
                                            '<a href="' . $downloadUrl . '"
                                               class="inline-flex items-center px-3 py-1 text-xs font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                                               download="' . htmlspecialchars($record->original_name) . '">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                                Download
                                            </a>'
                                        );
                                    }),
                            ])->columns(4),
                    ]),

                Section::make('Additional Information')
                    ->schema([
                        TextEntry::make('special_instructions')
                            ->label('Special Instructions')
                            ->columnSpanFull(),

                        TextEntry::make('notes')
                            ->label('Admin Notes')
                            ->columnSpanFull(),

                        TextEntry::make('estimated_completion_date')
                            ->label('Estimated Completion')
                            ->date(),

                        TextEntry::make('completed_at')
                            ->label('Completed At')
                            ->dateTime(),

                        TextEntry::make('shipped_at')
                            ->label('Shipped At')
                            ->dateTime(),
                    ])->columns(3),
            ]);
    }
}
