<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\WalletTransactionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class UserController extends Controller
{
    protected WalletTransactionService $transactionService;

    public function __construct(WalletTransactionService $transactionService)
    {
        $this->transactionService = $transactionService;
        
        // Ensure only admin users can access these endpoints
        $this->middleware(function ($request, $next) {
            if (!$request->user() || $request->user()->role !== 'admin') {
                return response()->json(['error' => 'Unauthorized. Admin access required.'], 403);
            }
            return $next($request);
        });
    }

    /**
     * Get user balance information
     */
    public function getBalance(Request $request, int $id): JsonResponse
    {
        try {
            $user = User::findOrFail($id);
            
            // Get balance breakdown using the wallet transaction service
            $breakdown = $this->transactionService->getBalanceBreakdown($user);
            
            // Get recent transactions (last 10)
            $recentTransactions = $user->walletTransactions()
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get()
                ->map(function ($transaction) {
                    return [
                        'id' => $transaction->id,
                        'type' => $transaction->type,
                        'amount' => $transaction->amount,
                        'formatted_amount' => ($transaction->amount >= 0 ? '+' : '') . 'RM ' . number_format(abs($transaction->amount), 2),
                        'description' => $transaction->description,
                        'payment_status' => $transaction->payment_status,
                        'payment_method' => $transaction->payment_method,
                        'created_at' => $transaction->created_at->format('Y-m-d H:i:s'),
                        'formatted_date' => $transaction->created_at->format('M j, Y g:i A'),
                    ];
                });

            return response()->json([
                'success' => true,
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                ],
                'balance' => [
                    'current_balance' => $user->wallet_balance,
                    'formatted_balance' => $user->getFormattedWalletBalance(),
                    'currency' => 'MYR',
                ],
                'breakdown' => $breakdown,
                'recent_transactions' => $recentTransactions,
                'transaction_counts' => [
                    'total' => $user->walletTransactions()->count(),
                    'pending' => $user->walletTransactions()->where('payment_status', 'pending')->count(),
                    'completed' => $user->walletTransactions()->where('payment_status', 'completed')->count(),
                    'failed' => $user->walletTransactions()->where('payment_status', 'failed')->count(),
                ],
            ]);
            
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'error' => 'User not found',
                'message' => "User with ID {$id} does not exist."
            ], 404);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Internal server error',
                'message' => 'An error occurred while retrieving user balance information.'
            ], 500);
        }
    }
}
