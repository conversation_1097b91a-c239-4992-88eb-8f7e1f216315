<?php

namespace App\Http\Requests;

use App\Services\WalletTransactionService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateWalletTransactionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only allow admin users to create transactions
        return $this->user() && $this->user()->role === 'admin';
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $transactionService = new WalletTransactionService();
        $type = $this->input('type');
        
        $rules = [
            'user_id' => 'required|exists:users,id',
            'type' => ['required', Rule::in(WalletTransactionService::VALID_TYPES)],
            'amount' => 'required|numeric|regex:/^\d+(\.\d{1,2})?$/',
            'description' => 'nullable|string|max:1000',
            'payment_reference' => 'nullable|string|max:255',
            'withdrawal_method' => 'nullable|string|max:100',
            'withdrawal_reference' => 'nullable|string|max:255',
            'metadata' => 'nullable|array',
        ];

        // Add type-specific validation rules
        if ($type) {
            $config = $transactionService->getTransactionTypeConfig($type);
            
            // Payment amount validation
            if ($config['requires_payment_amount']) {
                $rules['amount_paid'] = 'required|numeric|min:0|regex:/^\d+(\.\d{1,2})?$/';
            } else {
                $rules['amount_paid'] = 'nullable|numeric|min:0|regex:/^\d+(\.\d{1,2})?$/';
            }
            
            // Payment method validation
            if ($config['requires_payment_method']) {
                $rules['payment_method'] = 'required|in:billplz,manual,system,bank_transfer';
            } else {
                $rules['payment_method'] = 'nullable|in:billplz,manual,system,bank_transfer';
            }
            
            // Package validation
            if ($config['allows_package_selection']) {
                $rules['package_id'] = 'nullable|exists:credit_packages,id';
            } else {
                $rules['package_id'] = 'nullable';
            }
            
            // Payment status validation
            $rules['payment_status'] = 'required|in:pending,completed,failed,refunded';
            
            // Amount sign validation
            switch ($config['amount_sign']) {
                case 'positive':
                    $rules['amount'] .= '|min:0.01';
                    break;
                case 'negative':
                    // We'll handle negative conversion in the service
                    $rules['amount'] .= '|min:0.01';
                    break;
                case 'flexible':
                    // No additional constraint for adjustments
                    break;
            }

            // Withdrawal-specific validations
            if ($type === WalletTransactionService::TYPE_WITHDRAWAL) {
                $rules['withdrawal_method'] = 'required|in:bank_transfer,paypal,manual';
            }
        }

        return $rules;
    }

    /**
     * Get custom validation messages
     */
    public function messages(): array
    {
        return [
            'user_id.required' => 'Please select a user for this transaction.',
            'user_id.exists' => 'The selected user does not exist.',
            'type.required' => 'Please select a transaction type.',
            'type.in' => 'Invalid transaction type selected.',
            'amount.required' => 'Amount is required.',
            'amount.numeric' => 'Amount must be a valid number.',
            'amount.regex' => 'Amount must be a valid currency amount with up to 2 decimal places.',
            'amount.min' => 'Amount must be greater than 0.',
            'amount_paid.required' => 'Payment amount is required for this transaction type.',
            'amount_paid.numeric' => 'Payment amount must be a valid number.',
            'amount_paid.regex' => 'Payment amount must be a valid currency amount with up to 2 decimal places.',
            'amount_paid.min' => 'Payment amount cannot be negative.',
            'payment_method.required' => 'Payment method is required for this transaction type.',
            'payment_method.in' => 'Invalid payment method selected.',
            'payment_status.required' => 'Payment status is required.',
            'payment_status.in' => 'Invalid payment status selected.',
            'package_id.exists' => 'The selected package does not exist.',
            'description.max' => 'Description cannot exceed 1000 characters.',
            'payment_reference.max' => 'Payment reference cannot exceed 255 characters.',
            'withdrawal_method.required' => 'Withdrawal method is required for withdrawal transactions.',
            'withdrawal_method.in' => 'Invalid withdrawal method selected.',
            'withdrawal_reference.max' => 'Withdrawal reference cannot exceed 255 characters.',
            'metadata.array' => 'Metadata must be a valid array.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $type = $this->input('type');
            $amount = $this->input('amount');
            $userId = $this->input('user_id');
            
            if ($type && $amount && $userId) {
                $transactionService = new WalletTransactionService();
                
                try {
                    // Validate business rules using the service
                    $data = $this->validated();
                    $processedData = $transactionService->processTransactionData($data);
                    $transactionService->validateTransactionData($processedData);
                } catch (\Illuminate\Validation\ValidationException $e) {
                    foreach ($e->errors() as $field => $messages) {
                        foreach ($messages as $message) {
                            $validator->errors()->add($field, $message);
                        }
                    }
                }
            }
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        // Ensure processed_at is set for completed transactions
        if ($this->input('payment_status') === 'completed' && !$this->input('processed_at')) {
            $this->merge([
                'processed_at' => now(),
            ]);
        }

        // Ensure withdrawal_processed_at is set for completed withdrawals
        if ($this->input('type') === WalletTransactionService::TYPE_WITHDRAWAL && 
            $this->input('payment_status') === 'completed' && 
            !$this->input('withdrawal_processed_at')) {
            $this->merge([
                'withdrawal_processed_at' => now(),
            ]);
        }

        // Convert amount to proper format
        if ($this->has('amount')) {
            $amount = $this->input('amount');
            if (is_numeric($amount)) {
                $this->merge([
                    'amount' => round((float) $amount, 2),
                ]);
            }
        }

        // Convert amount_paid to proper format
        if ($this->has('amount_paid') && $this->input('amount_paid') !== null) {
            $amountPaid = $this->input('amount_paid');
            if (is_numeric($amountPaid)) {
                $this->merge([
                    'amount_paid' => round((float) $amountPaid, 2),
                ]);
            }
        }
    }

    /**
     * Get the validated data from the request with proper formatting.
     */
    public function validated($key = null, $default = null)
    {
        $validated = parent::validated($key, $default);

        // Ensure decimal precision for currency fields
        if (isset($validated['amount'])) {
            $validated['amount'] = round((float) $validated['amount'], 2);
        }

        if (isset($validated['amount_paid']) && $validated['amount_paid'] !== null) {
            $validated['amount_paid'] = round((float) $validated['amount_paid'], 2);
        }

        return $validated;
    }
}
