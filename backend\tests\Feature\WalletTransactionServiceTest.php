<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\WalletTransaction;
use App\Models\CreditPackage;
use App\Services\WalletTransactionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Validation\ValidationException;
use Tests\TestCase;

class WalletTransactionServiceTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected WalletTransactionService $service;
    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new WalletTransactionService();
        $this->user = User::factory()->create([
            'wallet_balance' => 100.00,
        ]);
    }

    /** @test */
    public function it_can_create_top_up_transaction()
    {
        $package = CreditPackage::factory()->create([
            'price' => 50.00,
            'is_active' => true,
        ]);

        $data = [
            'user_id' => $this->user->id,
            'type' => 'top_up',
            'amount' => 50.00,
            'amount_paid' => 50.00,
            'payment_method' => 'billplz',
            'payment_status' => 'completed',
            'package_id' => $package->id,
        ];

        $transaction = $this->service->createTransaction($data);

        $this->assertInstanceOf(WalletTransaction::class, $transaction);
        $this->assertEquals('top_up', $transaction->type);
        $this->assertEquals(50.00, $transaction->amount);
        $this->assertEquals(50.00, $transaction->amount_paid);
        $this->assertEquals('completed', $transaction->payment_status);
        $this->assertEquals($package->id, $transaction->package_id);

        // Check that user's wallet balance was updated
        $this->user->refresh();
        $this->assertEquals(150.00, $this->user->wallet_balance);
    }

    /** @test */
    public function it_can_create_payment_transaction()
    {
        $data = [
            'user_id' => $this->user->id,
            'type' => 'payment',
            'amount' => 25.00, // Will be converted to negative
            'payment_method' => 'system',
            'payment_status' => 'completed',
            'description' => 'Test payment',
        ];

        $transaction = $this->service->createTransaction($data);

        $this->assertEquals('payment', $transaction->type);
        $this->assertEquals(-25.00, $transaction->amount); // Should be negative
        $this->assertEquals('system', $transaction->payment_method);

        // Check that user's wallet balance was deducted
        $this->user->refresh();
        $this->assertEquals(75.00, $this->user->wallet_balance);
    }

    /** @test */
    public function it_prevents_payment_with_insufficient_balance()
    {
        $data = [
            'user_id' => $this->user->id,
            'type' => 'payment',
            'amount' => 150.00, // More than user's balance of 100.00
            'payment_method' => 'system',
            'payment_status' => 'completed',
        ];

        $this->expectException(ValidationException::class);
        $this->service->createTransaction($data);
    }

    /** @test */
    public function it_can_create_withdrawal_transaction()
    {
        $data = [
            'user_id' => $this->user->id,
            'type' => 'withdrawal',
            'amount' => 30.00, // Will be converted to negative
            'withdrawal_method' => 'bank_transfer',
            'payment_method' => 'manual',
            'payment_status' => 'completed',
        ];

        $transaction = $this->service->createTransaction($data);

        $this->assertEquals('withdrawal', $transaction->type);
        $this->assertEquals(-30.00, $transaction->amount);
        $this->assertEquals('bank_transfer', $transaction->withdrawal_method);

        // Check that user's wallet balance was deducted
        $this->user->refresh();
        $this->assertEquals(70.00, $this->user->wallet_balance);
    }

    /** @test */
    public function it_can_create_refund_transaction()
    {
        $data = [
            'user_id' => $this->user->id,
            'type' => 'refund',
            'amount' => 20.00,
            'payment_method' => 'system',
            'payment_status' => 'completed',
            'description' => 'Order refund',
        ];

        $transaction = $this->service->createTransaction($data);

        $this->assertEquals('refund', $transaction->type);
        $this->assertEquals(20.00, $transaction->amount); // Should be positive

        // Check that user's wallet balance was increased
        $this->user->refresh();
        $this->assertEquals(120.00, $this->user->wallet_balance);
    }

    /** @test */
    public function it_can_create_bonus_transaction()
    {
        $data = [
            'user_id' => $this->user->id,
            'type' => 'bonus',
            'amount' => 15.00,
            'payment_method' => 'system',
            'payment_status' => 'completed',
            'description' => 'Welcome bonus',
        ];

        $transaction = $this->service->createTransaction($data);

        $this->assertEquals('bonus', $transaction->type);
        $this->assertEquals(15.00, $transaction->amount);

        // Check that user's wallet balance was increased
        $this->user->refresh();
        $this->assertEquals(115.00, $this->user->wallet_balance);
    }

    /** @test */
    public function it_can_create_adjustment_transaction()
    {
        // Positive adjustment
        $data = [
            'user_id' => $this->user->id,
            'type' => 'adjustment',
            'amount' => 10.00,
            'payment_method' => 'manual',
            'payment_status' => 'completed',
            'description' => 'Balance correction',
        ];

        $transaction = $this->service->createTransaction($data);

        $this->assertEquals('adjustment', $transaction->type);
        $this->assertEquals(10.00, $transaction->amount);

        $this->user->refresh();
        $this->assertEquals(110.00, $this->user->wallet_balance);

        // Negative adjustment
        $data['amount'] = -5.00;
        $transaction2 = $this->service->createTransaction($data);

        $this->assertEquals(-5.00, $transaction2->amount);

        $this->user->refresh();
        $this->assertEquals(105.00, $this->user->wallet_balance);
    }

    /** @test */
    public function it_validates_transaction_data()
    {
        // Test missing user_id
        $this->expectException(ValidationException::class);
        $this->service->validateTransactionData([
            'type' => 'top_up',
            'amount' => 50.00,
        ]);
    }

    /** @test */
    public function it_calculates_expected_balance_correctly()
    {
        // Create some transactions
        WalletTransaction::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'top_up',
            'amount' => 50.00,
            'payment_status' => 'completed',
        ]);

        WalletTransaction::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'payment',
            'amount' => -25.00,
            'payment_status' => 'completed',
        ]);

        $expectedBalance = $this->service->calculateExpectedBalance($this->user);
        
        // Initial 100 + 50 - 25 = 125
        $this->assertEquals(125.00, $expectedBalance);
    }

    /** @test */
    public function it_can_verify_and_fix_balance()
    {
        // Manually set incorrect balance
        $this->user->update(['wallet_balance' => 50.00]);

        // Create a transaction that should make balance 150
        WalletTransaction::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'top_up',
            'amount' => 50.00,
            'payment_status' => 'completed',
        ]);

        $result = $this->service->verifyAndFixBalance($this->user);

        $this->assertFalse($result['balance_correct']);
        $this->assertTrue($result['fixed']);
        $this->assertEquals(150.00, $result['expected_balance']);

        $this->user->refresh();
        $this->assertEquals(150.00, $this->user->wallet_balance);
    }

    /** @test */
    public function it_provides_balance_breakdown()
    {
        // Create various transaction types
        WalletTransaction::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'top_up',
            'amount' => 100.00,
            'payment_status' => 'completed',
        ]);

        WalletTransaction::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'payment',
            'amount' => -30.00,
            'payment_status' => 'completed',
        ]);

        WalletTransaction::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'bonus',
            'amount' => 20.00,
            'payment_status' => 'completed',
        ]);

        $breakdown = $this->service->getBalanceBreakdown($this->user);

        $this->assertEquals(100.00, $breakdown['top_up']['total_amount']);
        $this->assertEquals(1, $breakdown['top_up']['transaction_count']);
        $this->assertEquals(-30.00, $breakdown['payment']['total_amount']);
        $this->assertEquals(1, $breakdown['payment']['transaction_count']);
        $this->assertEquals(20.00, $breakdown['bonus']['total_amount']);
        $this->assertEquals(1, $breakdown['bonus']['transaction_count']);
    }
}
