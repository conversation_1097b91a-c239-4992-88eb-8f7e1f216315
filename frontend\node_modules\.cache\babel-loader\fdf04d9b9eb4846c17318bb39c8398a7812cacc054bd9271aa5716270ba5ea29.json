{"ast": null, "code": "import addEventListener from './addEventListener';\nimport removeEventListener from './removeEventListener';\nfunction listen(node, eventName, handler, options) {\n  addEventListener(node, eventName, handler, options);\n  return function () {\n    removeEventListener(node, eventName, handler, options);\n  };\n}\nexport default listen;", "map": {"version": 3, "names": ["addEventListener", "removeEventListener", "listen", "node", "eventName", "handler", "options"], "sources": ["C:/laragon/www/frontend/node_modules/dom-helpers/esm/listen.js"], "sourcesContent": ["import addEventListener from './addEventListener';\nimport removeEventListener from './removeEventListener';\n\nfunction listen(node, eventName, handler, options) {\n  addEventListener(node, eventName, handler, options);\n  return function () {\n    removeEventListener(node, eventName, handler, options);\n  };\n}\n\nexport default listen;"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,mBAAmB,MAAM,uBAAuB;AAEvD,SAASC,MAAMA,CAACC,IAAI,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAE;EACjDN,gBAAgB,CAACG,IAAI,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,CAAC;EACnD,OAAO,YAAY;IACjBL,mBAAmB,CAACE,IAAI,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,CAAC;EACxD,CAAC;AACH;AAEA,eAAeJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}