<?php

namespace App\Http\Requests\Auth;

use App\Http\Requests\BaseRequest;

class RegisterRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $commonRules = $this->getCommonRules();
        
        return [
            'name' => $commonRules['name_rule'],
            'email' => $commonRules['email_rule'] . '|unique:users,email',
            'password' => $commonRules['password_rule'],
            'phone' => $commonRules['phone_rule'],
            'date_of_birth' => $commonRules['date_rule'] . '|before:today',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return array_merge(parent::messages(), [
            'email.unique' => 'An account with this email address already exists.',
            'date_of_birth.before' => 'Date of birth must be in the past.',
            'phone.regex' => 'Please enter a valid phone number.',
        ]);
    }

    /**
     * Fields that are allowed to contain HTML (none for registration)
     */
    protected function getAllowedHtmlFields(): array
    {
        return [];
    }
}
