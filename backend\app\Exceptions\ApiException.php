<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Http\JsonResponse;

class ApiException extends Exception
{
    protected $statusCode;
    protected $errorCode;
    protected $context;

    public function __construct(
        string $message = 'An error occurred',
        int $statusCode = 500,
        string $errorCode = 'INTERNAL_ERROR',
        array $context = [],
        Exception $previous = null
    ) {
        parent::__construct($message, 0, $previous);
        $this->statusCode = $statusCode;
        $this->errorCode = $errorCode;
        $this->context = $context;
    }

    /**
     * Render the exception as an HTTP response.
     */
    public function render(): JsonResponse
    {
        $response = [
            'success' => false,
            'error' => [
                'code' => $this->errorCode,
                'message' => $this->message,
                'status_code' => $this->statusCode,
            ],
            'timestamp' => now()->toISOString(),
            'request_id' => request()->header('X-Request-ID', uniqid()),
        ];

        // Add context in development/testing environments
        if (config('app.debug') && !empty($this->context)) {
            $response['debug'] = $this->context;
        }

        // Log the error
        $this->logError();

        return response()->json($response, $this->statusCode);
    }

    /**
     * Log the error with appropriate level
     */
    protected function logError(): void
    {
        $logData = [
            'error_code' => $this->errorCode,
            'message' => $this->message,
            'status_code' => $this->statusCode,
            'context' => $this->context,
            'request_id' => request()->header('X-Request-ID', uniqid()),
            'user_id' => auth()->id(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'url' => request()->fullUrl(),
            'method' => request()->method(),
        ];

        // Log based on severity
        if ($this->statusCode >= 500) {
            \Log::error('API Error: ' . $this->message, $logData);
        } elseif ($this->statusCode >= 400) {
            \Log::warning('API Warning: ' . $this->message, $logData);
        } else {
            \Log::info('API Info: ' . $this->message, $logData);
        }
    }

    /**
     * Static factory methods for common errors
     */
    public static function unauthorized(string $message = 'Unauthorized access'): self
    {
        return new self($message, 401, 'UNAUTHORIZED');
    }

    public static function forbidden(string $message = 'Access forbidden'): self
    {
        return new self($message, 403, 'FORBIDDEN');
    }

    public static function notFound(string $message = 'Resource not found'): self
    {
        return new self($message, 404, 'NOT_FOUND');
    }

    public static function validationFailed(string $message = 'Validation failed', array $errors = []): self
    {
        return new self($message, 422, 'VALIDATION_FAILED', ['validation_errors' => $errors]);
    }

    public static function serverError(string $message = 'Internal server error', array $context = []): self
    {
        return new self($message, 500, 'INTERNAL_ERROR', $context);
    }

    public static function badRequest(string $message = 'Bad request', array $context = []): self
    {
        return new self($message, 400, 'BAD_REQUEST', $context);
    }

    public static function conflict(string $message = 'Resource conflict', array $context = []): self
    {
        return new self($message, 409, 'CONFLICT', $context);
    }

    public static function tooManyRequests(string $message = 'Too many requests'): self
    {
        return new self($message, 429, 'TOO_MANY_REQUESTS');
    }

    public static function serviceUnavailable(string $message = 'Service unavailable'): self
    {
        return new self($message, 503, 'SERVICE_UNAVAILABLE');
    }

    /**
     * Business logic specific errors
     */
    public static function insufficientFunds(float $required, float $available): self
    {
        return new self(
            "Insufficient funds. Required: RM " . number_format($required, 2) . ", Available: RM " . number_format($available, 2),
            400,
            'INSUFFICIENT_FUNDS',
            ['required_amount' => $required, 'available_amount' => $available]
        );
    }

    public static function fileUploadError(string $message = 'File upload failed', array $context = []): self
    {
        return new self($message, 400, 'FILE_UPLOAD_ERROR', $context);
    }

    public static function paymentError(string $message = 'Payment processing failed', array $context = []): self
    {
        return new self($message, 400, 'PAYMENT_ERROR', $context);
    }

    public static function orderError(string $message = 'Order processing failed', array $context = []): self
    {
        return new self($message, 400, 'ORDER_ERROR', $context);
    }

    /**
     * Get the status code
     */
    public function getStatusCode(): int
    {
        return $this->statusCode;
    }

    /**
     * Get the error code
     */
    public function getErrorCode(): string
    {
        return $this->errorCode;
    }

    /**
     * Get the context
     */
    public function getContext(): array
    {
        return $this->context;
    }
}
