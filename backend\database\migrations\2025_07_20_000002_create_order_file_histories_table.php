<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_file_histories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_file_id')->constrained()->onDelete('cascade');
            $table->foreignId('printing_order_id')->constrained()->onDelete('cascade');
            $table->enum('action_type', ['uploaded', 're_uploaded', 'deleted', 'approved', 'rejected']);
            
            // Previous file information (for re-uploads)
            $table->string('previous_file_path')->nullable();
            $table->string('previous_file_name')->nullable();
            $table->string('previous_original_name')->nullable();
            $table->bigInteger('previous_file_size')->nullable();
            $table->string('previous_mime_type')->nullable();
            
            // New file information
            $table->string('new_file_path')->nullable();
            $table->string('new_file_name')->nullable();
            $table->string('new_original_name')->nullable();
            $table->bigInteger('new_file_size')->nullable();
            $table->string('new_mime_type')->nullable();
            
            // Re-upload specific fields
            $table->string('re_upload_reason')->nullable();
            $table->foreignId('performed_by')->constrained('users')->onDelete('cascade');
            $table->json('metadata')->nullable(); // Additional data like compression stats, etc.
            $table->text('notes')->nullable();
            
            $table->timestamps();

            // Indexes for performance
            $table->index(['order_file_id', 'action_type']);
            $table->index(['printing_order_id', 'action_type']);
            $table->index(['performed_by', 'created_at']);
            $table->index('action_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_file_histories');
    }
};
