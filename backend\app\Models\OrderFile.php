<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class OrderFile extends Model
{
    use HasFactory;

    protected $fillable = [
        'printing_order_id',
        'original_name',
        'file_name',
        'file_path',
        'file_size',
        'mime_type',
        'file_type',
        'dimensions',
        'dpi',
        'is_approved',
        'notes',
        'uploaded_by',
        'previous_file_id',
        're_upload_reason',
        're_uploaded_at',
        'version',
        'is_current_version',
    ];

    protected function casts(): array
    {
        return [
            'dimensions' => 'array',
            'is_approved' => 'boolean',
            're_uploaded_at' => 'datetime',
            'is_current_version' => 'boolean',
        ];
    }

    /**
     * The accessors to append to the model's array form.
     */
    protected $appends = [
        'file_url',
        'formatted_file_size',
        'file_type_label',
        'is_image',
        'dpi_status'
    ];

    // File type constants
    const TYPE_ARTWORK = 'artwork';
    const TYPE_REFERENCE = 'reference';
    const TYPE_PROOF = 'proof';

    /**
     * Get the order that owns the file
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(PrintingOrder::class, 'printing_order_id');
    }

    /**
     * Get the user who uploaded the file
     */
    public function uploader(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Get the previous version of this file
     */
    public function previousFile(): BelongsTo
    {
        return $this->belongsTo(OrderFile::class, 'previous_file_id');
    }

    /**
     * Get the next version of this file
     */
    public function nextFile()
    {
        return $this->hasOne(OrderFile::class, 'previous_file_id');
    }

    /**
     * Get all file history records
     */
    public function histories()
    {
        return $this->hasMany(OrderFileHistory::class);
    }

    /**
     * Get all file types
     */
    public static function getFileTypes(): array
    {
        return [
            self::TYPE_ARTWORK => 'Artwork',
            self::TYPE_REFERENCE => 'Reference',
            self::TYPE_PROOF => 'Proof',
        ];
    }

    /**
     * Get file type label
     */
    public function getFileTypeLabelAttribute()
    {
        return self::getFileTypes()[$this->file_type] ?? $this->file_type;
    }

    /**
     * Get formatted file size
     */
    public function getFormattedFileSizeAttribute()
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get file URL
     */
    public function getFileUrlAttribute()
    {
        // Use the dedicated file serving route that includes CORS headers
        // This ensures the frontend can access files from a different domain/port
        return config('app.url') . '/files/' . $this->printing_order_id . '/' . $this->id;
    }

    /**
     * Check if file is an image
     */
    public function getIsImageAttribute()
    {
        return in_array($this->mime_type, [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/svg+xml',
            'image/webp'
        ]);
    }

    /**
     * Check if file meets DPI requirements
     */
    public function meetsDpiRequirements(): bool
    {
        // For print quality, we typically require 300 DPI minimum
        return $this->dpi >= 300;
    }

    /**
     * Get DPI status
     */
    public function getDpiStatusAttribute()
    {
        if (!$this->dpi) {
            return 'unknown';
        }
        
        if ($this->dpi >= 300) {
            return 'good';
        } elseif ($this->dpi >= 150) {
            return 'acceptable';
        } else {
            return 'poor';
        }
    }

    /**
     * Check if this file has been re-uploaded
     */
    public function hasBeenReUploaded(): bool
    {
        return !is_null($this->re_uploaded_at);
    }

    /**
     * Check if this is the current version of the file
     */
    public function isCurrentVersion(): bool
    {
        return $this->is_current_version;
    }

    /**
     * Get all versions of this file (including this one)
     */
    public function getAllVersions()
    {
        $versions = collect([$this]);

        // Get previous versions
        $current = $this;
        while ($current->previousFile) {
            $current = $current->previousFile;
            $versions->prepend($current);
        }

        // Get next versions
        $current = $this;
        while ($current->nextFile) {
            $current = $current->nextFile;
            $versions->push($current);
        }

        return $versions->sortBy('version');
    }

    /**
     * Mark this file as replaced by a new version
     */
    public function markAsReplaced(): void
    {
        $this->update(['is_current_version' => false]);
    }

    /**
     * Create a re-upload history record
     */
    public function createReUploadHistory(User $user, string $reason = null, array $metadata = []): void
    {
        OrderFileHistory::createReUploadHistory(
            $this,
            $this->previousFile,
            $user,
            $reason,
            $metadata
        );
    }

    /**
     * Delete file from storage when model is deleted
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($file) {
            if (Storage::exists($file->file_path)) {
                Storage::delete($file->file_path);
            }
        });
    }
}
