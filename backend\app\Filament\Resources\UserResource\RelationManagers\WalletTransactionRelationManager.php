<?php

namespace App\Filament\Resources\UserResource\RelationManagers;

use App\Models\WalletTransaction;
use App\Services\WalletTransactionService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Notifications\Notification;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class WalletTransactionRelationManager extends RelationManager
{
    protected static string $relationship = 'walletTransactions';

    protected static ?string $title = 'Balance & Transactions';

    protected static ?string $recordTitleAttribute = 'description';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Transaction Details')
                    ->schema([
                        Forms\Components\Select::make('type')
                            ->options([
                                'top_up' => 'Top Up',
                                'payment' => 'Payment',
                                'withdrawal' => 'Withdrawal',
                                'refund' => 'Refund',
                                'bonus' => 'Bonus',
                                'adjustment' => 'Adjustment',
                            ])
                            ->required(),

                        Forms\Components\TextInput::make('amount')
                            ->numeric()
                            ->step(0.01)
                            ->required()
                            ->prefix('RM')
                            ->helperText('Enter positive amount for credits, negative for debits'),

                        Forms\Components\Select::make('payment_method')
                            ->options([
                                'system' => 'System',
                                'billplz' => 'Billplz',
                                'manual' => 'Manual',
                                'bank_transfer' => 'Bank Transfer',
                                'cash' => 'Cash',
                            ])
                            ->default('system')
                            ->required(),

                        Forms\Components\Select::make('payment_status')
                            ->options([
                                'pending' => 'Pending',
                                'completed' => 'Completed',
                                'failed' => 'Failed',
                                'cancelled' => 'Cancelled',
                            ])
                            ->default('completed')
                            ->required(),

                        Forms\Components\Textarea::make('description')
                            ->required()
                            ->maxLength(255)
                            ->columnSpanFull(),
                    ])->columns(2),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('description')
            ->columns([
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'top_up' => 'success',
                        'payment' => 'danger',
                        'withdrawal' => 'warning',
                        'refund' => 'info',
                        'bonus' => 'success',
                        'adjustment' => 'gray',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'top_up' => 'Top Up',
                        'payment' => 'Payment',
                        'withdrawal' => 'Withdrawal',
                        'refund' => 'Refund',
                        'bonus' => 'Bonus',
                        'adjustment' => 'Adjustment',
                        default => ucfirst($state),
                    }),

                Tables\Columns\TextColumn::make('amount')
                    ->label('Amount')
                    ->formatStateUsing(function ($state) {
                        $prefix = $state >= 0 ? '+' : '';
                        return $prefix . 'RM ' . number_format(abs($state), 2);
                    })
                    ->color(fn ($state) => $state >= 0 ? 'success' : 'danger')
                    ->weight('bold')
                    ->sortable(),

                Tables\Columns\TextColumn::make('description')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 50 ? $state : null;
                    }),

                Tables\Columns\TextColumn::make('payment_method')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'billplz' => 'Billplz',
                        'bank_transfer' => 'Bank Transfer',
                        default => ucfirst($state),
                    }),

                Tables\Columns\TextColumn::make('payment_status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'completed' => 'success',
                        'pending' => 'warning',
                        'failed' => 'danger',
                        'cancelled' => 'gray',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => ucfirst($state)),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Date')
                    ->dateTime('M j, Y g:i A')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'top_up' => 'Top Up',
                        'payment' => 'Payment',
                        'withdrawal' => 'Withdrawal',
                        'refund' => 'Refund',
                        'bonus' => 'Bonus',
                        'adjustment' => 'Adjustment',
                    ]),

                Tables\Filters\SelectFilter::make('payment_status')
                    ->options([
                        'pending' => 'Pending',
                        'completed' => 'Completed',
                        'failed' => 'Failed',
                        'cancelled' => 'Cancelled',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\Action::make('view_balance')
                    ->label('Current Balance: ' . $this->getOwnerRecord()->getFormattedWalletBalance())
                    ->icon('heroicon-o-banknotes')
                    ->color('success')
                    ->disabled()
                    ->extraAttributes(['class' => 'font-bold']),

                Tables\Actions\CreateAction::make()
                    ->label('Add Transaction')
                    ->icon('heroicon-o-plus')
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['processed_at'] = now();
                        return $data;
                    })
                    ->after(function ($record) {
                        Notification::make()
                            ->title('Transaction Created')
                            ->body("Transaction of RM " . number_format(abs($record->amount), 2) . " has been added.")
                            ->success()
                            ->send();
                    }),

                Tables\Actions\Action::make('verify_balance')
                    ->label('Verify Balance')
                    ->icon('heroicon-o-check-circle')
                    ->color('warning')
                    ->action(function () {
                        $user = $this->getOwnerRecord();
                        $service = app(WalletTransactionService::class);
                        $result = $service->verifyAndFixBalance($user);

                        if ($result['balance_correct']) {
                            Notification::make()
                                ->title('Balance Verified')
                                ->body('User balance is correct: ' . $user->getFormattedWalletBalance())
                                ->success()
                                ->send();
                        } else {
                            Notification::make()
                                ->title('Balance Corrected')
                                ->body("Balance was corrected from RM " . number_format($result['current_balance'], 2) . " to RM " . number_format($result['expected_balance'], 2))
                                ->warning()
                                ->send();
                        }
                    }),

                Tables\Actions\Action::make('add_balance')
                    ->label('Add Balance')
                    ->icon('heroicon-o-plus-circle')
                    ->color('success')
                    ->form([
                        Forms\Components\TextInput::make('amount')
                            ->label('Amount to Add')
                            ->numeric()
                            ->step(0.01)
                            ->required()
                            ->prefix('RM')
                            ->minValue(0.01),
                        Forms\Components\Textarea::make('description')
                            ->label('Description')
                            ->required()
                            ->placeholder('Reason for adding balance...'),
                    ])
                    ->action(function (array $data) {
                        $user = $this->getOwnerRecord();
                        $user->addToWallet($data['amount'], $data['description'], 'bonus');

                        Notification::make()
                            ->title('Balance Added')
                            ->body("RM " . number_format($data['amount'], 2) . " has been added to {$user->name}'s wallet.")
                            ->success()
                            ->send();
                    }),

                Tables\Actions\Action::make('deduct_balance')
                    ->label('Deduct Balance')
                    ->icon('heroicon-o-minus-circle')
                    ->color('danger')
                    ->form([
                        Forms\Components\TextInput::make('amount')
                            ->label('Amount to Deduct')
                            ->numeric()
                            ->step(0.01)
                            ->required()
                            ->prefix('RM')
                            ->minValue(0.01)
                            ->maxValue(fn () => $this->getOwnerRecord()->wallet_balance),
                        Forms\Components\Textarea::make('description')
                            ->label('Description')
                            ->required()
                            ->placeholder('Reason for deducting balance...'),
                    ])
                    ->action(function (array $data) {
                        $user = $this->getOwnerRecord();
                        $success = $user->deductFromWallet($data['amount'], $data['description'], 'adjustment');

                        if ($success) {
                            Notification::make()
                                ->title('Balance Deducted')
                                ->body("RM " . number_format($data['amount'], 2) . " has been deducted from {$user->name}'s wallet.")
                                ->success()
                                ->send();
                        } else {
                            Notification::make()
                                ->title('Insufficient Balance')
                                ->body("Cannot deduct RM " . number_format($data['amount'], 2) . ". User has insufficient balance.")
                                ->danger()
                                ->send();
                        }
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading('No transactions found')
            ->emptyStateDescription('This user has no wallet transactions yet.')
            ->emptyStateIcon('heroicon-o-banknotes');
    }
}
