<?php

namespace App\Filament\Resources\WalletTransactionResource\Pages;

use App\Filament\Resources\WalletTransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListWalletTransactions extends ListRecords
{
    protected static string $resource = WalletTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('All Transactions'),
            
            'pending' => Tab::make('Pending')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('payment_status', 'pending'))
                ->badge(fn () => $this->getModel()::where('payment_status', 'pending')->count())
                ->badgeColor('warning'),
            
            'completed' => Tab::make('Completed')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('payment_status', 'completed'))
                ->badge(fn () => $this->getModel()::where('payment_status', 'completed')->count())
                ->badgeColor('success'),
            
            'failed' => Tab::make('Failed')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('payment_status', 'failed'))
                ->badge(fn () => $this->getModel()::where('payment_status', 'failed')->count())
                ->badgeColor('danger'),
            
            'top_up' => Tab::make('Top Ups')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('type', 'top_up'))
                ->badge(fn () => $this->getModel()::where('type', 'top_up')->count()),
            
            'payment' => Tab::make('Payments')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('type', 'payment'))
                ->badge(fn () => $this->getModel()::where('type', 'payment')->count()),
            
            'withdrawal' => Tab::make('Withdrawals')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('type', 'withdrawal'))
                ->badge(fn () => $this->getModel()::where('type', 'withdrawal')->count()),
        ];
    }
}
