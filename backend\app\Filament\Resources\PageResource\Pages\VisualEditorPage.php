<?php

namespace App\Filament\Resources\PageResource\Pages;

use App\Filament\Resources\PageResource;
use App\Models\Page;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\Support\Htmlable;

class VisualEditorPage extends ViewRecord
{
    protected static string $resource = PageResource::class;

    protected static string $view = 'filament.resources.page-resource.pages.visual-editor';

    public function getTitle(): string | Htmlable
    {
        return "Visual Editor - {$this->record->title}";
    }

    public function getBreadcrumbs(): array
    {
        return [
            url()->route('filament.admin.resources.pages.index') => 'Pages',
            '#' => $this->getTitle(),
        ];
    }

    public function getViewData(): array
    {
        // Get frontend URL from environment or use default
        $frontendUrl = config('app.frontend_url', env('FRONTEND_URL', 'http://localhost:3000'));
        $frontendUrl = rtrim($frontendUrl, '/');

        return [
            'record' => $this->record,
            'editorUrl' => "{$frontendUrl}/visual-editor?page={$this->record->slug}",
        ];
    }
}
