<?php

require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\PaymentSetting;

echo "=== TESTING FIXED SANDBOX MODE ===\n";

// Test setting to production mode (false)
PaymentSetting::set('billplz_sandbox_mode', false, 'boolean');
$value = PaymentSetting::get('billplz_sandbox_mode');
echo "Set to false, got: " . var_export($value, true) . " (" . ($value ? 'Sandbox' : 'Production') . ")\n";

// Test setting to sandbox mode (true)
PaymentSetting::set('billplz_sandbox_mode', true, 'boolean');
$value = PaymentSetting::get('billplz_sandbox_mode');
echo "Set to true, got: " . var_export($value, true) . " (" . ($value ? 'Sandbox' : 'Production') . ")\n";

// Test setting back to production mode (false)
PaymentSetting::set('billplz_sandbox_mode', false, 'boolean');
$value = PaymentSetting::get('billplz_sandbox_mode');
echo "Set to false again, got: " . var_export($value, true) . " (" . ($value ? 'Sandbox' : 'Production') . ")\n";

echo "✓ Fix verified!\n";
