<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Step 1: Update users table - convert credit_balance to wallet_balance
        Schema::table('users', function (Blueprint $table) {
            // Add new wallet_balance column as decimal
            $table->decimal('wallet_balance', 10, 2)->default(0.00)->after('credit_balance');
        });

        // Step 2: Migrate existing credit balances to wallet balances (1:1 conversion)
        DB::statement('UPDATE users SET wallet_balance = credit_balance WHERE credit_balance IS NOT NULL');

        // Step 3: Update credit_transactions table structure for wallet transactions
        Schema::table('credit_transactions', function (Blueprint $table) {
            // Rename credit_amount to amount (decimal for currency)
            $table->decimal('amount', 10, 2)->after('credit_amount');
        });

        // Step 4: Migrate existing credit transaction amounts (1:1 conversion)
        DB::statement('UPDATE credit_transactions SET amount = credit_amount WHERE credit_amount IS NOT NULL');

        // Step 5: Update transaction types to wallet equivalents
        $typeMapping = [
            'purchase' => 'top_up',
            'usage' => 'payment',
            'refund' => 'refund',
            'bonus' => 'bonus',
            'adjustment' => 'adjustment'
        ];

        foreach ($typeMapping as $oldType => $newType) {
            DB::table('credit_transactions')
                ->where('type', $oldType)
                ->update(['type' => $newType]);
        }

        // Step 6: Add withdrawal transaction type support
        // (No existing data to migrate for this new type)

        // Step 7: Update amount_paid to be more wallet-friendly
        Schema::table('credit_transactions', function (Blueprint $table) {
            // Ensure amount_paid is decimal for currency
            $table->decimal('amount_paid', 10, 2)->nullable()->change();
        });

        // Step 8: Add wallet-specific fields if needed
        Schema::table('credit_transactions', function (Blueprint $table) {
            // Add withdrawal-specific fields
            $table->string('withdrawal_method')->nullable()->after('payment_method');
            $table->string('withdrawal_reference')->nullable()->after('payment_reference');
            $table->timestamp('withdrawal_processed_at')->nullable()->after('processed_at');
        });

        // Step 9: Update indexes for better performance
        Schema::table('credit_transactions', function (Blueprint $table) {
            $table->index(['user_id', 'type', 'payment_status']);
            $table->index(['type', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reverse Step 9: Remove indexes
        Schema::table('credit_transactions', function (Blueprint $table) {
            $table->dropIndex(['user_id', 'type', 'payment_status']);
            $table->dropIndex(['type', 'created_at']);
        });

        // Reverse Step 8: Remove wallet-specific fields
        Schema::table('credit_transactions', function (Blueprint $table) {
            $table->dropColumn(['withdrawal_method', 'withdrawal_reference', 'withdrawal_processed_at']);
        });

        // Reverse Step 7: Revert amount_paid changes
        Schema::table('credit_transactions', function (Blueprint $table) {
            $table->decimal('amount_paid', 8, 2)->nullable()->change();
        });

        // Reverse Step 5: Revert transaction types
        $typeMapping = [
            'top_up' => 'purchase',
            'payment' => 'usage',
            'refund' => 'refund',
            'bonus' => 'bonus',
            'adjustment' => 'adjustment'
        ];

        foreach ($typeMapping as $newType => $oldType) {
            DB::table('credit_transactions')
                ->where('type', $newType)
                ->update(['type' => $oldType]);
        }

        // Reverse Step 4: Restore credit_amount from amount
        DB::statement('UPDATE credit_transactions SET credit_amount = amount WHERE amount IS NOT NULL');

        // Reverse Step 3: Remove amount column
        Schema::table('credit_transactions', function (Blueprint $table) {
            $table->dropColumn('amount');
        });

        // Reverse Step 2: Restore credit_balance from wallet_balance
        DB::statement('UPDATE users SET credit_balance = wallet_balance WHERE wallet_balance IS NOT NULL');

        // Reverse Step 1: Remove wallet_balance column
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('wallet_balance');
        });
    }
};
