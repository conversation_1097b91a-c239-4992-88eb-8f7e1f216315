<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\WalletTransaction;
use App\Models\CreditPackage;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class WalletApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;
    protected User $admin;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create([
            'wallet_balance' => 150.50,
            'role' => 'user',
        ]);

        $this->admin = User::factory()->create([
            'role' => 'admin',
        ]);
    }

    /** @test */
    public function it_returns_user_wallet_balance()
    {
        $response = $this->actingAs($this->user, 'sanctum')
            ->getJson('/api/credit/balance');

        $response->assertStatus(200)
            ->assertJson([
                'balance' => 150.50,
                'wallet_balance' => 150.50,
                'credit_balance' => 150.50, // Backward compatibility
                'formatted_balance' => 'RM 150.50',
                'currency' => 'MYR',
                'user_id' => $this->user->id,
            ]);
    }

    /** @test */
    public function it_returns_wallet_transaction_history()
    {
        // Create some transactions
        WalletTransaction::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'top_up',
            'amount' => 100.00,
            'payment_status' => 'completed',
            'description' => 'Wallet top-up',
        ]);

        WalletTransaction::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'payment',
            'amount' => -25.00,
            'payment_status' => 'completed',
            'description' => 'Order payment',
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
            ->getJson('/api/credit/transactions');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'transactions' => [
                    'data' => [
                        '*' => [
                            'id',
                            'type',
                            'amount',
                            'credit_amount', // Backward compatibility
                            'formatted_amount',
                            'amount_paid',
                            'formatted_amount_paid',
                            'payment_method',
                            'payment_status',
                            'description',
                            'is_credit',
                            'is_debit',
                            'created_at',
                        ]
                    ]
                ]
            ]);

        $transactions = $response->json('transactions.data');
        $this->assertCount(2, $transactions);
    }

    /** @test */
    public function it_returns_wallet_statistics()
    {
        // Create various transaction types
        WalletTransaction::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'top_up',
            'amount' => 100.00,
            'payment_status' => 'completed',
        ]);

        WalletTransaction::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'payment',
            'amount' => -30.00,
            'payment_status' => 'completed',
        ]);

        WalletTransaction::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'bonus',
            'amount' => 20.00,
            'payment_status' => 'completed',
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
            ->getJson('/api/credit/statistics');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'current_balance',
                'wallet_balance',
                'credit_balance', // Backward compatibility
                'formatted_balance',
                'currency',
                'total_top_up',
                'total_purchased', // Backward compatibility
                'total_payments',
                'total_used', // Backward compatibility
                'total_withdrawals',
                'total_refunded',
                'total_bonus',
                'total_adjustments',
                'total_spent',
                'expected_balance',
                'balance_matches',
                'recent_transactions',
            ]);

        $data = $response->json();
        $this->assertEquals(100.00, $data['total_top_up']);
        $this->assertEquals(30.00, $data['total_payments']);
        $this->assertEquals(20.00, $data['total_bonus']);
    }

    /** @test */
    public function admin_can_create_wallet_transaction()
    {
        $transactionData = [
            'user_id' => $this->user->id,
            'type' => 'bonus',
            'amount' => 25.00,
            'payment_method' => 'system',
            'payment_status' => 'completed',
            'description' => 'Admin bonus',
        ];

        $response = $this->actingAs($this->admin, 'sanctum')
            ->postJson('/api/admin/wallet-transactions', $transactionData);

        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => 'Wallet transaction created successfully',
            ])
            ->assertJsonStructure([
                'transaction' => [
                    'id',
                    'type',
                    'amount',
                    'formatted_amount',
                    'payment_status',
                    'description',
                ]
            ]);

        $this->assertDatabaseHas('wallet_transactions', [
            'user_id' => $this->user->id,
            'type' => 'bonus',
            'amount' => 25.00,
            'payment_status' => 'completed',
        ]);
    }

    /** @test */
    public function non_admin_cannot_create_wallet_transaction()
    {
        $transactionData = [
            'user_id' => $this->user->id,
            'type' => 'bonus',
            'amount' => 25.00,
            'payment_method' => 'system',
            'payment_status' => 'completed',
            'description' => 'User bonus attempt',
        ];

        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/admin/wallet-transactions', $transactionData);

        $response->assertStatus(403)
            ->assertJson([
                'error' => 'Unauthorized. Admin access required.',
            ]);
    }

    /** @test */
    public function admin_can_verify_user_balance()
    {
        $response = $this->actingAs($this->admin, 'sanctum')
            ->postJson('/api/admin/wallet-transactions/verify-balance', [
                'user_id' => $this->user->id,
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonStructure([
                'balance_verification' => [
                    'user_id',
                    'current_balance',
                    'expected_balance',
                    'balance_correct',
                    'fixed',
                ]
            ]);
    }

    /** @test */
    public function admin_can_get_balance_breakdown()
    {
        // Create some transactions
        WalletTransaction::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'top_up',
            'amount' => 100.00,
            'payment_status' => 'completed',
        ]);

        $response = $this->actingAs($this->admin, 'sanctum')
            ->getJson("/api/admin/wallet-transactions/balance-breakdown?user_id={$this->user->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'user_id' => $this->user->id,
                'currency' => 'MYR',
            ])
            ->assertJsonStructure([
                'current_balance',
                'formatted_balance',
                'breakdown' => [
                    'top_up' => [
                        'total_amount',
                        'transaction_count',
                        'formatted_amount',
                    ],
                    'payment',
                    'withdrawal',
                    'refund',
                    'bonus',
                    'adjustment',
                ]
            ]);
    }

    /** @test */
    public function admin_can_get_transaction_statistics()
    {
        // Create various transactions
        WalletTransaction::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'top_up',
            'amount' => 100.00,
            'payment_status' => 'completed',
        ]);

        WalletTransaction::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'payment',
            'amount' => -25.00,
            'payment_status' => 'completed',
        ]);

        $response = $this->actingAs($this->admin, 'sanctum')
            ->getJson('/api/admin/wallet-transactions/statistics');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonStructure([
                'statistics' => [
                    'top_up' => [
                        'total_transactions',
                        'completed_transactions',
                        'pending_transactions',
                        'total_amount',
                        'completed_amount',
                        'formatted_total_amount',
                        'formatted_completed_amount',
                    ],
                    'payment',
                    'withdrawal',
                    'refund',
                    'bonus',
                    'adjustment',
                ]
            ]);
    }

    /** @test */
    public function admin_can_create_specific_transaction_types()
    {
        $transactionTypes = [
            'top-up' => ['amount' => 50.00, 'payment_method' => 'billplz'],
            'payment' => ['amount' => 25.00, 'payment_method' => 'system'],
            'withdrawal' => ['amount' => 30.00, 'withdrawal_method' => 'bank_transfer'],
            'refund' => ['amount' => 15.00, 'payment_method' => 'system'],
            'bonus' => ['amount' => 10.00, 'payment_method' => 'system'],
            'adjustment' => ['amount' => 5.00, 'payment_method' => 'manual'],
        ];

        foreach ($transactionTypes as $type => $data) {
            $requestData = array_merge([
                'user_id' => $this->user->id,
                'payment_status' => 'completed',
                'description' => "Test {$type}",
            ], $data);

            $response = $this->actingAs($this->admin, 'sanctum')
                ->postJson("/api/admin/wallet-transactions/{$type}", $requestData);

            $response->assertStatus(201)
                ->assertJson([
                    'success' => true,
                ]);
        }

        // Verify all transactions were created
        $this->assertEquals(6, WalletTransaction::where('user_id', $this->user->id)->count());
    }

    /** @test */
    public function it_validates_transaction_creation_data()
    {
        $invalidData = [
            'user_id' => 999, // Non-existent user
            'type' => 'invalid_type',
            'amount' => 'not_a_number',
            'payment_status' => 'invalid_status',
        ];

        $response = $this->actingAs($this->admin, 'sanctum')
            ->postJson('/api/admin/wallet-transactions', $invalidData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['user_id', 'type', 'amount', 'payment_status']);
    }
}
