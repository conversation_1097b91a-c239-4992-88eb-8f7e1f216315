<?php

namespace App\Filament\Pages;

use App\Models\GeneralSetting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Cache;

class GeneralSettings extends Page implements Forms\Contracts\HasForms
{
    use Forms\Concerns\InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';
    protected static ?string $navigationGroup = 'Settings';
    protected static ?string $navigationLabel = 'General Settings';
    protected static ?int $navigationSort = 1;
    protected static string $view = 'filament.pages.general-settings';

    public ?array $data = [];

    public function mount(): void
    {
        // Load current settings with defaults
        $this->data = [
            'site_name' => GeneralSetting::get('site_name', 'My Website'),
            'site_meta_title' => GeneralSetting::get('site_meta_title', 'My Website - Welcome'),
            'site_meta_description' => GeneralSetting::get('site_meta_description', 'Welcome to our website. We provide excellent services and products.'),
            'site_meta_keywords' => GeneralSetting::get('site_meta_keywords', 'website, services, products, business'),
            'site_logo' => GeneralSetting::get('site_logo'),
            'site_favicon' => GeneralSetting::get('site_favicon'),
        ];
    }

    public function form(Form $form): Form
    {
        return $form
            ->statePath('data')
            ->schema([
                // Basic Site Information Section
                Forms\Components\Section::make('Basic Site Information')
                    ->description('Configure your website\'s basic information and branding')
                    ->schema([
                        Forms\Components\TextInput::make('site_name')
                            ->label('Site Name')
                            ->helperText('The display name of your website')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\Grid::make(1)
                            ->schema([
                                Forms\Components\TextInput::make('site_meta_title')
                                    ->label('Site Meta Title')
                                    ->helperText('The HTML meta title tag for SEO (appears in browser tab and search results). Recommended length: 50-60 characters.')
                                    ->required()
                                    ->maxLength(60),

                                Forms\Components\Textarea::make('site_meta_description')
                                    ->label('Site Meta Description')
                                    ->helperText('The HTML meta description tag for SEO (appears in search results). Recommended length: 150-160 characters.')
                                    ->required()
                                    ->rows(3)
                                    ->maxLength(160),

                                Forms\Components\TextInput::make('site_meta_keywords')
                                    ->label('Site Meta Keywords')
                                    ->helperText('Comma-separated keywords for SEO (e.g., "printing, services, business")')
                                    ->maxLength(255),
                            ]),
                    ])->columns(1),

                // Site Branding Section
                Forms\Components\Section::make('Site Branding')
                    ->description('Upload your site logo and favicon')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\FileUpload::make('site_logo')
                                    ->label('Site Logo')
                                    ->helperText('Upload your main site logo (JPG, PNG formats accepted)')
                                    ->image()
                                    ->imageEditor()
                                    ->imageEditorAspectRatios([
                                        '16:9',
                                        '4:3',
                                        '1:1',
                                    ])
                                    ->directory('site-assets/logos')
                                    ->disk('public')
                                    ->acceptedFileTypes(['image/jpeg', 'image/png'])
                                    ->maxSize(5120) // 5MB
                                    ->imagePreviewHeight('100')
                                    ->loadingIndicatorPosition('left')
                                    ->panelAspectRatio('2:1')
                                    ->panelLayout('integrated')
                                    ->removeUploadedFileButtonPosition('right')
                                    ->uploadButtonPosition('left')
                                    ->uploadProgressIndicatorPosition('left'),

                                Forms\Components\FileUpload::make('site_favicon')
                                    ->label('Site Favicon')
                                    ->helperText('Upload your site favicon (ICO, PNG formats, recommended 32x32px)')
                                    ->image()
                                    ->directory('site-assets/favicons')
                                    ->disk('public')
                                    ->acceptedFileTypes(['image/x-icon', 'image/png'])
                                    ->maxSize(512) // 512KB
                                    ->imagePreviewHeight('32')
                                    ->loadingIndicatorPosition('left')
                                    ->panelAspectRatio('1:1')
                                    ->panelLayout('integrated')
                                    ->removeUploadedFileButtonPosition('right')
                                    ->uploadButtonPosition('left')
                                    ->uploadProgressIndicatorPosition('left'),
                            ]),
                    ])->columns(1),

                // SEO Guidelines Section
                Forms\Components\Section::make('SEO Guidelines')
                    ->description('Best practices for search engine optimization')
                    ->schema([
                        Forms\Components\Placeholder::make('seo_title_guide')
                            ->label('Meta Title Guidelines')
                            ->content('• Keep it under 60 characters\n• Include your main keyword\n• Make it compelling and descriptive\n• Each page should have a unique title'),

                        Forms\Components\Placeholder::make('seo_description_guide')
                            ->label('Meta Description Guidelines')
                            ->content('• Keep it under 160 characters\n• Include relevant keywords naturally\n• Write a compelling summary\n• Include a call-to-action when appropriate'),

                        Forms\Components\Placeholder::make('seo_keywords_guide')
                            ->label('Meta Keywords Guidelines')
                            ->content('• Use 5-10 relevant keywords\n• Separate with commas\n• Focus on your main services/products\n• Avoid keyword stuffing'),
                    ])->columns(3)->collapsible(),
            ]);
    }

    public function save(): void
    {
        $data = $this->form->getState();

        try {
            // Save Basic Site Information
            GeneralSetting::set('site_name', $data['site_name']);
            GeneralSetting::set('site_meta_title', $data['site_meta_title']);
            GeneralSetting::set('site_meta_description', $data['site_meta_description']);
            GeneralSetting::set('site_meta_keywords', $data['site_meta_keywords']);

            // Save file uploads (these will be file paths)
            if (isset($data['site_logo'])) {
                GeneralSetting::set('site_logo', $data['site_logo']);
            }
            if (isset($data['site_favicon'])) {
                GeneralSetting::set('site_favicon', $data['site_favicon']);
            }

            // Clear settings cache to ensure frontend gets updated values
            Cache::forget('general_settings');
            Cache::forget('general_settings_seo');
            Cache::forget('general_settings_branding');

            Notification::make()
                ->title('Settings saved successfully')
                ->body('Your general settings have been updated.')
                ->success()
                ->send();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Error saving settings')
                ->body('There was an error saving your settings: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }
}
