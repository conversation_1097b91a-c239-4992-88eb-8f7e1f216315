<div class="space-y-4 text-sm">
    <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
        <h4 class="font-semibold text-blue-900 dark:text-blue-100 mb-2">JSON Structure Reference</h4>
        <p class="text-blue-800 dark:text-blue-200 mb-3">
            The options field stores pricing and configuration data in JSON format. Here are the supported structures:
        </p>
    </div>

    <div class="space-y-3">
        <div class="bg-gray-50 dark:bg-gray-800 p-3 rounded border">
            <h5 class="font-medium mb-2">1. Quantity Pricing</h5>
            <pre class="text-xs bg-gray-100 dark:bg-gray-900 p-2 rounded overflow-x-auto"><code>{
  "quantity_pricing": [
    {"min_quantity": 100, "price_per_unit": 0.25},
    {"min_quantity": 500, "price_per_unit": 0.20},
    {"min_quantity": 1000, "price_per_unit": 0.15}
  ]
}</code></pre>
        </div>

        <div class="bg-gray-50 dark:bg-gray-800 p-3 rounded border">
            <h5 class="font-medium mb-2">2. Product Options with Pricing</h5>
            <pre class="text-xs bg-gray-100 dark:bg-gray-900 p-2 rounded overflow-x-auto"><code>{
  "finish_options": {
    "matt": "Matt Lamination",
    "gloss": "Gloss Lamination",
    "spot_uv": "Spot UV"
  },
  "sides_options": {
    "single": "Single-sided",
    "double": "Double-sided"
  },
  "pricing": {
    "finish": {
      "matt": 0.00,
      "gloss": 0.02,
      "spot_uv": 0.05
    },
    "sides": {
      "single": 0.00,
      "double": 0.08
    }
  }
}</code></pre>
        </div>

        <div class="bg-gray-50 dark:bg-gray-800 p-3 rounded border">
            <h5 class="font-medium mb-2">3. Complete Example</h5>
            <pre class="text-xs bg-gray-100 dark:bg-gray-900 p-2 rounded overflow-x-auto"><code>{
  "quantity_pricing": [
    {"min_quantity": 100, "price_per_unit": 0.35},
    {"min_quantity": 500, "price_per_unit": 0.25},
    {"min_quantity": 1000, "price_per_unit": 0.18}
  ],
  "finish_options": {
    "matt": "Matt Lamination",
    "gloss": "Gloss Lamination"
  },
  "sides_options": {
    "single": "Single-sided",
    "double": "Double-sided"
  },
  "pricing": {
    "finish": {
      "matt": 0.00,
      "gloss": 0.02
    },
    "sides": {
      "single": 0.00,
      "double": 0.08
    }
  }
}</code></pre>
        </div>
    </div>

    <div class="bg-amber-50 dark:bg-amber-900/20 p-4 rounded-lg border border-amber-200 dark:border-amber-800">
        <h4 class="font-semibold text-amber-900 dark:text-amber-100 mb-2">Important Notes</h4>
        <ul class="text-amber-800 dark:text-amber-200 space-y-1 text-xs">
            <li>• Use decimal numbers for prices (e.g., 0.25, not "0.25")</li>
            <li>• Use integers for quantities (e.g., 100, not "100")</li>
            <li>• Option keys should match between *_options and pricing sections</li>
            <li>• Quantity pricing should be in ascending order by min_quantity</li>
            <li>• JSON must be valid - use online validators if needed</li>
            <li>• Raw JSON will override structured fields above</li>
        </ul>
    </div>
</div>
