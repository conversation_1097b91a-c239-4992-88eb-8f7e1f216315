<?php

namespace App\Console\Commands;

use App\Models\PaymentSetting;
use App\Services\BillplzService;
use Billplz\API;
use Billplz\Connect;
use Illuminate\Console\Command;

class ValidateBillplzConfig extends Command
{
    protected $signature = 'billplz:validate';
    protected $description = 'Validate Billplz configuration and test API connectivity';

    public function handle()
    {
        $this->info('Billplz Configuration Validator');
        $this->info('================================');

        // Check if Billplz is enabled
        $enabled = PaymentSetting::get('billplz_enabled', false);
        $this->info("Billplz Enabled: " . ($enabled ? 'YES' : 'NO'));

        if (!$enabled) {
            $this->warn('Billplz is disabled. Enable it in the admin panel to use payment features.');
            return;
        }

        // Check configuration
        $sandboxMode = PaymentSetting::get('billplz_sandbox_mode', true);
        $apiKey = PaymentSetting::get('billplz_api_key');
        $collectionId = PaymentSetting::get('billplz_collection_id');
        $xSignatureKey = PaymentSetting::get('billplz_x_signature_key');

        $this->info("Sandbox Mode: " . ($sandboxMode ? 'YES' : 'NO'));
        $this->info("API Key: " . (empty($apiKey) ? 'NOT SET' : 'SET (length: ' . strlen($apiKey) . ')'));
        $this->info("Collection ID: " . (empty($collectionId) ? 'NOT SET' : $collectionId));
        $this->info("X Signature Key: " . (empty($xSignatureKey) ? 'NOT SET' : 'SET (length: ' . strlen($xSignatureKey) . ')'));

        // Validate required fields
        if (empty($apiKey)) {
            $this->error('API Key is required. Please set it in the admin panel.');
            return;
        }

        if (empty($collectionId)) {
            $this->error('Collection ID is required. Please set it in the admin panel.');
            return;
        }

        // Test API connectivity
        $this->info("\nTesting API Connectivity...");
        
        try {
            $connect = new Connect($apiKey);
            $connect->setMode(!$sandboxMode); // true for production, false for staging
            $api = new API($connect);

            // Test by listing collections
            $this->info("Fetching available collections...");
            $collectionsResponse = $api->getCollectionIndex();

            if ($collectionsResponse[0] === 200) {
                $collectionsData = json_decode($collectionsResponse[1], true);
                $this->info("✓ API connection successful!");
                
                if (isset($collectionsData['collections']) && is_array($collectionsData['collections'])) {
                    $this->info("Available collections:");
                    foreach ($collectionsData['collections'] as $collection) {
                        $status = $collection['id'] === $collectionId ? ' (CONFIGURED)' : '';
                        $this->line("  - {$collection['id']}: {$collection['title']} [{$collection['status']}]{$status}");
                    }
                } else {
                    $this->warn("No collections found or unexpected response format");
                }
            } else {
                $this->error("✗ Failed to fetch collections: HTTP {$collectionsResponse[0]} - {$collectionsResponse[1]}");
                
                if ($collectionsResponse[0] === 403) {
                    $this->error("This usually means:");
                    $this->error("1. The API key is invalid");
                    $this->error("2. The API key is for a different environment (production vs sandbox)");
                    $this->error("3. The API key has been revoked");
                } elseif ($collectionsResponse[0] === 401) {
                    $this->error("Authentication failed. Please check your API key.");
                }
                return;
            }

            // Test specific collection access
            $this->info("\nTesting configured collection access...");
            $collectionResponse = $api->getCollection($collectionId);

            if ($collectionResponse[0] === 200) {
                $collectionData = json_decode($collectionResponse[1], true);
                $this->info("✓ Collection access successful!");
                $this->info("Collection: {$collectionData['title']} [{$collectionData['status']}]");
            } else {
                $this->error("✗ Failed to access collection {$collectionId}: HTTP {$collectionResponse[0]} - {$collectionResponse[1]}");
                
                if ($collectionResponse[0] === 404) {
                    $this->error("Collection not found. Please check the Collection ID.");
                } elseif ($collectionResponse[0] === 403) {
                    $this->error("Access denied to this collection. Please check if the API key has access to this collection.");
                }
                return;
            }

            // Test service initialization
            $this->info("\nTesting BillplzService initialization...");
            try {
                $service = new BillplzService();
                $this->info("✓ BillplzService initialized successfully!");
                
                $this->info("✓ All tests passed! Billplz is properly configured.");
                
            } catch (\Exception $e) {
                $this->error("✗ BillplzService initialization failed: " . $e->getMessage());
            }

        } catch (\Exception $e) {
            $this->error("✗ API test failed: " . $e->getMessage());
        }
    }
}
