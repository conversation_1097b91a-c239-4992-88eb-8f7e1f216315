<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('printing_order_id')->constrained()->onDelete('cascade');
            $table->foreignId('printing_product_id')->constrained()->onDelete('cascade');
            $table->integer('quantity');
            $table->decimal('unit_price', 10, 2);
            $table->decimal('total_price', 10, 2);
            $table->json('specifications')->nullable(); // Size, material, finish selected
            $table->json('selected_options')->nullable(); // Customer selections
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index('printing_order_id');
            $table->index('printing_product_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_items');
    }
};
