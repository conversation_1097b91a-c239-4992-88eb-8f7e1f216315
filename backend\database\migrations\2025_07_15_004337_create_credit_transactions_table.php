<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('credit_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('credit_package_id')->nullable()->constrained()->onDelete('set null');
            $table->string('type'); // purchase, usage, refund, bonus, etc.
            $table->integer('credit_amount'); // positive for credit, negative for debit
            $table->decimal('amount_paid', 10, 2)->nullable(); // actual money paid
            $table->string('payment_method')->nullable(); // billplz, manual, etc.
            $table->string('payment_reference')->nullable(); // Billplz bill ID or transaction reference
            $table->string('payment_status')->default('pending'); // pending, completed, failed, refunded
            $table->text('description')->nullable();
            $table->json('metadata')->nullable(); // Additional payment gateway data
            $table->timestamp('processed_at')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'type']);
            $table->index(['payment_status']);
            $table->index(['payment_reference']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('credit_transactions');
    }
};
