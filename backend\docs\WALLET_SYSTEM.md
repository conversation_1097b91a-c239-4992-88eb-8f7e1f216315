# Wallet System Documentation

## Overview

The wallet system has replaced the previous credit-based system, providing a more intuitive monetary approach using Malaysian Ringgit (RM) currency. Users can now top up their wallet with real money and use it for various services.

## Key Features

- **Currency-based**: Uses Malaysian Ringgit (RM) with 2 decimal places
- **1:1 Conversion**: Direct migration from credit system (1 credit = RM 1.00)
- **Strict Balance Control**: Prevents overdrafts and negative balances
- **Multiple Transaction Types**: Support for various wallet operations
- **Billplz Integration**: Seamless payment gateway integration
- **Admin Management**: Comprehensive admin tools for wallet management

## Transaction Types

### 1. Top Up (`top_up`)
- **Purpose**: Add money to wallet
- **Amount**: Positive values only
- **Payment Methods**: Billplz, manual, bank transfer
- **Use Case**: User purchases credit packages to add funds

### 2. Payment (`payment`)
- **Purpose**: Deduct money for purchases/services
- **Amount**: Negative values (automatically converted)
- **Payment Methods**: System (automatic)
- **Use Case**: Printing orders, service payments

### 3. Withdrawal (`withdrawal`)
- **Purpose**: Cash out from wallet
- **Amount**: Negative values
- **Payment Methods**: Bank transfer, PayPal, manual
- **Additional Fields**: `withdrawal_method`, `withdrawal_reference`

### 4. Refund (`refund`)
- **Purpose**: Return money to wallet
- **Amount**: Positive values only
- **Payment Methods**: System, manual
- **Use Case**: Order cancellations, service refunds

### 5. Bonus (`bonus`)
- **Purpose**: Admin-granted wallet bonus
- **Amount**: Positive values only
- **Payment Methods**: System
- **Use Case**: Promotional credits, loyalty rewards

### 6. Adjustment (`adjustment`)
- **Purpose**: Manual balance corrections
- **Amount**: Positive or negative
- **Payment Methods**: Manual
- **Use Case**: Balance corrections, dispute resolutions

## Database Schema

### Users Table
```sql
ALTER TABLE users ADD COLUMN wallet_balance DECIMAL(10,2) DEFAULT 0.00;
```

### Wallet Transactions Table
```sql
CREATE TABLE wallet_transactions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    package_id BIGINT NULL,
    type ENUM('top_up', 'payment', 'withdrawal', 'refund', 'bonus', 'adjustment'),
    amount DECIMAL(10,2) NOT NULL,
    amount_paid DECIMAL(10,2) NULL,
    payment_method VARCHAR(50) NULL,
    payment_reference VARCHAR(255) NULL,
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    description TEXT NULL,
    processed_at TIMESTAMP NULL,
    withdrawal_method VARCHAR(100) NULL,
    withdrawal_reference VARCHAR(255) NULL,
    withdrawal_processed_at TIMESTAMP NULL,
    metadata JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (package_id) REFERENCES credit_packages(id),
    INDEX idx_user_type_status (user_id, type, payment_status),
    INDEX idx_type_created (type, created_at)
);
```

## API Endpoints

### User Endpoints

#### Get Wallet Balance
```http
GET /api/credit/balance
Authorization: Bearer {token}
```

**Response:**
```json
{
    "balance": 150.50,
    "wallet_balance": 150.50,
    "credit_balance": 150.50,
    "formatted_balance": "RM 150.50",
    "currency": "MYR",
    "user_id": 1
}
```

#### Get Transaction History
```http
GET /api/credit/transactions
Authorization: Bearer {token}
```

**Response:**
```json
{
    "transactions": {
        "data": [
            {
                "id": 1,
                "type": "top_up",
                "amount": 100.00,
                "formatted_amount": "RM 100.00",
                "payment_status": "completed",
                "description": "Wallet top-up: Premium Package",
                "created_at": "2025-01-15 10:30:00"
            }
        ]
    }
}
```

#### Get Wallet Statistics
```http
GET /api/credit/statistics
Authorization: Bearer {token}
```

### Admin Endpoints

#### Create Wallet Transaction
```http
POST /api/admin/wallet-transactions
Authorization: Bearer {admin_token}
Content-Type: application/json

{
    "user_id": 1,
    "type": "bonus",
    "amount": 25.00,
    "payment_method": "system",
    "payment_status": "completed",
    "description": "Welcome bonus"
}
```

#### Verify User Balance
```http
POST /api/admin/wallet-transactions/verify-balance
Authorization: Bearer {admin_token}
Content-Type: application/json

{
    "user_id": 1
}
```

#### Get Balance Breakdown
```http
GET /api/admin/wallet-transactions/balance-breakdown?user_id=1
Authorization: Bearer {admin_token}
```

## Service Classes

### WalletTransactionService

Main service class for wallet operations:

```php
use App\Services\WalletTransactionService;

$service = new WalletTransactionService();

// Create transaction
$transaction = $service->createTransaction([
    'user_id' => 1,
    'type' => 'top_up',
    'amount' => 50.00,
    'payment_method' => 'billplz',
    'payment_status' => 'completed'
]);

// Verify balance
$result = $service->verifyAndFixBalance($user);

// Get balance breakdown
$breakdown = $service->getBalanceBreakdown($user);
```

### Key Methods

- `createTransaction(array $data)`: Create new wallet transaction
- `processTransactionData(array $data)`: Apply business rules to transaction data
- `validateTransactionData(array $data)`: Validate transaction against business rules
- `calculateExpectedBalance(User $user)`: Calculate expected balance from transactions
- `verifyAndFixBalance(User $user)`: Verify and correct user balance
- `getBalanceBreakdown(User $user)`: Get detailed balance breakdown

## Model Events

The `WalletTransaction` model automatically handles balance updates:

```php
// On transaction creation (if completed)
static::created(function (WalletTransaction $transaction) {
    $transaction->updateUserBalance();
});

// On status change to completed
static::updated(function (WalletTransaction $transaction) {
    if ($transaction->wasChanged('payment_status') && 
        $transaction->payment_status === 'completed') {
        $transaction->updateUserBalance();
    }
});

// On transaction deletion
static::deleted(function (WalletTransaction $transaction) {
    if ($transaction->payment_status === 'completed') {
        $transaction->user->decrement('wallet_balance', $transaction->amount);
    }
});
```

## Business Rules

### Balance Management
1. **No Overdrafts**: Users cannot spend more than their wallet balance
2. **Automatic Updates**: Balance updates automatically when transactions are completed
3. **Precision**: All amounts use 2 decimal places for currency accuracy
4. **Audit Trail**: All balance changes are tracked through transactions

### Transaction Validation
1. **Type-specific Rules**: Each transaction type has specific validation rules
2. **Amount Signs**: Enforced based on transaction type (positive/negative)
3. **Required Fields**: Validation based on transaction type requirements
4. **User Verification**: All transactions must belong to valid users

### Payment Processing
1. **Status Tracking**: Transactions track payment status throughout lifecycle
2. **Gateway Integration**: Seamless integration with Billplz payment gateway
3. **Metadata Storage**: Additional payment data stored in JSON metadata field
4. **Reference Tracking**: Payment and withdrawal references for audit purposes

## Migration from Credit System

The system includes automatic migration from the previous credit-based system:

1. **Data Migration**: `credit_transactions` → `wallet_transactions`
2. **Balance Migration**: `credit_balance` → `wallet_balance` (1:1 ratio)
3. **Type Mapping**: `purchase` → `top_up`, `usage` → `payment`
4. **Backward Compatibility**: API maintains backward compatibility

## Admin Interface

### Filament Resources

- **WalletTransactionResource**: Complete CRUD interface for wallet transactions
- **Filtering**: Advanced filtering by type, status, amount, date
- **Bulk Operations**: Bulk balance verification and corrections
- **Statistics**: Comprehensive transaction statistics and reporting

### Key Features

- Transaction creation with type-specific forms
- Real-time balance verification
- Bulk balance correction tools
- Comprehensive reporting and analytics
- User wallet management interface

## Testing

Comprehensive test suite includes:

- **Unit Tests**: Service class methods and business logic
- **Feature Tests**: API endpoints and integration points
- **Model Tests**: Database interactions and model events
- **Integration Tests**: Payment gateway and printing system integration

### Running Tests

```bash
# Run all wallet tests
php artisan test --filter=Wallet

# Run specific test classes
php artisan test tests/Feature/WalletTransactionServiceTest.php
php artisan test tests/Feature/WalletApiTest.php
php artisan test tests/Feature/PrintingOrderPaymentTest.php
```

## Security Considerations

1. **Admin-only Creation**: Only admin users can create transactions via API
2. **User Isolation**: Users can only access their own transactions
3. **Balance Verification**: Regular balance verification prevents discrepancies
4. **Audit Logging**: All balance changes are logged for audit purposes
5. **Input Validation**: Strict validation prevents invalid transactions

## Performance Optimizations

1. **Database Indexes**: Optimized indexes for common queries
2. **Eager Loading**: Relationships loaded efficiently
3. **Caching**: Balance calculations cached where appropriate
4. **Batch Operations**: Bulk operations for admin tasks

## Troubleshooting

### Common Issues

1. **Balance Discrepancies**: Use balance verification tools
2. **Failed Payments**: Check payment gateway logs and transaction status
3. **Migration Issues**: Verify data migration completed successfully
4. **Performance**: Check database indexes and query optimization

### Debug Commands

```bash
# Verify user balance
php artisan wallet:fix-transaction-types

# Check migration status
php artisan migrate:status

# Clear application cache
php artisan cache:clear
php artisan config:clear
```
