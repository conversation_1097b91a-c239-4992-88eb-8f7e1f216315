{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AbstractModalHeader from './AbstractModalHeader';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst OffcanvasHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  closeLabel = 'Close',\n  closeButton = false,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'offcanvas-header');\n  return /*#__PURE__*/_jsx(AbstractModalHeader, {\n    ref: ref,\n    ...props,\n    className: classNames(className, bsPrefix),\n    closeLabel: closeLabel,\n    closeButton: closeButton\n  });\n});\nOffcanvasHeader.displayName = 'OffcanvasHeader';\nexport default OffcanvasHeader;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "AbstractModalHeader", "jsx", "_jsx", "OffcanvasHeader", "forwardRef", "bsPrefix", "className", "<PERSON><PERSON><PERSON><PERSON>", "closeButton", "props", "ref", "displayName"], "sources": ["C:/laragon/www/frontend/node_modules/react-bootstrap/esm/OffcanvasHeader.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AbstractModalHeader from './AbstractModalHeader';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst OffcanvasHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  closeLabel = 'Close',\n  closeButton = false,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'offcanvas-header');\n  return /*#__PURE__*/_jsx(AbstractModalHeader, {\n    ref: ref,\n    ...props,\n    className: classNames(className, bsPrefix),\n    closeLabel: closeLabel,\n    closeButton: closeButton\n  });\n});\nOffcanvasHeader.displayName = 'OffcanvasHeader';\nexport default OffcanvasHeader;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,eAAe,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAAC;EACrDC,QAAQ;EACRC,SAAS;EACTC,UAAU,GAAG,OAAO;EACpBC,WAAW,GAAG,KAAK;EACnB,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTL,QAAQ,GAAGN,kBAAkB,CAACM,QAAQ,EAAE,kBAAkB,CAAC;EAC3D,OAAO,aAAaH,IAAI,CAACF,mBAAmB,EAAE;IAC5CU,GAAG,EAAEA,GAAG;IACR,GAAGD,KAAK;IACRH,SAAS,EAAET,UAAU,CAACS,SAAS,EAAED,QAAQ,CAAC;IAC1CE,UAAU,EAAEA,UAAU;IACtBC,WAAW,EAAEA;EACf,CAAC,CAAC;AACJ,CAAC,CAAC;AACFL,eAAe,CAACQ,WAAW,GAAG,iBAAiB;AAC/C,eAAeR,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}