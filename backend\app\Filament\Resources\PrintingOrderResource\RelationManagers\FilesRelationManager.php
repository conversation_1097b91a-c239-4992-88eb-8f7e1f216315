<?php

namespace App\Filament\Resources\PrintingOrderResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\HtmlString;

class FilesRelationManager extends RelationManager
{
    protected static string $relationship = 'files';

    protected static ?string $title = 'Order Files';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('original_name')
                    ->required()
                    ->maxLength(255),

                Forms\Components\Select::make('file_type')
                    ->options([
                        'artwork' => 'Artwork',
                        'reference' => 'Reference',
                        'proof' => 'Proof',
                    ])
                    ->required(),

                Forms\Components\Toggle::make('is_approved')
                    ->label('Approved'),

                Forms\Components\Textarea::make('notes')
                    ->maxLength(65535)
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('original_name')
            ->columns([
                Tables\Columns\TextColumn::make('original_name')
                    ->label('File Name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('file_type_label')
                    ->label('Type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Artwork' => 'primary',
                        'Reference' => 'info',
                        'Proof' => 'warning',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('formatted_file_size')
                    ->label('Size')
                    ->sortable(),

                Tables\Columns\TextColumn::make('dpi')
                    ->label('DPI')
                    ->badge()
                    ->color(fn ($state): string => match (true) {
                        $state >= 300 => 'success',
                        $state >= 150 => 'warning',
                        $state > 0 => 'danger',
                        default => 'gray',
                    })
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_approved')
                    ->label('Approved')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('uploader.name')
                    ->label('Uploaded By')
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Upload Date')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('file_type')
                    ->options([
                        'artwork' => 'Artwork',
                        'reference' => 'Reference',
                        'proof' => 'Proof',
                    ]),

                Tables\Filters\TernaryFilter::make('is_approved')
                    ->label('Approved'),
            ])
            ->headerActions([
                // Files are uploaded through the frontend, so no create action needed
            ])
            ->actions([
                Tables\Actions\Action::make('download')
                    ->label('Download')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('info')
                    ->url(fn ($record): string =>
                        route('admin.orders.files.download', [
                            'orderId' => $record->printing_order_id,
                            'fileId' => $record->id
                        ])
                    )
                    ->openUrlInNewTab(),

                Tables\Actions\EditAction::make(),

                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }
}
