<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\GeneralSetting;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class GeneralSettingsController extends Controller
{
    /**
     * Get all general settings for frontend consumption
     */
    public function index(): JsonResponse
    {
        try {
            // Cache settings for 1 hour to improve performance
            $settings = Cache::remember('general_settings', 3600, function () {
                return $this->getFormattedSettings();
            });

            return response()->json([
                'success' => true,
                'data' => $settings
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve settings',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get a specific setting by key
     */
    public function show(string $key): JsonResponse
    {
        try {
            $value = GeneralSetting::get($key);
            
            if ($value === null) {
                return response()->json([
                    'success' => false,
                    'message' => 'Setting not found'
                ], 404);
            }

            // Format file URLs if needed
            if (in_array($key, ['site_logo', 'site_favicon']) && $value) {
                $value = $this->formatFileUrl($value);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'key' => $key,
                    'value' => $value
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve setting',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Clear settings cache (for admin use)
     */
    public function clearCache(): JsonResponse
    {
        try {
            Cache::forget('general_settings');
            
            return response()->json([
                'success' => true,
                'message' => 'Settings cache cleared successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear cache',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get formatted settings for frontend consumption
     */
    private function getFormattedSettings(): array
    {
        $defaults = GeneralSetting::getDefaults();
        $settings = [];

        foreach ($defaults as $key => $defaultValue) {
            $value = GeneralSetting::get($key, $defaultValue);
            
            // Format file URLs for logo and favicon
            if (in_array($key, ['site_logo', 'site_favicon']) && $value) {
                $value = $this->formatFileUrl($value);
            }
            
            $settings[$key] = $value;
        }

        return $settings;
    }

    /**
     * Format file URL for frontend consumption
     */
    private function formatFileUrl(?string $filePath): ?string
    {
        if (!$filePath) {
            return null;
        }

        // If it's already a full URL, return as is
        if (filter_var($filePath, FILTER_VALIDATE_URL)) {
            return $filePath;
        }

        // Check if file exists in storage
        if (Storage::disk('public')->exists($filePath)) {
            return Storage::disk('public')->url($filePath);
        }

        return null;
    }

    /**
     * Get settings optimized for SEO meta tags
     */
    public function seoMeta(): JsonResponse
    {
        try {
            $settings = Cache::remember('general_settings_seo', 3600, function () {
                return [
                    'title' => GeneralSetting::get('site_meta_title', 'My Website'),
                    'description' => GeneralSetting::get('site_meta_description', 'Welcome to our website'),
                    'keywords' => GeneralSetting::get('site_meta_keywords', ''),
                    'site_name' => GeneralSetting::get('site_name', 'My Website'),
                    'favicon' => $this->formatFileUrl(GeneralSetting::get('site_favicon')),
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $settings
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve SEO settings',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get branding settings (logo, site name)
     */
    public function branding(): JsonResponse
    {
        try {
            $settings = Cache::remember('general_settings_branding', 3600, function () {
                return [
                    'site_name' => GeneralSetting::get('site_name', 'My Website'),
                    'site_logo' => $this->formatFileUrl(GeneralSetting::get('site_logo')),
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $settings
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve branding settings',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }
}
