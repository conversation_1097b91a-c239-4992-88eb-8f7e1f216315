<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\FileUploadController;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Http\Request;

// Root route redirects to admin panel (backend serves only admin functionality)
Route::get('/', function () {
    return redirect('/admin');
})->name('homepage');

// Admin SSO route
Route::middleware(['web'])->get('/admin/sso/{token}', function (Request $request, $token) {
    // Validate token format (should be 64 character hex string)
    if (!preg_match('/^[a-f0-9]{64}$/', $token)) {
        abort(403, 'Invalid token format');
    }

    // Verify the SSO token
    $tokenData = Cache::get("admin_sso_token:{$token}");

    if (!$tokenData || !is_array($tokenData)) {
        abort(403, 'Invalid or expired SSO token');
    }

    // Additional security checks
    if ($tokenData['expires_at'] < now()->timestamp) {
        Cache::forget("admin_sso_token:{$token}");
        abort(403, 'SSO token has expired');
    }

    // Verify IP address (optional - can be disabled for mobile users)
    if (config('auth.sso_verify_ip', false) && $tokenData['ip_address'] !== $request->ip()) {
        Cache::forget("admin_sso_token:{$token}");
        abort(403, 'SSO token used from different IP address');
    }

    // Get the user
    $user = User::find($tokenData['user_id']);

    if (!$user || !$user->isAdmin() || !$user->is_active) {
        Cache::forget("admin_sso_token:{$token}");
        abort(403, 'Unauthorized access');
    }

    // Log the SSO access for security audit
    \Log::info('Admin SSO login successful', [
        'user_id' => $user->id,
        'user_email' => $user->email,
        'ip_address' => $request->ip(),
        'user_agent' => $request->userAgent(),
        'token_created_at' => $tokenData['created_at'],
        'login_at' => now()->timestamp,
    ]);

    // Log the user into the web session
    Auth::guard('web')->login($user);

    // Remove the used token (one-time use)
    Cache::forget("admin_sso_token:{$token}");

    // Redirect to admin panel using absolute URL
    $baseUrl = $request->getSchemeAndHttpHost();
    return redirect("{$baseUrl}/admin");
})->name('admin.sso');

// Admin file download routes (for Filament admin panel)
Route::middleware(['web'])->prefix('admin')->group(function () {
    Route::get('/orders/{orderId}/files/{fileId}/download', [FileUploadController::class, 'downloadFile'])
        ->name('admin.orders.files.download');
    Route::get('/orders/{orderId}/files/download-all', [FileUploadController::class, 'downloadAllFiles'])
        ->name('admin.orders.files.download-all');
});

// Public file serving route with CORS support
Route::middleware([\App\Http\Middleware\CorsMiddleware::class])->get('/files/{orderId}/{fileId}', [FileUploadController::class, 'serveFile'])
    ->name('files.serve');
