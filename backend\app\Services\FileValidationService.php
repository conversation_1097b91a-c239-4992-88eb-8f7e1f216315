<?php

namespace App\Services;

use App\Models\FileUploadSetting;
use App\Models\OrderFile;
use App\Models\PrintingOrder;
use App\Models\User;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;

class FileValidationService
{
    /**
     * Validate an uploaded file against current settings
     */
    public function validateFile(UploadedFile $file): array
    {
        $errors = [];
        $warnings = [];

        // Validate file type
        $typeValidation = $this->validateFileType($file);
        if (!$typeValidation['valid']) {
            $errors[] = $typeValidation['message'] ?? 'File type is not supported';
        }

        // Validate file size
        $sizeValidation = $this->validateFileSize($file);
        if (!$sizeValidation['valid']) {
            $errors[] = $sizeValidation['message'] ?? 'File size exceeds the allowed limit';
        }

        // Validate dimensions for images
        if ($this->isImage($file)) {
            $dimensionValidation = $this->validateDimensions($file);
            if (!$dimensionValidation['valid']) {
                $message = $dimensionValidation['message'] ?? 'Image dimensions validation failed';
                if (($dimensionValidation['type'] ?? 'error') === 'error') {
                    $errors[] = $message;
                } else {
                    $warnings[] = $message;
                }
            }

            // Validate DPI for images
            $dpiValidation = $this->validateDPI($file);
            if (!$dpiValidation['valid']) {
                $message = $dpiValidation['message'] ?? 'Image quality validation failed';
                if (($dpiValidation['type'] ?? 'error') === 'error') {
                    $errors[] = $message;
                } else {
                    $warnings[] = $message;
                }
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings,
            'message' => empty($errors) ? null : implode('. ', $errors)
        ];
    }

    /**
     * Validate file type against allowed types
     */
    private function validateFileType(UploadedFile $file): array
    {
        $allowedTypes = FileUploadSetting::getTyped('allowed_file_types', ['pdf', 'png', 'jpg', 'jpeg']);

        // Ensure allowed_file_types is always an array
        if (!is_array($allowedTypes)) {
            if (is_string($allowedTypes)) {
                $allowedTypes = json_decode($allowedTypes, true) ?: ['pdf', 'png', 'jpg', 'jpeg'];
            } else {
                $allowedTypes = ['pdf', 'png', 'jpg', 'jpeg'];
            }
        }

        $fileExtension = strtolower($file->getClientOriginalExtension());

        if (!in_array($fileExtension, $allowedTypes)) {
            $allowedTypesText = implode(', ', array_map('strtoupper', $allowedTypes));
            return [
                'valid' => false,
                'message' => "File type '{$fileExtension}' is not supported. Please use one of these formats: {$allowedTypesText}. Make sure your file has the correct extension."
            ];
        }

        return ['valid' => true];
    }

    /**
     * Validate file size against maximum allowed size
     */
    private function validateFileSize(UploadedFile $file): array
    {
        $maxSizeMB = FileUploadSetting::get('max_file_size_mb', 50);
        $maxSizeBytes = $maxSizeMB * 1024 * 1024;

        if ($file->getSize() > $maxSizeBytes) {
            return [
                'valid' => false,
                'message' => "File is too large ({$this->formatFileSize($file->getSize())}). Maximum allowed size is {$maxSizeMB}MB. Please compress your file or use a smaller image resolution."
            ];
        }

        return ['valid' => true];
    }

    /**
     * Validate image dimensions
     */
    private function validateDimensions(UploadedFile $file): array
    {
        if (!FileUploadSetting::get('enable_dimension_validation', true)) {
            return ['valid' => true];
        }

        try {
            $imageSize = getimagesize($file->getPathname());
            if (!$imageSize) {
                return ['valid' => true]; // Skip validation if we can't get dimensions
            }

            $width = $imageSize[0];
            $height = $imageSize[1];

            $minWidth = FileUploadSetting::get('min_width_px', 100);
            $minHeight = FileUploadSetting::get('min_height_px', 100);
            $maxWidth = FileUploadSetting::get('max_width_px', 10000);
            $maxHeight = FileUploadSetting::get('max_height_px', 10000);

            if ($width < $minWidth || $height < $minHeight) {
                return [
                    'valid' => false,
                    'type' => 'error',
                    'message' => "Image is too small ({$width}×{$height} pixels). Minimum size required is {$minWidth}×{$minHeight} pixels for good print quality. Please use a higher resolution image."
                ];
            }

            if ($width > $maxWidth || $height > $maxHeight) {
                return [
                    'valid' => false,
                    'type' => 'error',
                    'message' => "Image is too large ({$width}×{$height} pixels). Maximum allowed size is {$maxWidth}×{$maxHeight} pixels. Please resize your image or compress it."
                ];
            }

        } catch (\Exception $e) {
            Log::warning('Failed to validate image dimensions: ' . $e->getMessage());
        }

        return ['valid' => true];
    }

    /**
     * Validate DPI for images
     */
    private function validateDPI(UploadedFile $file): array
    {
        if (!FileUploadSetting::get('enable_dpi_validation', true)) {
            return ['valid' => true];
        }

        $dpi = $this->extractDPI($file);
        if (!$dpi) {
            return ['valid' => true]; // Skip validation if we can't detect DPI
        }

        $minDPI = FileUploadSetting::get('min_dpi_requirement', 300);
        $warningThreshold = FileUploadSetting::get('dpi_warning_threshold', 150);

        if ($dpi < $warningThreshold) {
            return [
                'valid' => false,
                'type' => 'error',
                'message' => "Image resolution is too low ({$dpi} DPI) for professional printing. Please use an image with at least {$minDPI} DPI for crisp, high-quality prints. You can increase DPI by using a higher resolution image or scanning at a higher setting."
            ];
        }

        if ($dpi < $minDPI) {
            return [
                'valid' => false,
                'type' => 'warning',
                'message' => "Image resolution ({$dpi} DPI) is below the recommended {$minDPI} DPI for optimal print quality. While this will still print, you may notice some pixelation. Consider using a higher resolution image for best results."
            ];
        }

        return ['valid' => true];
    }

    /**
     * Extract DPI from image file
     */
    private function extractDPI(UploadedFile $file): ?int
    {
        if (!FileUploadSetting::get('auto_dpi_detection', true)) {
            return null;
        }

        try {
            // Try to get DPI from EXIF data
            if (function_exists('exif_read_data') && in_array($file->getMimeType(), ['image/jpeg', 'image/tiff'])) {
                $exif = @exif_read_data($file->getPathname());
                if ($exif && isset($exif['XResolution'])) {
                    return $this->parseDpi($exif['XResolution']);
                }
            }

            // Try to get DPI from image info
            $imageSize = getimagesize($file->getPathname());
            if ($imageSize && isset($imageSize['channels'])) {
                // This is a basic estimation - real DPI detection would need more sophisticated methods
                return $this->estimateDpi($imageSize);
            }

        } catch (\Exception $e) {
            Log::warning('Failed to extract DPI: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Parse DPI value from EXIF data
     */
    private function parseDpi($dpiValue): ?int
    {
        if (is_string($dpiValue) && strpos($dpiValue, '/') !== false) {
            $parts = explode('/', $dpiValue);
            if (count($parts) === 2 && $parts[1] != 0) {
                return (int) ($parts[0] / $parts[1]);
            }
        }

        return is_numeric($dpiValue) ? (int) $dpiValue : null;
    }

    /**
     * Estimate DPI based on image dimensions (basic estimation)
     */
    private function estimateDpi(array $imageSize): int
    {
        $width = $imageSize[0];
        $height = $imageSize[1];

        // Basic estimation based on common print sizes
        // This is a rough estimate and may not be accurate
        if ($width >= 2400 && $height >= 3000) {
            return 300; // Likely high-res print quality
        } elseif ($width >= 1200 && $height >= 1500) {
            return 150; // Medium quality
        } else {
            return 72; // Web quality
        }
    }

    /**
     * Check if file is an image
     */
    private function isImage(UploadedFile $file): bool
    {
        return in_array($file->getMimeType(), [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/tiff',
            'image/svg+xml',
            'image/webp'
        ]);
    }

    /**
     * Format file size for display
     */
    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Validate total upload size for an order
     */
    public function validateTotalUploadSize(array $files, int $orderId = null): array
    {
        $maxTotalSizeMB = FileUploadSetting::get('max_total_upload_size_mb', 200);
        $maxTotalSizeBytes = $maxTotalSizeMB * 1024 * 1024;

        $totalSize = 0;
        foreach ($files as $file) {
            $totalSize += $file->getSize();
        }

        // TODO: Add existing files size for the order if $orderId is provided

        if ($totalSize > $maxTotalSizeBytes) {
            return [
                'valid' => false,
                'message' => "Total upload size ({$this->formatFileSize($totalSize)}) exceeds maximum allowed ({$maxTotalSizeMB}MB)"
            ];
        }

        return ['valid' => true];
    }

    /**
     * Validate number of files per order
     */
    public function validateFileCount(array $files, int $orderId = null): array
    {
        $maxFiles = FileUploadSetting::get('max_files_per_order', 10);
        $fileCount = count($files);

        // TODO: Add existing files count for the order if $orderId is provided

        if ($fileCount > $maxFiles) {
            return [
                'valid' => false,
                'message' => "Number of files ({$fileCount}) exceeds maximum allowed ({$maxFiles})"
            ];
        }

        return ['valid' => true];
    }

    /**
     * Validate file for re-upload
     */
    public function validateFileForReUpload(UploadedFile $file, OrderFile $originalFile): array
    {
        // Validate basic file properties
        $basicValidation = $this->validateFile($file);
        if (!$basicValidation['valid']) {
            return $basicValidation;
        }

        // Validate file type matches original (optional - can be configurable)
        $allowSameTypeOnly = FileUploadSetting::get('reupload_same_type_only', false);
        if ($allowSameTypeOnly) {
            $newExtension = strtolower($file->getClientOriginalExtension());
            $originalExtension = strtolower(pathinfo($originalFile->original_name, PATHINFO_EXTENSION));

            if ($newExtension !== $originalExtension) {
                return [
                    'valid' => false,
                    'message' => "File type mismatch. Your original file was {$originalExtension} format, but you're trying to upload a {$newExtension} file. Please upload a file with the same format as the original, or contact support if you need to change file types."
                ];
            }
        }

        // Validate file type category matches (artwork, reference, proof)
        // This ensures users can't change the purpose of the file during re-upload
        $maintainFileType = FileUploadSetting::get('reupload_maintain_file_type', true);
        if ($maintainFileType) {
            // This will be validated in the controller where we have access to the file_type parameter
        }

        return ['valid' => true];
    }

    /**
     * Validate re-upload reason
     */
    public function validateReUploadReason(string $reason = null): array
    {
        $requireReason = FileUploadSetting::get('reupload_require_reason', true);

        if ($requireReason && empty($reason)) {
            return [
                'valid' => false,
                'message' => 'Please provide a reason for replacing this file. This helps us track changes and ensure quality. For example: "Fixed color issues", "Higher resolution version", or "Corrected text".'
            ];
        }

        if ($reason && strlen($reason) > 500) {
            return [
                'valid' => false,
                'message' => 'Reason is too long (' . strlen($reason) . ' characters). Please keep it under 500 characters. Focus on the main changes you made to the file.'
            ];
        }

        return ['valid' => true];
    }

    /**
     * Validate if file can be re-uploaded based on order status
     */
    public function validateOrderStatusForReUpload(PrintingOrder $order): array
    {
        $allowedStatuses = FileUploadSetting::getTyped('reupload_allowed_statuses', [
            'pending', 'confirmed', 'in_production'
        ]);

        if (!in_array($order->status, $allowedStatuses)) {
            $statusLabel = $order->status_label ?? ucfirst(str_replace('_', ' ', $order->status));
            return [
                'valid' => false,
                'message' => "Cannot replace files because your order is currently '{$statusLabel}'. File changes are only allowed for orders that are still being prepared. If you need to make changes, please contact our support team."
            ];
        }

        return ['valid' => true];
    }

    /**
     * Validate re-upload permissions
     */
    public function validateReUploadPermissions(User $user, PrintingOrder $order, OrderFile $file): array
    {
        // Check if user owns the order
        if ($order->user_id !== $user->id && !$user->isAdmin()) {
            return [
                'valid' => false,
                'message' => 'Access denied. You can only modify files for your own orders. If you believe this is an error, please contact support.'
            ];
        }

        // Check if file belongs to the order
        if ($file->printing_order_id !== $order->id) {
            return [
                'valid' => false,
                'message' => 'File mismatch error. This file doesn\'t belong to the selected order. Please refresh the page and try again.'
            ];
        }

        // Check if file is current version
        if (!$file->is_current_version) {
            return [
                'valid' => false,
                'message' => 'Cannot replace an old version of a file. Please make sure you\'re working with the most recent version. Refresh the page to see the latest files.'
            ];
        }

        return ['valid' => true];
    }
}
