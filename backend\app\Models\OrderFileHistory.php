<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderFileHistory extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_file_id',
        'printing_order_id',
        'action_type',
        'previous_file_path',
        'previous_file_name',
        'previous_original_name',
        'previous_file_size',
        'previous_mime_type',
        'new_file_path',
        'new_file_name',
        'new_original_name',
        'new_file_size',
        'new_mime_type',
        're_upload_reason',
        'performed_by',
        'metadata',
        'notes',
    ];

    protected function casts(): array
    {
        return [
            'metadata' => 'array',
        ];
    }

    // Action type constants
    const ACTION_UPLOADED = 'uploaded';
    const ACTION_RE_UPLOADED = 're_uploaded';
    const ACTION_DELETED = 'deleted';
    const ACTION_APPROVED = 'approved';
    const ACTION_REJECTED = 'rejected';

    /**
     * Get the order file this history belongs to
     */
    public function orderFile(): BelongsTo
    {
        return $this->belongsTo(OrderFile::class);
    }

    /**
     * Get the printing order this history belongs to
     */
    public function printingOrder(): BelongsTo
    {
        return $this->belongsTo(PrintingOrder::class);
    }

    /**
     * Get the user who performed the action
     */
    public function performedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'performed_by');
    }

    /**
     * Get all action types
     */
    public static function getActionTypes(): array
    {
        return [
            self::ACTION_UPLOADED => 'Uploaded',
            self::ACTION_RE_UPLOADED => 'Re-uploaded',
            self::ACTION_DELETED => 'Deleted',
            self::ACTION_APPROVED => 'Approved',
            self::ACTION_REJECTED => 'Rejected',
        ];
    }

    /**
     * Get action type label
     */
    public function getActionTypeLabelAttribute()
    {
        return self::getActionTypes()[$this->action_type] ?? $this->action_type;
    }

    /**
     * Create a history record for file upload
     */
    public static function createUploadHistory(OrderFile $orderFile, User $user, array $metadata = []): self
    {
        return self::create([
            'order_file_id' => $orderFile->id,
            'printing_order_id' => $orderFile->printing_order_id,
            'action_type' => self::ACTION_UPLOADED,
            'new_file_path' => $orderFile->file_path,
            'new_file_name' => $orderFile->file_name,
            'new_original_name' => $orderFile->original_name,
            'new_file_size' => $orderFile->file_size,
            'new_mime_type' => $orderFile->mime_type,
            'performed_by' => $user->id,
            'metadata' => $metadata,
        ]);
    }

    /**
     * Create a history record for file re-upload
     */
    public static function createReUploadHistory(
        OrderFile $newFile, 
        OrderFile $previousFile, 
        User $user, 
        string $reason = null,
        array $metadata = []
    ): self {
        return self::create([
            'order_file_id' => $newFile->id,
            'printing_order_id' => $newFile->printing_order_id,
            'action_type' => self::ACTION_RE_UPLOADED,
            'previous_file_path' => $previousFile->file_path,
            'previous_file_name' => $previousFile->file_name,
            'previous_original_name' => $previousFile->original_name,
            'previous_file_size' => $previousFile->file_size,
            'previous_mime_type' => $previousFile->mime_type,
            'new_file_path' => $newFile->file_path,
            'new_file_name' => $newFile->file_name,
            'new_original_name' => $newFile->original_name,
            'new_file_size' => $newFile->file_size,
            'new_mime_type' => $newFile->mime_type,
            're_upload_reason' => $reason,
            'performed_by' => $user->id,
            'metadata' => $metadata,
        ]);
    }

    /**
     * Get formatted file size for previous file
     */
    public function getFormattedPreviousFileSizeAttribute()
    {
        if (!$this->previous_file_size) {
            return null;
        }

        $bytes = $this->previous_file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get formatted file size for new file
     */
    public function getFormattedNewFileSizeAttribute()
    {
        if (!$this->new_file_size) {
            return null;
        }

        $bytes = $this->new_file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }
}
