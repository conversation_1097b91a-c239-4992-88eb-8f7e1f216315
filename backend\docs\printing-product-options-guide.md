# Printing Product Options & Pricing Guide

## Overview

The enhanced Options & Pricing section in the Printing Product admin form provides a user-friendly interface for configuring complex pricing structures without requiring manual JSON editing.

## Features

### 1. Structured Form Interface
- **Quantity Pricing Tiers**: Visual interface for setting up quantity-based pricing
- **Product Options**: Easy management of customizable options (finish, sides, paper type, etc.)
- **Automatic JSON Generation**: The system automatically converts structured data to the required JSON format

### 2. Backward Compatibility
- All existing JSON configurations continue to work
- Raw JSON editing is still available for advanced users
- Automatic conversion between structured fields and JSON format

## How to Use

### Quantity Pricing Tab

1. **Add Pricing Tiers**: Click "Add Pricing Tier" to create quantity-based pricing
2. **Set Minimum Quantity**: Enter the minimum order quantity for this tier
3. **Set Price per Unit**: Enter the price per unit for this quantity range
4. **Reorder Tiers**: Drag and drop to reorder pricing tiers

**Example**: 
- 100+ units: RM 0.25 each
- 500+ units: RM 0.20 each  
- 1000+ units: RM 0.15 each

### Product Options Tab

1. **Add Product Option**: Click "Add Product Option" to create a new customizable option
2. **Option Name**: Internal name (e.g., "finish", "sides") - used in the system
3. **Display Label**: User-friendly label shown to customers
4. **Add Choices**: For each option, add available choices:
   - **Value**: Internal value (e.g., "matt", "gloss")
   - **Display Label**: Customer-facing label (e.g., "Matt Lamination")
   - **Price Modifier**: Additional cost for this choice (0 for no extra cost)

**Example Options**:
- **Finish Type**: Matt (RM 0.00), Gloss (+RM 0.02), Spot UV (+RM 0.05)
- **Sides**: Single-sided (RM 0.00), Double-sided (+RM 0.08)

### Advanced JSON Tab

For advanced users who prefer direct JSON editing:
- Edit the raw JSON structure directly
- Includes helpful examples and syntax guide
- JSON validation ensures proper format
- Raw JSON overrides structured fields when provided

## Data Structure

The system maintains the same JSON structure used by the pricing calculation logic:

```json
{
  "quantity_pricing": [
    {"min_quantity": 100, "price_per_unit": 0.25},
    {"min_quantity": 500, "price_per_unit": 0.20}
  ],
  "finish_options": {
    "matt": "Matt Lamination",
    "gloss": "Gloss Lamination"
  },
  "pricing": {
    "finish": {
      "matt": 0.00,
      "gloss": 0.02
    }
  }
}
```

## Validation

The form includes comprehensive validation:
- Quantity tiers must have valid minimum quantities (integers ≥ 1)
- Price per unit must be numeric and ≥ 0
- JSON syntax validation for raw JSON input
- Required field validation for all essential data

## Migration from Old System

Existing products with JSON configurations will automatically populate the structured fields when editing. No data migration is required.

## Tips for Best Practices

1. **Quantity Tiers**: Order from lowest to highest minimum quantity
2. **Option Names**: Use consistent, descriptive names (snake_case recommended)
3. **Price Modifiers**: Use 0 for default options, positive values for premium options
4. **Testing**: Use the price calculation API to verify your configurations work correctly

## Troubleshooting

- **Fields not populating**: Check that existing JSON follows the expected structure
- **Validation errors**: Ensure all required fields are filled and numeric values are valid
- **JSON errors**: Use the validation messages to identify syntax issues
- **Price calculation issues**: Verify option names match between options and pricing sections
