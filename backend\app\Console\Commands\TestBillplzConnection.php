<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\PaymentSetting;
use App\Services\BillplzService;
use App\Models\User;
use App\Models\CreditPackage;
use Billplz\API;
use Billplz\Connect;
use Exception;

class TestBillplzConnection extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'billplz:test {--debug : Show detailed debug information}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Billplz API connection and configuration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== BILLPLZ CONFIGURATION VERIFICATION ===');

        // Step 1: Check configuration
        if (!$this->checkConfiguration()) {
            return 1;
        }

        // Step 2: Verify settings match
        $this->verifySettingsConsistency();

        // Step 3: Test API connection
        $this->testApiConnection();

        // Step 4: Test collection access
        $this->testCollectionAccess();

        // Step 5: Provide recommendations
        $this->provideRecommendations();

        $this->info('=== VERIFICATION COMPLETED ===');
    }

    private function verifySettingsConsistency()
    {
        $this->info("\n2. Verifying Settings Consistency...");

        $sandbox = PaymentSetting::get('billplz_sandbox_mode');
        $apiKey = PaymentSetting::get('billplz_api_key');
        $collectionId = PaymentSetting::get('billplz_collection_id');

        $this->table(['Check', 'Current Value', 'Expected', 'Status'], [
            [
                'Environment Mode',
                $sandbox ? 'Sandbox' : 'Production',
                'Should match your Billplz dashboard',
                '⚠'
            ],
            [
                'API Endpoint',
                $sandbox ? 'billplz-sandbox.com' : 'billplz.com',
                'Should match your API key source',
                '⚠'
            ],
            [
                'API Key Format',
                'UUID (' . strlen($apiKey) . ' chars)',
                'UUID format (36 chars)',
                strlen($apiKey) === 36 ? '✓' : '✗'
            ],
            [
                'Collection ID Format',
                strlen($collectionId) . ' chars',
                '8 characters',
                strlen($collectionId) === 8 ? '✓' : '✗'
            ]
        ]);

        $this->warn("\n⚠ IMPORTANT VERIFICATION NEEDED:");
        $this->warn("Please verify the following in your Billplz dashboard:");

        if ($sandbox) {
            $this->info("1. Go to: https://www.billplz-sandbox.com/");
            $this->info("2. Login to your SANDBOX account");
            $this->info("3. Go to Settings → API Keys");
            $this->info("4. Verify your API key starts with: " . substr($apiKey, 0, 8) . "...");
            $this->info("5. Go to Collections and verify collection ID: " . $collectionId);
        } else {
            $this->info("1. Go to: https://www.billplz.com/");
            $this->info("2. Login to your PRODUCTION account");
            $this->info("3. Go to Settings → API Keys");
            $this->info("4. Verify your API key starts with: " . substr($apiKey, 0, 8) . "...");
            $this->info("5. Go to Collections and verify collection ID: " . $collectionId);
        }

        $this->newLine();
        $verified = $this->confirm('Have you verified that the API key and Collection ID exist in the correct Billplz environment?');

        if (!$verified) {
            $this->error('❌ Please verify your settings in Billplz dashboard first.');
            $this->info('Common issues:');
            $this->info('- Using Production API key with Sandbox mode enabled');
            $this->info('- Using Sandbox API key with Sandbox mode disabled');
            $this->info('- Collection ID from different environment');
            return false;
        }

        return true;
    }

    private function provideRecommendations()
    {
        $this->info("\n6. Recommendations...");

        $sandbox = PaymentSetting::get('billplz_sandbox_mode');
        $apiKey = PaymentSetting::get('billplz_api_key');
        $collectionId = PaymentSetting::get('billplz_collection_id');

        $this->info("Based on your configuration:");
        $this->info("- Environment: " . ($sandbox ? 'Sandbox' : 'Production'));
        $this->info("- Dashboard URL: " . ($sandbox ? 'https://www.billplz-sandbox.com/' : 'https://www.billplz.com/'));

        $this->newLine();
        $this->info("If you're still getting 403 Forbidden errors:");

        $this->info("1. VERIFY ENVIRONMENT MATCH:");
        $this->info("   - Sandbox mode: " . ($sandbox ? 'ENABLED' : 'DISABLED'));
        $this->info("   - Your API key should be from: " . ($sandbox ? 'SANDBOX' : 'PRODUCTION') . " dashboard");

        $this->info("2. CHECK ACCOUNT STATUS:");
        $this->info("   - Ensure your Billplz account is verified");
        $this->info("   - Check if there are any account restrictions");

        $this->info("3. VERIFY PERMISSIONS:");
        $this->info("   - API key should have collection and bill creation permissions");
        $this->info("   - Collection should be active and accessible");

        $this->info("4. TRY REGENERATING CREDENTIALS:");
        $this->info("   - Generate a new API key from the correct dashboard");
        $this->info("   - Create a new collection if needed");

        if ($sandbox) {
            $this->info("5. FOR SANDBOX TESTING:");
            $this->info("   - Use test credit cards provided by Billplz");
            $this->info("   - Sandbox transactions are not real payments");
        }
    }

    private function checkConfiguration()
    {
        $this->info("\n1. Checking Configuration...");

        $enabled = PaymentSetting::get('billplz_enabled');
        $sandbox = PaymentSetting::get('billplz_sandbox_mode');
        $apiKey = PaymentSetting::get('billplz_api_key');
        $collectionId = PaymentSetting::get('billplz_collection_id');
        $xSignature = PaymentSetting::get('billplz_x_signature_key');

        $this->table(['Setting', 'Value', 'Status'], [
            ['Billplz Enabled', $enabled ? 'YES' : 'NO', $enabled ? '✓' : '✗'],
            ['Sandbox Mode', $sandbox ? 'YES' : 'NO', '✓'],
            ['API Key', $apiKey ? 'SET (' . strlen($apiKey) . ' chars)' : 'NOT SET', $apiKey ? '✓' : '✗'],
            ['Collection ID', $collectionId ?: 'NOT SET', $collectionId ? '✓' : '✗'],
            ['X Signature Key', $xSignature ? 'SET' : 'NOT SET', $xSignature ? '✓' : '⚠'],
        ]);

        if (!$enabled || !$apiKey || !$collectionId) {
            $this->error('❌ Configuration incomplete! Please configure Billplz settings in admin panel.');
            return false;
        }

        if ($this->option('debug')) {
            $this->info("API Key (first 10 chars): " . substr($apiKey, 0, 10) . '...');
            $this->info("Collection ID: " . $collectionId);
        }

        return true;
    }

    private function testApiConnection()
    {
        $this->info("\n3. Testing API Connection...");

        try {
            $apiKey = PaymentSetting::get('billplz_api_key');
            $sandbox = PaymentSetting::get('billplz_sandbox_mode');

            $this->info("Connecting to: " . ($sandbox ? 'https://www.billplz-sandbox.com/api/' : 'https://www.billplz.com/api/'));

            $connect = new Connect($apiKey);
            $connect->setMode(!$sandbox); // true for production, false for staging
            $api = new API($connect);

            // Test with a simple API call - get collections
            $response = $api->getCollectionIndex();

            if ($response[0] === 200) {
                $this->info('✓ API connection successful');

                $collections = json_decode($response[1], true);
                if (isset($collections['collections'])) {
                    $this->info('✓ Found ' . count($collections['collections']) . ' collections');

                    if ($this->option('debug')) {
                        foreach ($collections['collections'] as $collection) {
                            $this->line("  - {$collection['title']} (ID: {$collection['id']})");
                        }
                    }
                }
            } else {
                $this->error('❌ API connection failed: HTTP ' . $response[0]);

                if ($this->option('debug')) {
                    $this->error('Response: ' . $response[1]);
                }

                // Diagnose the issue
                $this->diagnoseProblem($response[0], $response[1], $apiKey, $sandbox);
                return false;
            }
        } catch (Exception $e) {
            $this->error('❌ API connection error: ' . $e->getMessage());
            return false;
        }

        return true;
    }

    private function testManualCurl($apiKey, $sandbox)
    {
        $this->info("\n--- Manual cURL Test ---");

        $endpoint = $sandbox ? 'https://www.billplz-sandbox.com/api/v3/collections' : 'https://www.billplz.com/api/v3/collections';

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $endpoint);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($ch, CURLOPT_USERPWD, $apiKey . ':');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Accept: application/json',
            'User-Agent: Laravel-Billplz-Test'
        ]);
        curl_setopt($ch, CURLOPT_VERBOSE, true);
        curl_setopt($ch, CURLOPT_STDERR, fopen('php://temp', 'w+'));

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);

        $this->info("Manual cURL Result:");
        $this->info("- HTTP Code: " . $httpCode);
        $this->info("- Response: " . substr($response, 0, 200) . (strlen($response) > 200 ? '...' : ''));

        if ($error) {
            $this->error("- cURL Error: " . $error);
        }

        curl_close($ch);
    }

    private function diagnoseProblem($httpCode, $response, $apiKey, $sandbox)
    {
        $this->info("\n--- Problem Diagnosis ---");

        switch ($httpCode) {
            case 401:
                $this->error("❌ 401 Unauthorized - API key is invalid or expired");
                $this->info("Suggestions:");
                $this->info("1. Verify your API key is correct");
                $this->info("2. Check if you're using sandbox key for sandbox mode");
                $this->info("3. Ensure the API key hasn't expired");
                break;

            case 403:
                $this->error("❌ 403 Forbidden - Access denied");
                $this->info("Possible causes:");
                $this->info("1. API key doesn't have required permissions");
                $this->info("2. Account might be suspended or restricted");
                $this->info("3. IP address might be blocked");
                $this->info("4. Wrong endpoint (sandbox vs production)");
                break;

            case 404:
                $this->error("❌ 404 Not Found - Endpoint not found");
                $this->info("Check if you're using the correct API endpoint");
                break;

            default:
                $this->error("❌ HTTP {$httpCode} - Unexpected error");
                $this->info("Response: " . $response);
        }

        // Additional checks
        $this->info("\nAPI Key Analysis:");
        $this->info("- Length: " . strlen($apiKey) . " characters");
        $this->info("- Format: " . (preg_match('/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/', $apiKey) ? 'UUID format ✓' : 'Non-UUID format'));
        $this->info("- Starts with: " . substr($apiKey, 0, 8) . '...');

        $this->info("\nEndpoint Analysis:");
        $expectedEndpoint = $sandbox ? 'https://www.billplz-sandbox.com/api/' : 'https://www.billplz.com/api/';
        $this->info("- Expected: " . $expectedEndpoint);
        $this->info("- Sandbox Mode: " . ($sandbox ? 'Enabled' : 'Disabled'));
    }

    private function testCollectionAccess()
    {
        $this->info("\n4. Testing Collection Access...");

        try {
            $apiKey = PaymentSetting::get('billplz_api_key');
            $sandbox = PaymentSetting::get('billplz_sandbox_mode');
            $collectionId = PaymentSetting::get('billplz_collection_id');

            $connect = new Connect($apiKey);
            $connect->setMode(!$sandbox);
            $api = new API($connect);

            // Test collection access
            $response = $api->getCollection($collectionId);

            if ($response[0] === 200) {
                $collection = json_decode($response[1], true);
                $this->info('✓ Collection access successful');
                $this->info("Collection: {$collection['title']} (Status: {$collection['status']})");

                if ($collection['status'] !== 'active') {
                    $this->warn('⚠ Collection is not active!');
                    $this->info('Please activate the collection in your Billplz dashboard.');
                }
            } else {
                $this->error('❌ Collection access failed: HTTP ' . $response[0]);

                if ($this->option('debug')) {
                    $this->error('Response: ' . $response[1]);
                }

                if ($response[0] === 404) {
                    $this->error('❌ Collection ID "' . $collectionId . '" not found.');
                    $this->info('Please verify:');
                    $this->info('1. Collection ID exists in your ' . ($sandbox ? 'SANDBOX' : 'PRODUCTION') . ' dashboard');
                    $this->info('2. Collection ID is spelled correctly');
                } elseif ($response[0] === 403) {
                    $this->error('❌ Access denied to collection.');
                    $this->info('This usually means environment mismatch.');
                }
                return false;
            }
        } catch (Exception $e) {
            $this->error('❌ Collection access error: ' . $e->getMessage());
            return false;
        }

        return true;
    }

    private function testBillCreation()
    {
        $this->info("\n4. Testing Bill Creation...");

        try {
            // Get a test user and package
            $user = User::first();
            $package = CreditPackage::where('is_active', true)->first();

            if (!$user || !$package) {
                $this->error('❌ No test user or active package found');
                return false;
            }

            $this->info("Test User: {$user->name} ({$user->email})");
            $this->info("Test Package: {$package->name} (RM {$package->price})");

            $billplzService = new BillplzService();
            $result = $billplzService->createBill($user, $package, [
                'redirect_url' => 'http://localhost:3000/dashboard/credit'
            ]);

            if ($result['success']) {
                $this->info('✓ Bill creation successful!');
                $this->info("Bill ID: {$result['bill_id']}");
                $this->info("Payment URL: {$result['url']}");
                $this->info("Transaction ID: {$result['transaction_id']}");

                if ($this->option('debug')) {
                    $this->info("Full response: " . json_encode($result['data'], JSON_PRETTY_PRINT));
                }
            } else {
                $this->error('❌ Bill creation failed: ' . $result['error']);
                return false;
            }
        } catch (Exception $e) {
            $this->error('❌ Bill creation error: ' . $e->getMessage());
            return false;
        }

        return true;
    }
}
