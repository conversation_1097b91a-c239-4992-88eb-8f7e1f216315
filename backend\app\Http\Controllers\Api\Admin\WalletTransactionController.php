<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\CreateWalletTransactionRequest;
use App\Models\WalletTransaction;
use App\Models\User;
use App\Services\WalletTransactionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class WalletTransactionController extends Controller
{
    protected WalletTransactionService $transactionService;

    public function __construct(WalletTransactionService $transactionService)
    {
        $this->transactionService = $transactionService;
        
        // Ensure only admin users can access these endpoints
        $this->middleware(function ($request, $next) {
            if (!$request->user() || $request->user()->role !== 'admin') {
                return response()->json(['error' => 'Unauthorized. Admin access required.'], 403);
            }
            return $next($request);
        });
    }

    /**
     * Create a new wallet transaction
     */
    public function store(CreateWalletTransactionRequest $request): JsonResponse
    {
        try {
            $transaction = $this->transactionService->createTransaction($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Wallet transaction created successfully',
                'transaction' => [
                    'id' => $transaction->id,
                    'type' => $transaction->type,
                    'user_id' => $transaction->user_id,
                    'amount' => $transaction->amount,
                    'formatted_amount' => $transaction->getFormattedAmount(),
                    'amount_paid' => $transaction->amount_paid,
                    'formatted_amount_paid' => $transaction->getFormattedAmountPaid(),
                    'payment_method' => $transaction->payment_method,
                    'payment_status' => $transaction->payment_status,
                    'description' => $transaction->description,
                    'withdrawal_method' => $transaction->withdrawal_method,
                    'processed_at' => $transaction->processed_at,
                    'withdrawal_processed_at' => $transaction->withdrawal_processed_at,
                    'created_at' => $transaction->created_at,
                ],
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create wallet transaction',
                'error' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Get transaction type configurations
     */
    public function getTransactionTypes(): JsonResponse
    {
        $configs = $this->transactionService->getAllTransactionTypeConfigs();

        return response()->json([
            'success' => true,
            'transaction_types' => $configs,
        ]);
    }

    /**
     * Verify and fix user wallet balance
     */
    public function verifyBalance(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        $user = User::findOrFail($request->user_id);
        $result = $this->transactionService->verifyAndFixBalance($user);

        return response()->json([
            'success' => true,
            'balance_verification' => $result,
        ]);
    }

    /**
     * Get detailed balance breakdown for a user
     */
    public function getBalanceBreakdown(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        $user = User::findOrFail($request->user_id);
        $breakdown = $this->transactionService->getBalanceBreakdown($user);

        return response()->json([
            'success' => true,
            'user_id' => $user->id,
            'current_balance' => $user->wallet_balance,
            'formatted_balance' => $user->getFormattedWalletBalance(),
            'currency' => 'MYR',
            'breakdown' => $breakdown,
        ]);
    }

    /**
     * Bulk verify and fix wallet balances
     */
    public function bulkVerifyBalances(Request $request): JsonResponse
    {
        $request->validate([
            'user_ids' => 'nullable|array',
            'user_ids.*' => 'exists:users,id',
        ]);

        $userIds = $request->input('user_ids');
        
        // If no specific users provided, check all users with transactions
        if (empty($userIds)) {
            $userIds = User::whereHas('walletTransactions')->pluck('id')->toArray();
        }

        $results = [];
        
        foreach ($userIds as $userId) {
            $user = User::find($userId);
            if ($user) {
                $results[] = $this->transactionService->verifyAndFixBalance($user);
            }
        }

        $fixedCount = collect($results)->where('fixed', true)->count();
        $totalCount = count($results);

        return response()->json([
            'success' => true,
            'message' => "Verified {$totalCount} users, fixed {$fixedCount} balances",
            'results' => $results,
        ]);
    }

    /**
     * Get transaction statistics
     */
    public function getStatistics(): JsonResponse
    {
        $stats = DB::table('wallet_transactions')
            ->select('type', 'payment_status')
            ->selectRaw('COUNT(*) as count')
            ->selectRaw('SUM(amount) as total_amount')
            ->selectRaw('SUM(amount_paid) as total_amount_paid')
            ->groupBy('type', 'payment_status')
            ->get()
            ->groupBy('type');

        $summary = [];
        foreach (WalletTransactionService::VALID_TYPES as $type) {
            $typeStats = $stats->get($type, collect());
            
            $summary[$type] = [
                'total_transactions' => $typeStats->sum('count'),
                'completed_transactions' => $typeStats->where('payment_status', 'completed')->sum('count'),
                'pending_transactions' => $typeStats->where('payment_status', 'pending')->sum('count'),
                'total_amount' => round($typeStats->sum('total_amount'), 2),
                'completed_amount' => round($typeStats->where('payment_status', 'completed')->sum('total_amount'), 2),
                'total_amount_paid' => round($typeStats->sum('total_amount_paid'), 2),
                'formatted_total_amount' => 'RM ' . number_format($typeStats->sum('total_amount'), 2),
                'formatted_completed_amount' => 'RM ' . number_format($typeStats->where('payment_status', 'completed')->sum('total_amount'), 2),
            ];
        }

        return response()->json([
            'success' => true,
            'statistics' => $summary,
        ]);
    }

    /**
     * Create a specific transaction type with simplified parameters
     */
    public function createTopUp(Request $request): JsonResponse
    {
        return $this->createSpecificTransaction($request, 'top_up');
    }

    public function createPayment(Request $request): JsonResponse
    {
        return $this->createSpecificTransaction($request, 'payment');
    }

    public function createWithdrawal(Request $request): JsonResponse
    {
        return $this->createSpecificTransaction($request, 'withdrawal');
    }

    public function createRefund(Request $request): JsonResponse
    {
        return $this->createSpecificTransaction($request, 'refund');
    }

    public function createBonus(Request $request): JsonResponse
    {
        return $this->createSpecificTransaction($request, 'bonus');
    }

    public function createAdjustment(Request $request): JsonResponse
    {
        return $this->createSpecificTransaction($request, 'adjustment');
    }

    /**
     * Helper method to create specific transaction types
     */
    protected function createSpecificTransaction(Request $request, string $type): JsonResponse
    {
        $data = $request->all();
        $data['type'] = $type;

        // Create a new request instance with the type set
        $createRequest = new CreateWalletTransactionRequest();
        $createRequest->replace($data);
        $createRequest->setUserResolver($request->getUserResolver());

        return $this->store($createRequest);
    }
}
