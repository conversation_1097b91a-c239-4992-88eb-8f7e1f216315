<x-filament-panels::page>
    <div class="space-y-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h2 class="text-lg font-semibold text-gray-900">Visual Editor</h2>
                    <p class="text-sm text-gray-600">Edit "{{ $record->title }}" using the visual page builder</p>
                </div>
                <div class="flex space-x-2">
                    <a 
                        href="{{ $editorUrl }}" 
                        target="_blank"
                        class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150"
                    >
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Open Visual Editor
                    </a>
                    @if($record->status === 'published')
                    <a
                        href="{{ rtrim(config('app.frontend_url', env('FRONTEND_URL', 'http://localhost:3000')), '/') . '/' . $record->slug }}"
                        target="_blank"
                        class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150"
                    >
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        Preview Page
                    </a>
                    @endif
                </div>
            </div>
            
            <div class="border-t pt-4">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                        <span class="font-medium text-gray-700">Status:</span>
                        <span class="ml-2 px-2 py-1 rounded-full text-xs font-medium
                            @if($record->status === 'published') bg-green-100 text-green-800
                            @elseif($record->status === 'draft') bg-gray-100 text-gray-800
                            @else bg-yellow-100 text-yellow-800
                            @endif">
                            {{ ucfirst($record->status) }}
                        </span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">Slug:</span>
                        <span class="ml-2 font-mono text-gray-600">{{ $record->slug }}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">Last Updated:</span>
                        <span class="ml-2 text-gray-600">{{ $record->updated_at->format('M j, Y g:i A') }}</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Instructions</h3>
            <div class="prose prose-sm text-gray-600">
                <ol>
                    <li>Click "Open Visual Editor" to launch the Puck visual page builder in a new tab</li>
                    <li>Use the drag-and-drop interface to add and arrange content blocks</li>
                    <li>Customize each block using the properties panel on the right</li>
                    <li>Click "Save" in the visual editor to save your changes</li>
                    <li>Return to this page to manage other page settings or preview the result</li>
                </ol>
                <p class="mt-4">
                    <strong>Note:</strong> The visual editor provides a full-screen editing experience with real-time preview. 
                    All changes are automatically saved to this page record.
                </p>
            </div>
        </div>
    </div>
</x-filament-panels::page>
