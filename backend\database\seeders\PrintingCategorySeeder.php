<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PrintingCategory;
use Illuminate\Support\Str;

class PrintingCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Business Cards',
                'description' => 'Professional business cards in various materials and finishes',
                'slug' => 'business-cards',
                'sort_order' => 1,
                'meta_title' => 'Business Cards Printing Services',
                'meta_description' => 'High-quality business card printing with various paper types and finishes',
            ],
            [
                'name' => 'Flyers & Leaflets',
                'description' => 'Eye-catching flyers and leaflets for marketing and promotions',
                'slug' => 'flyers-leaflets',
                'sort_order' => 2,
                'meta_title' => 'Flyers & Leaflets Printing',
                'meta_description' => 'Professional flyer and leaflet printing services for all your marketing needs',
            ],
            [
                'name' => 'Banners & Signage',
                'description' => 'Large format banners and signage for events and advertising',
                'slug' => 'banners-signage',
                'sort_order' => 3,
                'meta_title' => 'Banner & Signage Printing',
                'meta_description' => 'Large format banner and signage printing for indoor and outdoor use',
            ],
            [
                'name' => 'Brochures & Catalogs',
                'description' => 'Multi-page brochures and catalogs with professional binding',
                'slug' => 'brochures-catalogs',
                'sort_order' => 4,
                'meta_title' => 'Brochure & Catalog Printing',
                'meta_description' => 'Professional brochure and catalog printing with various binding options',
            ],
            [
                'name' => 'Stickers & Labels',
                'description' => 'Custom stickers and labels in various shapes and materials',
                'slug' => 'stickers-labels',
                'sort_order' => 5,
                'meta_title' => 'Sticker & Label Printing',
                'meta_description' => 'Custom sticker and label printing in various materials and finishes',
            ],
            [
                'name' => 'Posters',
                'description' => 'High-quality poster printing for events and advertising',
                'slug' => 'posters',
                'sort_order' => 6,
                'meta_title' => 'Poster Printing Services',
                'meta_description' => 'Professional poster printing in various sizes and materials',
            ],
        ];

        foreach ($categories as $category) {
            PrintingCategory::create($category);
        }
    }
}
