<?php

namespace App\Filament\Resources\PrintingCategoryResource\Pages;

use App\Filament\Resources\PrintingCategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPrintingCategories extends ListRecords
{
    protected static string $resource = PrintingCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
