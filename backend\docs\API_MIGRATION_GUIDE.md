# API Migration Guide: Credit System to Wallet System

## Overview

This guide outlines the changes made to the API when migrating from the credit-based system to the wallet-based system. The migration maintains backward compatibility while introducing new wallet-specific features.

## Breaking Changes

### None - Backward Compatibility Maintained

The migration has been designed to maintain full backward compatibility. Existing API clients will continue to work without modifications.

## API Endpoint Changes

### User Balance and Transactions

#### GET `/api/credit/balance`

**Before (Credit System):**
```json
{
    "credit_balance": 150,
    "user_id": 1
}
```

**After (Wallet System):**
```json
{
    "balance": 150.50,
    "wallet_balance": 150.50,
    "credit_balance": 150.50,
    "formatted_balance": "RM 150.50",
    "currency": "MYR",
    "user_id": 1
}
```

**Changes:**
- Added `balance` (primary field)
- Added `wallet_balance` (new field)
- Maintained `credit_balance` (backward compatibility)
- Added `formatted_balance` with currency formatting
- Added `currency` field
- Values now use decimal precision (2 decimal places)

#### GET `/api/credit/transactions`

**Before (Credit System):**
```json
{
    "transactions": {
        "data": [
            {
                "id": 1,
                "type": "purchase",
                "credit_amount": 100,
                "amount_paid": 100.00,
                "formatted_amount_paid": "RM 100.00",
                "payment_method": "billplz",
                "payment_status": "completed",
                "description": "Purchase of Premium Package",
                "package_name": "Premium Package",
                "is_credit": true,
                "is_debit": false,
                "processed_at": "2025-01-15 10:30:00",
                "created_at": "2025-01-15 10:30:00"
            }
        ]
    }
}
```

**After (Wallet System):**
```json
{
    "transactions": {
        "data": [
            {
                "id": 1,
                "type": "top_up",
                "amount": 100.00,
                "credit_amount": 100.00,
                "formatted_amount": "RM 100.00",
                "amount_paid": 100.00,
                "formatted_amount_paid": "RM 100.00",
                "payment_method": "billplz",
                "payment_status": "completed",
                "description": "Wallet top-up: Premium Package (RM 100.00)",
                "package_name": "Premium Package",
                "is_credit": true,
                "is_debit": false,
                "withdrawal_method": null,
                "withdrawal_reference": null,
                "processed_at": "2025-01-15 10:30:00",
                "withdrawal_processed_at": null,
                "created_at": "2025-01-15 10:30:00"
            }
        ]
    }
}
```

**Changes:**
- Transaction types: `purchase` → `top_up`, `usage` → `payment`
- Added `amount` (primary field with decimal precision)
- Maintained `credit_amount` (backward compatibility)
- Added `formatted_amount` with currency formatting
- Added withdrawal-related fields for new transaction types
- Enhanced descriptions with currency formatting

#### GET `/api/credit/statistics`

**Before (Credit System):**
```json
{
    "current_balance": 150,
    "total_purchased": 200,
    "total_used": 50,
    "total_refunded": 0,
    "total_bonus": 0,
    "total_adjustments": 0,
    "total_spent": 200.00,
    "total_amount_paid_deductions": 200,
    "expected_balance": 150,
    "balance_matches": true,
    "recent_transactions": []
}
```

**After (Wallet System):**
```json
{
    "current_balance": 150.50,
    "wallet_balance": 150.50,
    "credit_balance": 150.50,
    "formatted_balance": "RM 150.50",
    "currency": "MYR",
    "total_top_up": 200.00,
    "total_purchased": 200.00,
    "total_payments": 50.00,
    "total_used": 50.00,
    "total_withdrawals": 0.00,
    "total_refunded": 0.00,
    "total_bonus": 0.00,
    "total_adjustments": 0.00,
    "total_spent": 200.00,
    "expected_balance": 150.50,
    "balance_matches": true,
    "recent_transactions": []
}
```

**Changes:**
- Added wallet-specific fields with currency formatting
- Maintained backward compatibility fields
- Added new transaction type totals (`total_withdrawals`)
- Enhanced precision with decimal values
- Added currency information

### Admin Endpoints

#### New Wallet Transaction Endpoints

**Primary Endpoints:**
- `POST /api/admin/wallet-transactions`
- `GET /api/admin/wallet-transactions/types`
- `POST /api/admin/wallet-transactions/verify-balance`
- `GET /api/admin/wallet-transactions/balance-breakdown`
- `POST /api/admin/wallet-transactions/bulk-verify-balances`
- `GET /api/admin/wallet-transactions/statistics`

**Transaction Type Specific Endpoints:**
- `POST /api/admin/wallet-transactions/top-up`
- `POST /api/admin/wallet-transactions/payment`
- `POST /api/admin/wallet-transactions/withdrawal`
- `POST /api/admin/wallet-transactions/refund`
- `POST /api/admin/wallet-transactions/bonus`
- `POST /api/admin/wallet-transactions/adjustment`

**Legacy Compatibility Endpoints:**
All existing `/api/admin/credit-transactions/*` endpoints continue to work and redirect to wallet transaction handlers.

### Payment Endpoints

#### POST `/api/payments/create`

**Before:**
```json
{
    "success": true,
    "bill_id": "abc123",
    "payment_url": "https://billplz.com/bills/abc123",
    "transaction_id": 1
}
```

**After:**
```json
{
    "success": true,
    "message": "Wallet top-up payment created successfully",
    "bill_id": "abc123",
    "payment_url": "https://billplz.com/bills/abc123",
    "transaction_id": 1,
    "package": {
        "id": 1,
        "name": "Premium Package",
        "price": 100.00,
        "formatted_price": "RM 100.00",
        "description": "Premium package description"
    }
}
```

**Changes:**
- Added success message
- Added package information in response
- Enhanced with currency formatting

#### GET `/api/payments/status`

**Before:**
```json
{
    "transaction_id": 1,
    "payment_status": "completed",
    "credit_amount": 100,
    "amount_paid": 100.00,
    "processed_at": "2025-01-15 10:30:00"
}
```

**After:**
```json
{
    "transaction_id": 1,
    "payment_status": "completed",
    "type": "top_up",
    "amount": 100.00,
    "formatted_amount": "RM 100.00",
    "amount_paid": 100.00,
    "formatted_amount_paid": "RM 100.00",
    "processed_at": "2025-01-15 10:30:00",
    "description": "Wallet top-up: Premium Package (RM 100.00)"
}
```

**Changes:**
- Added transaction type
- Added formatted amounts
- Added transaction description
- Enhanced with currency information

### Order Endpoints

#### New Payment-Related Endpoints

**GET `/api/orders/{id}/payment-info`**
```json
{
    "success": true,
    "data": {
        "order_id": 1,
        "order_number": "ORD-2025-001",
        "total_amount": 25.00,
        "formatted_total_amount": "RM 25.00",
        "payment_status": "pending",
        "payment_status_label": "Pending",
        "payment_method": null,
        "payment_reference": null,
        "user_balance": 100.00,
        "formatted_user_balance": "RM 100.00",
        "can_pay": true,
        "balance_sufficient": true,
        "shortfall": 0
    }
}
```

**POST `/api/orders/{id}/pay`**
```json
{
    "success": true,
    "message": "Payment processed successfully",
    "data": {
        "id": 1,
        "payment_status": "paid",
        "status": "confirmed",
        "payment_reference": "WT-123"
    },
    "transaction": {
        "id": 123,
        "amount": -25.00,
        "formatted_amount": "RM -25.00",
        "description": "Payment for printing order #ORD-2025-001"
    }
}
```

## Data Type Changes

### Precision Changes

| Field | Before | After | Notes |
|-------|--------|-------|-------|
| Balance | Integer | Decimal(10,2) | Currency precision |
| Transaction Amount | Integer | Decimal(10,2) | Currency precision |
| Amount Paid | Decimal(8,2) | Decimal(10,2) | Increased precision |

### New Fields

| Field | Type | Description |
|-------|------|-------------|
| `withdrawal_method` | String | Method used for withdrawals |
| `withdrawal_reference` | String | Reference for withdrawal transactions |
| `withdrawal_processed_at` | Timestamp | When withdrawal was processed |
| `formatted_balance` | String | Currency-formatted balance |
| `formatted_amount` | String | Currency-formatted amount |
| `currency` | String | Currency code (MYR) |

## Migration Strategy

### For Existing Clients

1. **No Immediate Changes Required**: All existing API calls continue to work
2. **Gradual Migration**: Update clients to use new fields when convenient
3. **Enhanced Features**: Take advantage of new wallet features as needed

### Recommended Updates

1. **Use Primary Fields**: Prefer `balance` over `credit_balance`, `amount` over `credit_amount`
2. **Currency Formatting**: Use `formatted_*` fields for display purposes
3. **New Transaction Types**: Handle new transaction types (`withdrawal`, `bonus`, `adjustment`)
4. **Enhanced Error Handling**: Handle new validation messages for insufficient balance

### Testing Migration

1. **Verify Backward Compatibility**: Ensure existing API calls return expected data
2. **Test New Features**: Validate new wallet-specific functionality
3. **Balance Verification**: Confirm balance calculations are correct
4. **Payment Flow**: Test complete payment workflows

## Error Handling Changes

### New Error Responses

**Insufficient Balance:**
```json
{
    "success": false,
    "message": "Insufficient wallet balance. You have RM 50.00, but RM 75.00 is required.",
    "required_amount": 75.00,
    "current_balance": 50.00,
    "shortfall": 25.00
}
```

**Enhanced Validation:**
```json
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "amount": ["Amount must be positive for top-up transactions."],
        "user_id": ["User not found."]
    }
}
```

## Best Practices

### For API Consumers

1. **Handle Decimal Precision**: Ensure proper handling of decimal amounts
2. **Currency Display**: Use formatted fields for user-facing displays
3. **Balance Checks**: Implement client-side balance validation
4. **Error Handling**: Handle new error response formats
5. **Transaction Types**: Support new transaction types in UI

### For Developers

1. **Use Service Classes**: Leverage `WalletTransactionService` for business logic
2. **Validate Inputs**: Use provided validation rules and request classes
3. **Handle Events**: Utilize model events for balance updates
4. **Test Thoroughly**: Use provided test suite for validation
5. **Monitor Performance**: Watch for performance impacts with decimal precision

## Support and Troubleshooting

### Common Issues

1. **Balance Discrepancies**: Use admin balance verification tools
2. **Decimal Precision**: Ensure proper decimal handling in client code
3. **Transaction Validation**: Check business rules for transaction creation
4. **Payment Integration**: Verify Billplz integration configuration

### Debug Tools

- Balance verification: `POST /api/admin/wallet-transactions/verify-balance`
- Balance breakdown: `GET /api/admin/wallet-transactions/balance-breakdown`
- Transaction statistics: `GET /api/admin/wallet-transactions/statistics`
- Bulk balance fix: `POST /api/admin/wallet-transactions/bulk-verify-balances`
