<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;

class UpdateProfileRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $user = $this->user();
        $commonRules = $this->getCommonRules();
        
        return [
            'name' => 'sometimes|' . str_replace('required|', '', $commonRules['name_rule']),
            'email' => 'sometimes|required|email|max:255|unique:users,email,' . $user->id,
            'phone' => $commonRules['phone_rule'],
            'bio' => $commonRules['text_rule'],
            'date_of_birth' => $commonRules['date_rule'] . '|before:today',
            'current_password' => 'required_with:password|string',
            'password' => 'nullable|string|min:8|confirmed',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return array_merge(parent::messages(), [
            'email.unique' => 'This email address is already in use by another account.',
            'current_password.required_with' => 'Please enter your current password to change your password.',
            'date_of_birth.before' => 'Date of birth must be in the past.',
            'phone.regex' => 'Please enter a valid phone number.',
        ]);
    }

    /**
     * Fields that are allowed to contain HTML
     */
    protected function getAllowedHtmlFields(): array
    {
        return ['bio'];
    }
}
