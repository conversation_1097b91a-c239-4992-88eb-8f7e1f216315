<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\FileUploadSetting;

class FileUploadSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // File Format Settings
            [
                'key' => 'allowed_file_types',
                'value' => json_encode(['pdf', 'ai', 'eps', 'png', 'jpg', 'jpeg', 'tiff', 'svg']),
                'type' => 'json',
                'description' => 'Allowed file types for upload',
                'is_encrypted' => false,
            ],
            [
                'key' => 'max_file_size_mb',
                'value' => '50',
                'type' => 'integer',
                'description' => 'Maximum file size in MB',
                'is_encrypted' => false,
            ],
            [
                'key' => 'max_total_upload_size_mb',
                'value' => '200',
                'type' => 'integer',
                'description' => 'Maximum total upload size per order in MB',
                'is_encrypted' => false,
            ],
            [
                'key' => 'max_files_per_order',
                'value' => '10',
                'type' => 'integer',
                'description' => 'Maximum number of files per order',
                'is_encrypted' => false,
            ],

            // DPI Settings
            [
                'key' => 'min_dpi_requirement',
                'value' => '300',
                'type' => 'integer',
                'description' => 'Minimum DPI requirement for print-ready files',
                'is_encrypted' => false,
            ],
            [
                'key' => 'dpi_warning_threshold',
                'value' => '150',
                'type' => 'integer',
                'description' => 'DPI threshold for showing warnings',
                'is_encrypted' => false,
            ],
            [
                'key' => 'enable_dpi_validation',
                'value' => 'true',
                'type' => 'boolean',
                'description' => 'Enable DPI validation for uploaded files',
                'is_encrypted' => false,
            ],
            [
                'key' => 'auto_dpi_detection',
                'value' => 'true',
                'type' => 'boolean',
                'description' => 'Enable automatic DPI detection',
                'is_encrypted' => false,
            ],

            // Dimension Settings
            [
                'key' => 'min_width_px',
                'value' => '100',
                'type' => 'integer',
                'description' => 'Minimum width in pixels',
                'is_encrypted' => false,
            ],
            [
                'key' => 'min_height_px',
                'value' => '100',
                'type' => 'integer',
                'description' => 'Minimum height in pixels',
                'is_encrypted' => false,
            ],
            [
                'key' => 'max_width_px',
                'value' => '10000',
                'type' => 'integer',
                'description' => 'Maximum width in pixels',
                'is_encrypted' => false,
            ],
            [
                'key' => 'max_height_px',
                'value' => '10000',
                'type' => 'integer',
                'description' => 'Maximum height in pixels',
                'is_encrypted' => false,
            ],
            [
                'key' => 'enable_dimension_validation',
                'value' => 'true',
                'type' => 'boolean',
                'description' => 'Enable dimension validation for uploaded files',
                'is_encrypted' => false,
            ],

            // Compression Settings
            [
                'key' => 'enable_auto_compression',
                'value' => 'false',
                'type' => 'boolean',
                'description' => 'Enable automatic file compression',
                'is_encrypted' => false,
            ],
            [
                'key' => 'compression_quality',
                'value' => '85',
                'type' => 'integer',
                'description' => 'Compression quality (1-100)',
                'is_encrypted' => false,
            ],
            [
                'key' => 'compression_threshold_mb',
                'value' => '10',
                'type' => 'integer',
                'description' => 'File size threshold for compression in MB',
                'is_encrypted' => false,
            ],
        ];

        foreach ($settings as $setting) {
            FileUploadSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
