<?php

namespace App\Filament\Resources\PrintingOrderResource\Pages;

use App\Filament\Resources\PrintingOrderResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPrintingOrders extends ListRecords
{
    protected static string $resource = PrintingOrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
