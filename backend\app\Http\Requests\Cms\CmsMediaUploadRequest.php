<?php

namespace App\Http\Requests\Cms;

use App\Http\Requests\BaseRequest;

class CmsMediaUploadRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only admin users can upload CMS media
        return auth()->check() && auth()->user()->isAdmin();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'files' => 'required|array|min:1|max:10',
            'files.*' => [
                'required',
                'file',
                'max:10240', // 10MB max file size
                'mimes:jpg,jpeg,png,gif,webp,svg,pdf,doc,docx,txt,mp4,mov,avi',
                'mimetypes:image/jpeg,image/png,image/gif,image/webp,image/svg+xml,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,text/plain,video/mp4,video/quicktime,video/x-msvideo',
            ],
            'upload_type' => 'required|in:image,document,video,general',
            'alt_text' => 'nullable|array',
            'alt_text.*' => 'nullable|string|max:255',
            'description' => 'nullable|array',
            'description.*' => 'nullable|string|max:500',
            'folder' => 'nullable|string|max:100|regex:/^[a-zA-Z0-9\-_\/]+$/',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return array_merge(parent::messages(), [
            'files.required' => 'Please select at least one file to upload.',
            'files.max' => 'You can upload a maximum of 10 files at once.',
            'files.*.required' => 'Each file is required.',
            'files.*.file' => 'Each upload must be a valid file.',
            'files.*.max' => 'Each file must be smaller than 10MB.',
            'files.*.mimes' => 'File type not allowed. Allowed types: JPG, PNG, GIF, WebP, SVG, PDF, DOC, DOCX, TXT, MP4, MOV, AVI.',
            'files.*.mimetypes' => 'Invalid file type detected. Please upload only allowed file types.',
            'upload_type.required' => 'Please specify the upload type.',
            'upload_type.in' => 'Upload type must be image, document, video, or general.',
            'alt_text.*.max' => 'Alt text cannot exceed 255 characters.',
            'description.*.max' => 'Description cannot exceed 500 characters.',
            'folder.regex' => 'Folder name can only contain letters, numbers, hyphens, underscores, and forward slashes.',
        ]);
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return array_merge(parent::attributes(), [
            'files.*' => 'file',
            'upload_type' => 'upload type',
            'alt_text.*' => 'alt text',
            'description.*' => 'description',
        ]);
    }

    /**
     * Fields that are allowed to contain HTML (none for file uploads)
     */
    protected function getAllowedHtmlFields(): array
    {
        return [];
    }

    /**
     * Additional security validation
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            if ($this->hasFile('files')) {
                foreach ($this->file('files') as $index => $file) {
                    // Check for suspicious file names
                    $filename = $file->getClientOriginalName();
                    if ($this->isSuspiciousFilename($filename)) {
                        $validator->errors()->add("files.{$index}", 'Suspicious filename detected.');
                    }

                    // Check file size again (double check)
                    $maxSizeBytes = 10 * 1024 * 1024; // 10MB
                    if ($file->getSize() > $maxSizeBytes) {
                        $validator->errors()->add("files.{$index}", 'File size exceeds maximum allowed size.');
                    }

                    // Basic file content validation
                    if (!$this->isValidFileContent($file)) {
                        $validator->errors()->add("files.{$index}", 'File content appears to be invalid or corrupted.');
                    }

                    // Validate file type against upload_type
                    if (!$this->isValidFileTypeForUploadType($file, $this->upload_type)) {
                        $validator->errors()->add("files.{$index}", "File type doesn't match the specified upload type.");
                    }
                }
            }

            // Validate folder path
            if ($this->folder) {
                if (str_contains($this->folder, '..')) {
                    $validator->errors()->add('folder', 'Invalid folder path detected.');
                }
                if (strlen($this->folder) > 100) {
                    $validator->errors()->add('folder', 'Folder path is too long.');
                }
            }
        });
    }

    /**
     * Check for suspicious filenames
     */
    private function isSuspiciousFilename(string $filename): bool
    {
        $suspiciousPatterns = [
            '/\.(php|phtml|php3|php4|php5|phar|exe|bat|cmd|com|scr|vbs|js|jar)$/i',
            '/\.\./i', // Directory traversal
            '/[<>:"|?*]/i', // Invalid characters
            '/^(con|prn|aux|nul|com[1-9]|lpt[1-9])$/i', // Windows reserved names
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $filename)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Basic file content validation
     */
    private function isValidFileContent($file): bool
    {
        // Check if file is readable
        if (!is_readable($file->getPathname())) {
            return false;
        }

        // Check for minimum file size (avoid empty files)
        if ($file->getSize() < 10) {
            return false;
        }

        return true;
    }

    /**
     * Validate file type against upload type
     */
    private function isValidFileTypeForUploadType($file, string $uploadType): bool
    {
        $mimeType = $file->getMimeType();
        
        switch ($uploadType) {
            case 'image':
                return str_starts_with($mimeType, 'image/');
            case 'document':
                return in_array($mimeType, [
                    'application/pdf',
                    'application/msword',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'text/plain'
                ]);
            case 'video':
                return str_starts_with($mimeType, 'video/');
            case 'general':
                return true; // Allow any valid file type for general uploads
            default:
                return false;
        }
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        parent::prepareForValidation();

        // Set default upload type if not provided
        if (!$this->has('upload_type')) {
            $this->merge(['upload_type' => 'general']);
        }

        // Set default folder if not provided
        if (!$this->has('folder')) {
            $this->merge(['folder' => 'cms/' . date('Y/m')]);
        }

        // Ensure alt_text and description are arrays
        if ($this->has('alt_text') && !is_array($this->alt_text)) {
            $this->merge(['alt_text' => []]);
        }
        if ($this->has('description') && !is_array($this->description)) {
            $this->merge(['description' => []]);
        }
    }
}
