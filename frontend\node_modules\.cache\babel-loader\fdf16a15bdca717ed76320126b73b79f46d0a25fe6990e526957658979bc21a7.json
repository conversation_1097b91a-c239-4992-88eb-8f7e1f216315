{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListUtilityClass(slot) {\n  return generateUtilityClass('MuiList', slot);\n}\nconst listClasses = generateUtilityClasses('MuiList', ['root', 'padding', 'dense', 'subheader']);\nexport default listClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getListUtilityClass", "slot", "listClasses"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/List/listClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListUtilityClass(slot) {\n  return generateUtilityClass('MuiList', slot);\n}\nconst listClasses = generateUtilityClasses('MuiList', ['root', 'padding', 'dense', 'subheader']);\nexport default listClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EACxC,OAAOF,oBAAoB,CAAC,SAAS,EAAEE,IAAI,CAAC;AAC9C;AACA,MAAMC,WAAW,GAAGJ,sBAAsB,CAAC,SAAS,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;AAChG,eAAeI,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}