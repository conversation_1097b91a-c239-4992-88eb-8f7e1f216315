<?php

namespace App\Services;

use App\Models\User;
use App\Models\WalletTransaction;
use App\Models\CreditPackage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class WalletTransactionService
{
    /**
     * Transaction type constants
     */
    const TYPE_TOP_UP = 'top_up';
    const TYPE_PAYMENT = 'payment';
    const TYPE_WITHDRAWAL = 'withdrawal';
    const TYPE_REFUND = 'refund';
    const TYPE_BONUS = 'bonus';
    const TYPE_ADJUSTMENT = 'adjustment';

    /**
     * Valid transaction types
     */
    const VALID_TYPES = [
        self::TYPE_TOP_UP,
        self::TYPE_PAYMENT,
        self::TYPE_WITHDRAWAL,
        self::TYPE_REFUND,
        self::TYPE_BONUS,
        self::TYPE_ADJUSTMENT,
    ];

    /**
     * Create a new wallet transaction with proper business logic
     */
    public function createTransaction(array $data): WalletTransaction
    {
        // Validate transaction type
        if (!in_array($data['type'], self::VALID_TYPES)) {
            throw ValidationException::withMessages([
                'type' => 'Invalid transaction type provided.',
            ]);
        }

        // Apply type-specific business logic
        $processedData = $this->processTransactionData($data);

        // Validate the processed data
        $this->validateTransactionData($processedData);

        // Create transaction in database transaction
        return DB::transaction(function () use ($processedData) {
            $transaction = WalletTransaction::create($processedData);
            
            Log::info('Wallet transaction created', [
                'transaction_id' => $transaction->id,
                'type' => $transaction->type,
                'user_id' => $transaction->user_id,
                'amount' => $transaction->amount,
                'payment_status' => $transaction->payment_status,
            ]);

            return $transaction;
        });
    }

    /**
     * Process transaction data based on type
     */
    public function processTransactionData(array $data): array
    {
        $type = $data['type'];
        
        switch ($type) {
            case self::TYPE_TOP_UP:
                return $this->processTopUpTransaction($data);
            
            case self::TYPE_PAYMENT:
                return $this->processPaymentTransaction($data);
            
            case self::TYPE_WITHDRAWAL:
                return $this->processWithdrawalTransaction($data);
            
            case self::TYPE_REFUND:
                return $this->processRefundTransaction($data);
            
            case self::TYPE_BONUS:
                return $this->processBonusTransaction($data);
            
            case self::TYPE_ADJUSTMENT:
                return $this->processAdjustmentTransaction($data);
            
            default:
                throw ValidationException::withMessages([
                    'type' => 'Unsupported transaction type.',
                ]);
        }
    }

    /**
     * Process top-up transaction (replaces purchase)
     */
    protected function processTopUpTransaction(array $data): array
    {
        // Ensure amount is positive for top-ups
        $data['amount'] = abs($data['amount']);
        
        // Set default payment method if not provided
        if (empty($data['payment_method'])) {
            $data['payment_method'] = 'manual';
        }
        
        // Set default description if not provided
        if (empty($data['description'])) {
            $amount = number_format($data['amount'], 2);
            $data['description'] = "Wallet top-up: RM {$amount}";
        }
        
        // Top-up transactions start as pending by default unless specified
        if (empty($data['payment_status'])) {
            $data['payment_status'] = 'pending';
        }
        
        return $data;
    }

    /**
     * Process payment transaction (replaces usage)
     */
    protected function processPaymentTransaction(array $data): array
    {
        // Ensure amount is negative for payments
        $data['amount'] = -abs($data['amount']);
        
        // Payment transactions don't have amount_paid
        $data['amount_paid'] = null;
        $data['payment_method'] = 'system';
        
        // Set default description if not provided
        if (empty($data['description'])) {
            $amount = number_format(abs($data['amount']), 2);
            $data['description'] = "Wallet payment: RM {$amount}";
        }
        
        // Payment transactions are immediately completed
        $data['payment_status'] = 'completed';
        $data['processed_at'] = now();
        
        return $data;
    }

    /**
     * Process withdrawal transaction
     */
    protected function processWithdrawalTransaction(array $data): array
    {
        // Ensure amount is negative for withdrawals
        $data['amount'] = -abs($data['amount']);
        
        // Set withdrawal method
        if (empty($data['withdrawal_method'])) {
            $data['withdrawal_method'] = 'bank_transfer';
        }
        
        // Set payment method to manual for withdrawals
        $data['payment_method'] = 'manual';
        
        // Set default description if not provided
        if (empty($data['description'])) {
            $amount = number_format(abs($data['amount']), 2);
            $data['description'] = "Wallet withdrawal: RM {$amount}";
        }
        
        // Withdrawals start as pending by default
        if (empty($data['payment_status'])) {
            $data['payment_status'] = 'pending';
        }
        
        return $data;
    }

    /**
     * Process refund transaction
     */
    protected function processRefundTransaction(array $data): array
    {
        // Ensure amount is positive for refunds (adding money back)
        $data['amount'] = abs($data['amount']);
        
        // Set payment method to manual for refunds
        if (empty($data['payment_method'])) {
            $data['payment_method'] = 'manual';
        }
        
        // Set default description if not provided
        if (empty($data['description'])) {
            $amount = number_format($data['amount'], 2);
            $data['description'] = "Wallet refund: RM {$amount}";
        }
        
        // Refunds are typically completed immediately
        if (empty($data['payment_status'])) {
            $data['payment_status'] = 'completed';
            $data['processed_at'] = now();
        }
        
        return $data;
    }

    /**
     * Process bonus transaction
     */
    protected function processBonusTransaction(array $data): array
    {
        // Ensure amount is positive for bonuses
        $data['amount'] = abs($data['amount']);
        
        // Bonus transactions don't have amount_paid
        $data['amount_paid'] = null;
        $data['payment_method'] = 'system';
        
        // Set default description if not provided
        if (empty($data['description'])) {
            $amount = number_format($data['amount'], 2);
            $data['description'] = "Wallet bonus: RM {$amount}";
        }
        
        // Bonus transactions are immediately completed
        $data['payment_status'] = 'completed';
        $data['processed_at'] = now();
        
        return $data;
    }

    /**
     * Process adjustment transaction
     */
    protected function processAdjustmentTransaction(array $data): array
    {
        // Adjustments can be positive or negative - keep original sign
        // No automatic sign change for adjustments
        
        // Adjustment transactions don't typically have amount_paid
        if (!isset($data['amount_paid'])) {
            $data['amount_paid'] = null;
        }
        
        // Set payment method to manual for adjustments
        if (empty($data['payment_method'])) {
            $data['payment_method'] = 'manual';
        }
        
        // Set default description if not provided
        if (empty($data['description'])) {
            $amount = number_format(abs($data['amount']), 2);
            $adjustmentType = $data['amount'] > 0 ? 'positive' : 'negative';
            $data['description'] = "Wallet adjustment ({$adjustmentType}): RM {$amount}";
        }
        
        // Adjustments are typically completed immediately
        if (empty($data['payment_status'])) {
            $data['payment_status'] = 'completed';
            $data['processed_at'] = now();
        }
        
        return $data;
    }

    /**
     * Validate transaction data based on type
     */
    public function validateTransactionData(array $data): void
    {
        $type = $data['type'];

        // Common validations
        if (empty($data['user_id'])) {
            throw ValidationException::withMessages([
                'user_id' => 'User ID is required.',
            ]);
        }

        if (!User::find($data['user_id'])) {
            throw ValidationException::withMessages([
                'user_id' => 'User not found.',
            ]);
        }

        if (empty($data['amount']) || !is_numeric($data['amount'])) {
            throw ValidationException::withMessages([
                'amount' => 'Valid amount is required.',
            ]);
        }

        // Type-specific validations
        switch ($type) {
            case self::TYPE_TOP_UP:
                $this->validateTopUpTransaction($data);
                break;

            case self::TYPE_PAYMENT:
                $this->validatePaymentTransaction($data);
                break;

            case self::TYPE_WITHDRAWAL:
                $this->validateWithdrawalTransaction($data);
                break;

            case self::TYPE_REFUND:
                $this->validateRefundTransaction($data);
                break;

            case self::TYPE_BONUS:
                $this->validateBonusTransaction($data);
                break;

            case self::TYPE_ADJUSTMENT:
                $this->validateAdjustmentTransaction($data);
                break;
        }
    }

    /**
     * Validate top-up transaction
     */
    protected function validateTopUpTransaction(array $data): void
    {
        if ($data['amount'] <= 0) {
            throw ValidationException::withMessages([
                'amount' => 'Top-up transactions must have positive amount.',
            ]);
        }

        // If package is specified, validate it
        if (!empty($data['package_id'])) {
            $package = CreditPackage::find($data['package_id']);
            if (!$package) {
                throw ValidationException::withMessages([
                    'package_id' => 'Package not found.',
                ]);
            }

            if (!$package->is_active) {
                throw ValidationException::withMessages([
                    'package_id' => 'Package is not active.',
                ]);
            }
        }
    }

    /**
     * Validate payment transaction
     */
    protected function validatePaymentTransaction(array $data): void
    {
        if ($data['amount'] >= 0) {
            throw ValidationException::withMessages([
                'amount' => 'Payment transactions must have negative amount.',
            ]);
        }

        // Check if user has sufficient wallet balance
        $user = User::find($data['user_id']);
        $paymentAmount = abs($data['amount']);

        if ($user->wallet_balance < $paymentAmount) {
            throw ValidationException::withMessages([
                'amount' => "Insufficient wallet balance. User has RM " . number_format($user->wallet_balance, 2) . ", but RM " . number_format($paymentAmount, 2) . " required.",
            ]);
        }
    }

    /**
     * Validate withdrawal transaction
     */
    protected function validateWithdrawalTransaction(array $data): void
    {
        if ($data['amount'] >= 0) {
            throw ValidationException::withMessages([
                'amount' => 'Withdrawal transactions must have negative amount.',
            ]);
        }

        // Check if user has sufficient wallet balance
        $user = User::find($data['user_id']);
        $withdrawalAmount = abs($data['amount']);

        if ($user->wallet_balance < $withdrawalAmount) {
            throw ValidationException::withMessages([
                'amount' => "Insufficient wallet balance for withdrawal. User has RM " . number_format($user->wallet_balance, 2) . ", but RM " . number_format($withdrawalAmount, 2) . " requested.",
            ]);
        }
    }

    /**
     * Validate refund transaction
     */
    protected function validateRefundTransaction(array $data): void
    {
        if ($data['amount'] <= 0) {
            throw ValidationException::withMessages([
                'amount' => 'Refund transactions must have positive amount.',
            ]);
        }
    }

    /**
     * Validate bonus transaction
     */
    protected function validateBonusTransaction(array $data): void
    {
        if ($data['amount'] <= 0) {
            throw ValidationException::withMessages([
                'amount' => 'Bonus transactions must have positive amount.',
            ]);
        }
    }

    /**
     * Validate adjustment transaction
     */
    protected function validateAdjustmentTransaction(array $data): void
    {
        if ($data['amount'] == 0) {
            throw ValidationException::withMessages([
                'amount' => 'Adjustment transactions cannot have zero amount.',
            ]);
        }

        // For negative adjustments, check if user has sufficient balance
        if ($data['amount'] < 0) {
            $user = User::find($data['user_id']);
            $adjustmentAmount = abs($data['amount']);

            if ($user->wallet_balance < $adjustmentAmount) {
                throw ValidationException::withMessages([
                    'amount' => "Insufficient wallet balance for negative adjustment. User has RM " . number_format($user->wallet_balance, 2) . ", but RM " . number_format($adjustmentAmount, 2) . " would be deducted.",
                ]);
            }
        }
    }

    /**
     * Get transaction type configuration
     */
    public function getTransactionTypeConfig(string $type): array
    {
        switch ($type) {
            case self::TYPE_TOP_UP:
                return [
                    'label' => 'Top Up',
                    'description' => 'Add money to wallet',
                    'amount_sign' => 'positive',
                    'requires_payment_amount' => true,
                    'requires_payment_method' => true,
                    'default_payment_status' => 'pending',
                    'allows_package_selection' => true,
                ];

            case self::TYPE_PAYMENT:
                return [
                    'label' => 'Payment',
                    'description' => 'Deduct money from wallet for purchases',
                    'amount_sign' => 'negative',
                    'requires_payment_amount' => false,
                    'requires_payment_method' => false,
                    'default_payment_status' => 'completed',
                    'allows_package_selection' => false,
                ];

            case self::TYPE_WITHDRAWAL:
                return [
                    'label' => 'Withdrawal',
                    'description' => 'Cash out from wallet to external account',
                    'amount_sign' => 'negative',
                    'requires_payment_amount' => false,
                    'requires_payment_method' => true,
                    'default_payment_status' => 'pending',
                    'allows_package_selection' => false,
                ];

            case self::TYPE_REFUND:
                return [
                    'label' => 'Refund',
                    'description' => 'Return money to wallet',
                    'amount_sign' => 'positive',
                    'requires_payment_amount' => false,
                    'requires_payment_method' => true,
                    'default_payment_status' => 'completed',
                    'allows_package_selection' => false,
                ];

            case self::TYPE_BONUS:
                return [
                    'label' => 'Bonus',
                    'description' => 'Admin-granted wallet bonus',
                    'amount_sign' => 'positive',
                    'requires_payment_amount' => false,
                    'requires_payment_method' => false,
                    'default_payment_status' => 'completed',
                    'allows_package_selection' => false,
                ];

            case self::TYPE_ADJUSTMENT:
                return [
                    'label' => 'Adjustment',
                    'description' => 'Manual wallet balance correction',
                    'amount_sign' => 'flexible',
                    'requires_payment_amount' => false,
                    'requires_payment_method' => true,
                    'default_payment_status' => 'completed',
                    'allows_package_selection' => false,
                ];

            default:
                return [];
        }
    }

    /**
     * Get all transaction type configurations
     */
    public function getAllTransactionTypeConfigs(): array
    {
        $configs = [];
        foreach (self::VALID_TYPES as $type) {
            $configs[$type] = $this->getTransactionTypeConfig($type);
        }
        return $configs;
    }

    /**
     * Calculate expected wallet balance for a user
     */
    public function calculateExpectedBalance(User $user): float
    {
        $transactions = $user->walletTransactions()
            ->where('payment_status', 'completed')
            ->get();

        $balance = 0.00;

        foreach ($transactions as $transaction) {
            $balance += $transaction->amount;
        }

        return round($balance, 2);
    }

    /**
     * Verify and fix user wallet balance
     */
    public function verifyAndFixBalance(User $user): array
    {
        $currentBalance = $user->wallet_balance;
        $expectedBalance = $this->calculateExpectedBalance($user);

        $result = [
            'user_id' => $user->id,
            'current_balance' => $currentBalance,
            'expected_balance' => $expectedBalance,
            'balance_correct' => abs($currentBalance - $expectedBalance) < 0.01, // Account for floating point precision
            'fixed' => false,
        ];

        if (!$result['balance_correct']) {
            $difference = $expectedBalance - $currentBalance;

            // Use secure method to update balance
            $user->wallet_balance = $expectedBalance;
            $user->save();

            $result['fixed'] = true;

            Log::info('Wallet balance corrected', [
                'user_id' => $user->id,
                'old_balance' => $currentBalance,
                'new_balance' => $expectedBalance,
                'difference' => $difference,
                'reason' => 'Balance verification and correction',
            ]);
        }

        return $result;
    }

    /**
     * Get detailed balance breakdown for a user
     */
    public function getBalanceBreakdown(User $user): array
    {
        $breakdown = [];

        foreach (self::VALID_TYPES as $type) {
            $transactions = $user->walletTransactions()
                ->where('type', $type)
                ->where('payment_status', 'completed')
                ->get();

            $totalAmount = $transactions->sum('amount') ?? 0;
            $count = $transactions->count();

            $breakdown[$type] = [
                'total_amount' => round($totalAmount, 2),
                'transaction_count' => $count,
                'formatted_amount' => 'RM ' . number_format($totalAmount, 2),
            ];
        }

        return $breakdown;
    }
}
