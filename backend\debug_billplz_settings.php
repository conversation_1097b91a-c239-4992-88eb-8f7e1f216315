<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\PaymentSetting;
use Illuminate\Support\Facades\DB;

echo "=== BILLPLZ SETTINGS DIAGNOSTIC ===\n";

// 1. Check current database state
echo "\n1. Current Database State:\n";
$settings = PaymentSetting::whereIn('key', [
    'billplz_enabled',
    'billplz_sandbox_mode', 
    'billplz_api_key',
    'billplz_collection_id',
    'billplz_x_signature_key'
])->get();

foreach ($settings as $setting) {
    echo "- {$setting->key}:\n";
    echo "  Raw Value: {$setting->getAttributes()['value']}\n";
    echo "  Type: {$setting->type}\n";
    echo "  Encrypted: " . ($setting->is_encrypted ? 'YES' : 'NO') . "\n";
    echo "  Processed: " . var_export(PaymentSetting::get($setting->key), true) . "\n";
    echo "\n";
}

// 2. Test sandbox mode specifically
echo "2. Sandbox Mode Analysis:\n";
$sandboxSetting = PaymentSetting::where('key', 'billplz_sandbox_mode')->first();
if ($sandboxSetting) {
    echo "- Exists: YES\n";
    echo "- Raw Value: '{$sandboxSetting->getAttributes()['value']}'\n";
    echo "- Type: {$sandboxSetting->type}\n";
    echo "- Processed Value: " . var_export(PaymentSetting::get('billplz_sandbox_mode'), true) . "\n";
    echo "- Boolean Cast: " . (PaymentSetting::get('billplz_sandbox_mode') ? 'TRUE' : 'FALSE') . "\n";
} else {
    echo "- Exists: NO\n";
    echo "- Default Value: " . var_export(PaymentSetting::get('billplz_sandbox_mode', true), true) . "\n";
}

// 3. Test manual toggle
echo "\n3. Testing Manual Toggle:\n";
$currentValue = PaymentSetting::get('billplz_sandbox_mode', true);
echo "Current Value: " . ($currentValue ? 'TRUE (Sandbox)' : 'FALSE (Production)') . "\n";

// Try to toggle to production mode
echo "Attempting to set to Production Mode (false)...\n";
try {
    PaymentSetting::set('billplz_sandbox_mode', false, 'boolean');
    $newValue = PaymentSetting::get('billplz_sandbox_mode');
    echo "✓ Successfully set to: " . ($newValue ? 'TRUE (Sandbox)' : 'FALSE (Production)') . "\n";
    
    // Verify in database
    $dbValue = DB::table('payment_settings')
        ->where('key', 'billplz_sandbox_mode')
        ->value('value');
    echo "✓ Database value: '{$dbValue}'\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

// 4. Test toggle back to sandbox
echo "\nAttempting to set back to Sandbox Mode (true)...\n";
try {
    PaymentSetting::set('billplz_sandbox_mode', true, 'boolean');
    $newValue = PaymentSetting::get('billplz_sandbox_mode');
    echo "✓ Successfully set to: " . ($newValue ? 'TRUE (Sandbox)' : 'FALSE (Production)') . "\n";
    
    // Verify in database
    $dbValue = DB::table('payment_settings')
        ->where('key', 'billplz_sandbox_mode')
        ->value('value');
    echo "✓ Database value: '{$dbValue}'\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

// 5. Check form data simulation
echo "\n4. Form Data Simulation:\n";
$formData = [
    'billplz_enabled' => true,
    'billplz_sandbox_mode' => false, // This should set to production
    'billplz_api_key' => 'test-production-key',
    'billplz_collection_id' => 'test-collection',
    'billplz_x_signature_key' => 'test-signature'
];

echo "Simulating form submission with production mode...\n";
try {
    // Simulate the save method logic
    $billplzEnabled = isset($formData['billplz_enabled']) ? (bool) $formData['billplz_enabled'] : false;
    $billplzSandboxMode = isset($formData['billplz_sandbox_mode']) ? (bool) $formData['billplz_sandbox_mode'] : true;
    $billplzApiKey = isset($formData['billplz_api_key']) ? (string) $formData['billplz_api_key'] : '';
    $billplzCollectionId = isset($formData['billplz_collection_id']) ? (string) $formData['billplz_collection_id'] : '';
    $billplzXSignatureKey = isset($formData['billplz_x_signature_key']) ? (string) $formData['billplz_x_signature_key'] : '';

    echo "Processing values:\n";
    echo "- Enabled: " . ($billplzEnabled ? 'TRUE' : 'FALSE') . "\n";
    echo "- Sandbox Mode: " . ($billplzSandboxMode ? 'TRUE' : 'FALSE') . "\n";
    echo "- API Key: {$billplzApiKey}\n";
    echo "- Collection ID: {$billplzCollectionId}\n";
    echo "- X Signature Key: {$billplzXSignatureKey}\n";

    // Save settings
    PaymentSetting::set('billplz_enabled', $billplzEnabled, 'boolean');
    PaymentSetting::set('billplz_sandbox_mode', $billplzSandboxMode, 'boolean');
    PaymentSetting::set('billplz_api_key', $billplzApiKey, 'string', true);
    PaymentSetting::set('billplz_collection_id', $billplzCollectionId);
    PaymentSetting::set('billplz_x_signature_key', $billplzXSignatureKey, 'string', true);

    echo "\n✓ Form simulation completed successfully\n";
    
    // Verify final state
    echo "\nFinal verification:\n";
    echo "- Enabled: " . (PaymentSetting::get('billplz_enabled') ? 'TRUE' : 'FALSE') . "\n";
    echo "- Sandbox Mode: " . (PaymentSetting::get('billplz_sandbox_mode') ? 'TRUE (Sandbox)' : 'FALSE (Production)') . "\n";
    
} catch (Exception $e) {
    echo "❌ Form simulation error: " . $e->getMessage() . "\n";
}

echo "\n=== DIAGNOSTIC COMPLETED ===\n";
