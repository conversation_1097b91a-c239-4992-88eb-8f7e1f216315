<?php

namespace App\Filament\Resources;

use App\Filament\Resources\WalletTransactionResource\Pages;
use App\Filament\Resources\WalletTransactionResource\RelationManagers;
use App\Models\WalletTransaction;
use App\Services\WalletTransactionService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class WalletTransactionResource extends Resource
{
    protected static ?string $model = WalletTransaction::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?string $navigationGroup = 'Settings';
    protected static ?string $navigationLabel = 'Wallet Transactions';

    protected static ?string $modelLabel = 'Wallet Transaction';

    protected static ?string $pluralModelLabel = 'Wallet Transactions';

    protected static ?int $navigationSort = 5;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Transaction Details')
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->relationship('user', 'name')
                            ->required()
                            ->searchable()
                            ->columnSpanFull(),

                        Forms\Components\Select::make('type')
                            ->options([
                                'top_up' => 'Top Up',
                                'payment' => 'Payment',
                                'withdrawal' => 'Withdrawal',
                                'refund' => 'Refund',
                                'bonus' => 'Bonus',
                                'adjustment' => 'Adjustment',
                            ])
                            ->required()
                            ->live()
                            ->afterStateUpdated(function (Forms\Set $set, $state) {
                                if ($state) {
                                    $service = new WalletTransactionService();
                                    $config = $service->getTransactionTypeConfig($state);
                                    
                                    // Set default payment status
                                    $set('payment_status', $config['default_payment_status']);
                                    
                                    // Clear fields that might not be applicable
                                    if (!$config['requires_payment_amount']) {
                                        $set('amount_paid', null);
                                    }
                                    
                                    if (!$config['requires_payment_method']) {
                                        $set('payment_method', $state === 'payment' || $state === 'bonus' ? 'system' : null);
                                    }
                                    
                                    if (!$config['allows_package_selection']) {
                                        $set('package_id', null);
                                    }
                                    
                                    // Set processed_at for completed transactions
                                    if ($config['default_payment_status'] === 'completed') {
                                        $set('processed_at', now());
                                    }
                                }
                            })
                            ->helperText(function ($state) {
                                if ($state) {
                                    $service = new WalletTransactionService();
                                    $config = $service->getTransactionTypeConfig($state);
                                    return $config['description'];
                                }
                                return 'Select a transaction type to see specific requirements';
                            }),

                        Forms\Components\Select::make('package_id')
                            ->relationship('package', 'name')
                            ->visible(fn (Forms\Get $get) => in_array($get('type'), ['top_up']))
                            ->helperText('Optional: Select if this transaction is related to a specific package'),

                        Forms\Components\TextInput::make('amount')
                            ->required()
                            ->numeric()
                            ->prefix('RM')
                            ->step(0.01)
                            ->minValue(0.01)
                            ->helperText(function (Forms\Get $get) {
                                $type = $get('type');
                                if ($type) {
                                    $service = new WalletTransactionService();
                                    $config = $service->getTransactionTypeConfig($type);
                                    
                                    switch ($config['amount_sign']) {
                                        case 'positive':
                                            return 'Enter amount to add to wallet (will be positive)';
                                        case 'negative':
                                            return 'Enter amount to deduct from wallet (will be converted to negative)';
                                        case 'flexible':
                                            return 'Enter positive amount to add, or negative to deduct';
                                        default:
                                            return 'Enter the transaction amount';
                                    }
                                }
                                return 'Enter the transaction amount in RM';
                            }),

                        Forms\Components\TextInput::make('amount_paid')
                            ->numeric()
                            ->prefix('RM')
                            ->step(0.01)
                            ->minValue(0)
                            ->visible(function (Forms\Get $get) {
                                $type = $get('type');
                                if ($type) {
                                    $service = new WalletTransactionService();
                                    $config = $service->getTransactionTypeConfig($type);
                                    return $config['requires_payment_amount'];
                                }
                                return true;
                            })
                            ->required(function (Forms\Get $get) {
                                $type = $get('type');
                                if ($type) {
                                    $service = new WalletTransactionService();
                                    $config = $service->getTransactionTypeConfig($type);
                                    return $config['requires_payment_amount'];
                                }
                                return false;
                            }),

                        Forms\Components\Select::make('payment_method')
                            ->options([
                                'billplz' => 'Billplz',
                                'manual' => 'Manual',
                                'system' => 'System',
                                'bank_transfer' => 'Bank Transfer',
                            ])
                            ->visible(function (Forms\Get $get) {
                                $type = $get('type');
                                if ($type) {
                                    $service = new WalletTransactionService();
                                    $config = $service->getTransactionTypeConfig($type);
                                    return $config['requires_payment_method'];
                                }
                                return true;
                            })
                            ->required(function (Forms\Get $get) {
                                $type = $get('type');
                                if ($type) {
                                    $service = new WalletTransactionService();
                                    $config = $service->getTransactionTypeConfig($type);
                                    return $config['requires_payment_method'];
                                }
                                return false;
                            }),

                        Forms\Components\Select::make('withdrawal_method')
                            ->options([
                                'bank_transfer' => 'Bank Transfer',
                                'paypal' => 'PayPal',
                                'manual' => 'Manual',
                            ])
                            ->visible(fn (Forms\Get $get) => $get('type') === 'withdrawal')
                            ->required(fn (Forms\Get $get) => $get('type') === 'withdrawal'),

                        Forms\Components\TextInput::make('payment_reference')
                            ->maxLength(255)
                            ->helperText('Optional: Payment gateway reference or transaction ID'),

                        Forms\Components\TextInput::make('withdrawal_reference')
                            ->maxLength(255)
                            ->visible(fn (Forms\Get $get) => $get('type') === 'withdrawal')
                            ->helperText('Optional: Withdrawal reference or transaction ID'),

                        Forms\Components\Select::make('payment_status')
                            ->options([
                                'pending' => 'Pending',
                                'completed' => 'Completed',
                                'failed' => 'Failed',
                                'refunded' => 'Refunded',
                            ])
                            ->required()
                            ->live()
                            ->afterStateUpdated(function (Forms\Set $set, $state, Forms\Get $get) {
                                if ($state === 'completed') {
                                    $set('processed_at', now());
                                    if ($get('type') === 'withdrawal') {
                                        $set('withdrawal_processed_at', now());
                                    }
                                } else {
                                    $set('processed_at', null);
                                    $set('withdrawal_processed_at', null);
                                }
                            }),

                        Forms\Components\DateTimePicker::make('processed_at')
                            ->visible(fn (Forms\Get $get) => $get('payment_status') === 'completed'),

                        Forms\Components\DateTimePicker::make('withdrawal_processed_at')
                            ->visible(fn (Forms\Get $get) => $get('type') === 'withdrawal' && $get('payment_status') === 'completed'),

                        Forms\Components\Textarea::make('description')
                            ->rows(3)
                            ->columnSpanFull()
                            ->helperText('Optional: Will be auto-generated if left empty'),
                    ])->columns(2),

                Forms\Components\Section::make('Additional Information')
                    ->schema([
                        Forms\Components\KeyValue::make('metadata')
                            ->label('Metadata')
                            ->helperText('Additional payment gateway data in key-value format')
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('User')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'top_up' => 'success',
                        'payment' => 'warning',
                        'withdrawal' => 'danger',
                        'refund' => 'info',
                        'bonus' => 'primary',
                        'adjustment' => 'gray',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'top_up' => 'Top Up',
                        'payment' => 'Payment',
                        'withdrawal' => 'Withdrawal',
                        'refund' => 'Refund',
                        'bonus' => 'Bonus',
                        'adjustment' => 'Adjustment',
                        default => ucfirst($state),
                    }),

                Tables\Columns\TextColumn::make('amount')
                    ->label('Amount')
                    ->money('MYR')
                    ->sortable()
                    ->color(fn ($record): string => $record->amount >= 0 ? 'success' : 'danger'),

                Tables\Columns\TextColumn::make('amount_paid')
                    ->label('Amount Paid')
                    ->money('MYR')
                    ->placeholder('N/A')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('payment_method')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'billplz' => 'success',
                        'manual' => 'warning',
                        'system' => 'info',
                        'bank_transfer' => 'primary',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'billplz' => 'Billplz',
                        'manual' => 'Manual',
                        'system' => 'System',
                        'bank_transfer' => 'Bank Transfer',
                        default => ucfirst($state),
                    })
                    ->toggleable(),

                Tables\Columns\TextColumn::make('payment_status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'completed' => 'success',
                        'pending' => 'warning',
                        'failed' => 'danger',
                        'refunded' => 'info',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => ucfirst($state)),

                Tables\Columns\TextColumn::make('description')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    })
                    ->toggleable(),

                Tables\Columns\TextColumn::make('payment_reference')
                    ->label('Payment Ref')
                    ->placeholder('N/A')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('withdrawal_method')
                    ->label('Withdrawal Method')
                    ->placeholder('N/A')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('processed_at')
                    ->label('Processed At')
                    ->dateTime()
                    ->placeholder('Not processed')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created At')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'top_up' => 'Top Up',
                        'payment' => 'Payment',
                        'withdrawal' => 'Withdrawal',
                        'refund' => 'Refund',
                        'bonus' => 'Bonus',
                        'adjustment' => 'Adjustment',
                    ]),

                Tables\Filters\SelectFilter::make('payment_status')
                    ->options([
                        'pending' => 'Pending',
                        'completed' => 'Completed',
                        'failed' => 'Failed',
                        'refunded' => 'Refunded',
                    ]),

                Tables\Filters\SelectFilter::make('payment_method')
                    ->options([
                        'billplz' => 'Billplz',
                        'manual' => 'Manual',
                        'system' => 'System',
                        'bank_transfer' => 'Bank Transfer',
                    ]),

                Tables\Filters\Filter::make('amount_range')
                    ->form([
                        Forms\Components\TextInput::make('amount_from')
                            ->numeric()
                            ->prefix('RM'),
                        Forms\Components\TextInput::make('amount_to')
                            ->numeric()
                            ->prefix('RM'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['amount_from'],
                                fn (Builder $query, $amount): Builder => $query->where('amount', '>=', $amount),
                            )
                            ->when(
                                $data['amount_to'],
                                fn (Builder $query, $amount): Builder => $query->where('amount', '<=', $amount),
                            );
                    }),

                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from'),
                        Forms\Components\DatePicker::make('created_until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWalletTransactions::route('/'),
            'create' => Pages\CreateWalletTransaction::route('/create'),
            'view' => Pages\ViewWalletTransaction::route('/{record}'),
            'edit' => Pages\EditWalletTransaction::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('payment_status', 'pending')->count();
    }

    public static function getNavigationBadgeColor(): ?string
    {
        $pendingCount = static::getModel()::where('payment_status', 'pending')->count();
        return $pendingCount > 0 ? 'warning' : null;
    }
}
