<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\WalletTransaction;
use App\Models\CreditPackage;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class WalletTransactionTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create([
            'wallet_balance' => 100.00,
        ]);
    }

    /** @test */
    public function it_updates_user_balance_on_completed_transaction()
    {
        $transaction = WalletTransaction::create([
            'user_id' => $this->user->id,
            'type' => 'top_up',
            'amount' => 50.00,
            'payment_method' => 'billplz',
            'payment_status' => 'completed',
            'description' => 'Test top-up',
        ]);

        $this->user->refresh();
        $this->assertEquals(150.00, $this->user->wallet_balance);
    }

    /** @test */
    public function it_does_not_update_balance_for_pending_transactions()
    {
        $transaction = WalletTransaction::create([
            'user_id' => $this->user->id,
            'type' => 'top_up',
            'amount' => 50.00,
            'payment_method' => 'billplz',
            'payment_status' => 'pending',
            'description' => 'Test top-up',
        ]);

        $this->user->refresh();
        $this->assertEquals(100.00, $this->user->wallet_balance); // Should remain unchanged
    }

    /** @test */
    public function it_updates_balance_when_status_changes_to_completed()
    {
        $transaction = WalletTransaction::create([
            'user_id' => $this->user->id,
            'type' => 'top_up',
            'amount' => 50.00,
            'payment_method' => 'billplz',
            'payment_status' => 'pending',
            'description' => 'Test top-up',
        ]);

        $this->user->refresh();
        $this->assertEquals(100.00, $this->user->wallet_balance);

        // Update status to completed
        $transaction->update(['payment_status' => 'completed']);

        $this->user->refresh();
        $this->assertEquals(150.00, $this->user->wallet_balance);
    }

    /** @test */
    public function it_reverses_balance_on_transaction_deletion()
    {
        $transaction = WalletTransaction::create([
            'user_id' => $this->user->id,
            'type' => 'top_up',
            'amount' => 50.00,
            'payment_method' => 'billplz',
            'payment_status' => 'completed',
            'description' => 'Test top-up',
        ]);

        $this->user->refresh();
        $this->assertEquals(150.00, $this->user->wallet_balance);

        // Delete the transaction
        $transaction->delete();

        $this->user->refresh();
        $this->assertEquals(100.00, $this->user->wallet_balance); // Should be reversed
    }

    /** @test */
    public function it_handles_negative_amounts_correctly()
    {
        $transaction = WalletTransaction::create([
            'user_id' => $this->user->id,
            'type' => 'payment',
            'amount' => -30.00,
            'payment_method' => 'system',
            'payment_status' => 'completed',
            'description' => 'Test payment',
        ]);

        $this->user->refresh();
        $this->assertEquals(70.00, $this->user->wallet_balance);
    }

    /** @test */
    public function it_has_correct_relationships()
    {
        $package = CreditPackage::factory()->create();
        
        $transaction = WalletTransaction::create([
            'user_id' => $this->user->id,
            'package_id' => $package->id,
            'type' => 'top_up',
            'amount' => 50.00,
            'payment_method' => 'billplz',
            'payment_status' => 'completed',
            'description' => 'Test top-up',
        ]);

        $this->assertEquals($this->user->id, $transaction->user->id);
        $this->assertEquals($package->id, $transaction->package->id);
    }

    /** @test */
    public function it_provides_formatted_amounts()
    {
        $transaction = WalletTransaction::create([
            'user_id' => $this->user->id,
            'type' => 'top_up',
            'amount' => 50.75,
            'amount_paid' => 50.75,
            'payment_method' => 'billplz',
            'payment_status' => 'completed',
            'description' => 'Test top-up',
        ]);

        $this->assertEquals('RM 50.75', $transaction->getFormattedAmount());
        $this->assertEquals('RM 50.75', $transaction->getFormattedAmountPaid());
    }

    /** @test */
    public function it_identifies_credit_and_debit_transactions()
    {
        $creditTransaction = WalletTransaction::create([
            'user_id' => $this->user->id,
            'type' => 'top_up',
            'amount' => 50.00,
            'payment_method' => 'billplz',
            'payment_status' => 'completed',
            'description' => 'Test top-up',
        ]);

        $debitTransaction = WalletTransaction::create([
            'user_id' => $this->user->id,
            'type' => 'payment',
            'amount' => -30.00,
            'payment_method' => 'system',
            'payment_status' => 'completed',
            'description' => 'Test payment',
        ]);

        $this->assertTrue($creditTransaction->isCredit());
        $this->assertFalse($creditTransaction->isDebit());

        $this->assertFalse($debitTransaction->isCredit());
        $this->assertTrue($debitTransaction->isDebit());
    }

    /** @test */
    public function it_provides_correct_badge_colors()
    {
        $transaction = WalletTransaction::create([
            'user_id' => $this->user->id,
            'type' => 'top_up',
            'amount' => 50.00,
            'payment_method' => 'billplz',
            'payment_status' => 'completed',
            'description' => 'Test top-up',
        ]);

        $this->assertEquals('success', $transaction->getStatusBadgeColor());
        $this->assertEquals('success', $transaction->getTypeBadgeColor());

        $transaction->update(['payment_status' => 'pending']);
        $this->assertEquals('warning', $transaction->getStatusBadgeColor());

        $transaction->update(['type' => 'payment']);
        $this->assertEquals('warning', $transaction->getTypeBadgeColor());
    }

    /** @test */
    public function it_validates_business_rules()
    {
        $transaction = WalletTransaction::create([
            'user_id' => $this->user->id,
            'type' => 'payment',
            'amount' => -30.00,
            'payment_method' => 'system',
            'payment_status' => 'pending', // Not completed yet
            'description' => 'Test payment',
        ]);

        $errors = $transaction->validateBusinessRules();
        $this->assertEmpty($errors); // Should be valid

        // Test insufficient balance scenario
        $transaction->amount = -200.00; // More than user's balance
        $errors = $transaction->validateBusinessRules();
        $this->assertNotEmpty($errors);
        $this->assertStringContains('Insufficient wallet balance', $errors[0]);
    }

    /** @test */
    public function it_applies_business_rules_on_creation()
    {
        // Test that business rules are applied automatically
        $transaction = WalletTransaction::create([
            'user_id' => $this->user->id,
            'type' => 'top_up',
            'amount' => 50.00,
            'payment_method' => 'billplz',
            'payment_status' => 'completed',
        ]);

        // Description should be auto-generated
        $this->assertNotEmpty($transaction->description);
        $this->assertStringContains('Wallet top-up', $transaction->description);
    }

    /** @test */
    public function it_handles_metadata_correctly()
    {
        $metadata = [
            'order_id' => 123,
            'order_type' => 'printing',
            'gateway_reference' => 'bill_abc123',
        ];

        $transaction = WalletTransaction::create([
            'user_id' => $this->user->id,
            'type' => 'payment',
            'amount' => -25.00,
            'payment_method' => 'system',
            'payment_status' => 'completed',
            'description' => 'Order payment',
            'metadata' => $metadata,
        ]);

        $this->assertEquals($metadata, $transaction->metadata);
        $this->assertEquals(123, $transaction->metadata['order_id']);
    }

    /** @test */
    public function it_handles_withdrawal_fields()
    {
        $transaction = WalletTransaction::create([
            'user_id' => $this->user->id,
            'type' => 'withdrawal',
            'amount' => -40.00,
            'withdrawal_method' => 'bank_transfer',
            'withdrawal_reference' => 'TXN123456',
            'withdrawal_processed_at' => now(),
            'payment_method' => 'manual',
            'payment_status' => 'completed',
            'description' => 'Bank withdrawal',
        ]);

        $this->assertEquals('bank_transfer', $transaction->withdrawal_method);
        $this->assertEquals('TXN123456', $transaction->withdrawal_reference);
        $this->assertNotNull($transaction->withdrawal_processed_at);
    }
}
