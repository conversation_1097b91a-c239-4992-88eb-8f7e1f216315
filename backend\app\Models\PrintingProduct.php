<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PrintingProduct extends Model
{
    use HasFactory;

    protected $fillable = [
        'printing_category_id',
        'name',
        'description',
        'slug',
        'base_price',
        'image',
        'specifications',
        'options',
        'is_active',
        'sort_order',
        'min_quantity',
        'max_quantity',
        'production_time_days',
        'meta_title',
        'meta_description',
    ];

    protected function casts(): array
    {
        return [
            'base_price' => 'decimal:2',
            'specifications' => 'array',
            'options' => 'array',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the category that owns the product
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(PrintingCategory::class, 'printing_category_id');
    }

    /**
     * Get the order items for this product
     */
    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Scope for active products
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordering products
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get formatted base price
     */
    public function getFormattedBasePriceAttribute()
    {
        return 'RM ' . number_format($this->base_price, 2);
    }

    /**
     * Calculate price based on quantity and options
     */
    public function calculatePrice(int $quantity, array $selectedOptions = []): float
    {
        $price = $this->base_price;
        
        // Apply quantity-based pricing if configured
        if (isset($this->options['quantity_pricing'])) {
            foreach ($this->options['quantity_pricing'] as $tier) {
                if ($quantity >= $tier['min_quantity']) {
                    $price = $tier['price_per_unit'];
                }
            }
        }
        
        // Apply option-based pricing
        foreach ($selectedOptions as $optionKey => $optionValue) {
            if (isset($this->options['pricing'][$optionKey][$optionValue])) {
                $price += $this->options['pricing'][$optionKey][$optionValue];
            }
        }
        
        return $price * $quantity;
    }

    /**
     * Get the route key for the model
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }
}
