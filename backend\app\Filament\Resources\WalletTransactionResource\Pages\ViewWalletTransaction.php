<?php

namespace App\Filament\Resources\WalletTransactionResource\Pages;

use App\Filament\Resources\WalletTransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class ViewWalletTransaction extends ViewRecord
{
    protected static string $resource = WalletTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Transaction Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('id')
                            ->label('Transaction ID'),
                        
                        Infolists\Components\TextEntry::make('user.name')
                            ->label('User'),
                        
                        Infolists\Components\TextEntry::make('type')
                            ->badge()
                            ->color(fn (string $state): string => match ($state) {
                                'top_up' => 'success',
                                'payment' => 'warning',
                                'withdrawal' => 'danger',
                                'refund' => 'info',
                                'bonus' => 'primary',
                                'adjustment' => 'gray',
                                default => 'gray',
                            })
                            ->formatStateUsing(fn (string $state): string => match ($state) {
                                'top_up' => 'Top Up',
                                'payment' => 'Payment',
                                'withdrawal' => 'Withdrawal',
                                'refund' => 'Refund',
                                'bonus' => 'Bonus',
                                'adjustment' => 'Adjustment',
                                default => ucfirst($state),
                            }),
                        
                        Infolists\Components\TextEntry::make('amount')
                            ->money('MYR')
                            ->color(fn ($record): string => $record->amount >= 0 ? 'success' : 'danger'),
                        
                        Infolists\Components\TextEntry::make('amount_paid')
                            ->money('MYR')
                            ->placeholder('N/A'),
                        
                        Infolists\Components\TextEntry::make('payment_status')
                            ->badge()
                            ->color(fn (string $state): string => match ($state) {
                                'completed' => 'success',
                                'pending' => 'warning',
                                'failed' => 'danger',
                                'refunded' => 'info',
                                default => 'gray',
                            }),
                    ])->columns(2),

                Infolists\Components\Section::make('Payment Details')
                    ->schema([
                        Infolists\Components\TextEntry::make('payment_method')
                            ->formatStateUsing(fn (string $state): string => match ($state) {
                                'billplz' => 'Billplz',
                                'manual' => 'Manual',
                                'system' => 'System',
                                'bank_transfer' => 'Bank Transfer',
                                default => ucfirst($state),
                            }),
                        
                        Infolists\Components\TextEntry::make('payment_reference')
                            ->placeholder('N/A'),
                        
                        Infolists\Components\TextEntry::make('withdrawal_method')
                            ->visible(fn ($record) => $record->type === 'withdrawal')
                            ->placeholder('N/A'),
                        
                        Infolists\Components\TextEntry::make('withdrawal_reference')
                            ->visible(fn ($record) => $record->type === 'withdrawal')
                            ->placeholder('N/A'),
                        
                        Infolists\Components\TextEntry::make('processed_at')
                            ->dateTime()
                            ->placeholder('Not processed'),
                        
                        Infolists\Components\TextEntry::make('withdrawal_processed_at')
                            ->visible(fn ($record) => $record->type === 'withdrawal')
                            ->dateTime()
                            ->placeholder('Not processed'),
                    ])->columns(2),

                Infolists\Components\Section::make('Additional Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('description')
                            ->columnSpanFull(),
                        
                        Infolists\Components\KeyValueEntry::make('metadata')
                            ->columnSpanFull()
                            ->visible(fn ($record) => !empty($record->metadata)),
                        
                        Infolists\Components\TextEntry::make('created_at')
                            ->dateTime(),
                        
                        Infolists\Components\TextEntry::make('updated_at')
                            ->dateTime(),
                    ])->columns(2),
            ]);
    }
}
