<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PrintingCategory;
use App\Models\PrintingProduct;

class PrintingProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get categories
        $businessCards = PrintingCategory::where('slug', 'business-cards')->first();
        $flyers = PrintingCategory::where('slug', 'flyers-leaflets')->first();
        $banners = PrintingCategory::where('slug', 'banners-signage')->first();
        $brochures = PrintingCategory::where('slug', 'brochures-catalogs')->first();
        $stickers = PrintingCategory::where('slug', 'stickers-labels')->first();
        $posters = PrintingCategory::where('slug', 'posters')->first();

        // Business Cards Products
        if ($businessCards) {
            PrintingProduct::create([
                'printing_category_id' => $businessCards->id,
                'name' => 'Standard Business Cards',
                'description' => 'Premium quality business cards on 350gsm art card',
                'slug' => 'standard-business-cards',
                'base_price' => 25.00,
                'min_quantity' => 100,
                'max_quantity' => 10000,
                'production_time_days' => 3,
                'specifications' => [
                    'size' => '90mm x 54mm',
                    'material' => '350gsm Art Card',
                    'finish' => 'Matt Lamination',
                    'colors' => 'Full Color (CMYK)',
                ],
                'options' => [
                    'quantity_pricing' => [
                        ['min_quantity' => 100, 'price_per_unit' => 0.25],
                        ['min_quantity' => 500, 'price_per_unit' => 0.20],
                        ['min_quantity' => 1000, 'price_per_unit' => 0.15],
                        ['min_quantity' => 2000, 'price_per_unit' => 0.12],
                    ],
                    'finish_options' => [
                        'matt' => 'Matt Lamination',
                        'gloss' => 'Gloss Lamination',
                        'spot_uv' => 'Spot UV',
                    ],
                    'pricing' => [
                        'finish' => [
                            'matt' => 0.00,
                            'gloss' => 0.02,
                            'spot_uv' => 0.05,
                        ],
                    ],
                ],
                'sort_order' => 1,
            ]);

            PrintingProduct::create([
                'printing_category_id' => $businessCards->id,
                'name' => 'Premium Business Cards',
                'description' => 'Luxury business cards on 400gsm textured paper',
                'slug' => 'premium-business-cards',
                'base_price' => 45.00,
                'min_quantity' => 100,
                'max_quantity' => 5000,
                'production_time_days' => 5,
                'specifications' => [
                    'size' => '90mm x 54mm',
                    'material' => '400gsm Textured Paper',
                    'finish' => 'Embossed',
                    'colors' => 'Full Color + Spot Colors',
                ],
                'options' => [
                    'quantity_pricing' => [
                        ['min_quantity' => 100, 'price_per_unit' => 0.45],
                        ['min_quantity' => 500, 'price_per_unit' => 0.40],
                        ['min_quantity' => 1000, 'price_per_unit' => 0.35],
                    ],
                ],
                'sort_order' => 2,
            ]);
        }

        // Flyers Products
        if ($flyers) {
            PrintingProduct::create([
                'printing_category_id' => $flyers->id,
                'name' => 'A5 Flyers',
                'description' => 'Single-sided A5 flyers on 150gsm art paper',
                'slug' => 'a5-flyers',
                'base_price' => 35.00,
                'min_quantity' => 100,
                'max_quantity' => 50000,
                'production_time_days' => 2,
                'specifications' => [
                    'size' => 'A5 (148mm x 210mm)',
                    'material' => '150gsm Art Paper',
                    'sides' => 'Single-sided',
                    'colors' => 'Full Color (CMYK)',
                ],
                'options' => [
                    'quantity_pricing' => [
                        ['min_quantity' => 100, 'price_per_unit' => 0.35],
                        ['min_quantity' => 500, 'price_per_unit' => 0.25],
                        ['min_quantity' => 1000, 'price_per_unit' => 0.18],
                        ['min_quantity' => 5000, 'price_per_unit' => 0.12],
                    ],
                    'sides_options' => [
                        'single' => 'Single-sided',
                        'double' => 'Double-sided',
                    ],
                    'pricing' => [
                        'sides' => [
                            'single' => 0.00,
                            'double' => 0.08,
                        ],
                    ],
                ],
                'sort_order' => 1,
            ]);
        }

        // Banners Products
        if ($banners) {
            PrintingProduct::create([
                'printing_category_id' => $banners->id,
                'name' => 'Vinyl Banner',
                'description' => 'Outdoor vinyl banner with grommets',
                'slug' => 'vinyl-banner',
                'base_price' => 15.00, // per square foot
                'min_quantity' => 1,
                'max_quantity' => 100,
                'production_time_days' => 3,
                'specifications' => [
                    'material' => '13oz Vinyl',
                    'finish' => 'Weather Resistant',
                    'colors' => 'Full Color Digital Print',
                    'grommets' => 'Every 2 feet',
                ],
                'options' => [
                    'size_pricing' => [
                        'calculation' => 'per_sqft',
                        'base_price' => 15.00,
                    ],
                    'finishing_options' => [
                        'grommets' => 'Grommets every 2 feet',
                        'pole_pockets' => 'Pole pockets',
                        'wind_slits' => 'Wind slits',
                    ],
                ],
                'sort_order' => 1,
            ]);
        }
    }
}
