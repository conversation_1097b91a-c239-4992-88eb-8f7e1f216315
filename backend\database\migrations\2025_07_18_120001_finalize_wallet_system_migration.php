<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Step 1: Rename credit_transactions table to wallet_transactions
        Schema::rename('credit_transactions', 'wallet_transactions');

        // Step 2: Remove old credit_amount column (data already migrated to amount)
        Schema::table('wallet_transactions', function (Blueprint $table) {
            $table->dropColumn('credit_amount');
        });

        // Step 3: Remove old credit_balance column from users (data already migrated to wallet_balance)
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('credit_balance');
        });

        // Step 4: Update credit_package_id to be more generic (optional - for future use)
        Schema::table('wallet_transactions', function (Blueprint $table) {
            $table->renameColumn('credit_package_id', 'package_id');
        });

        // Step 5: Add wallet-specific constraints
        Schema::table('wallet_transactions', function (Blueprint $table) {
            // Ensure amount is always present
            $table->decimal('amount', 10, 2)->nullable(false)->change();
        });

        // Step 5b: Update type column separately to handle enum constraint properly
        DB::statement("ALTER TABLE wallet_transactions MODIFY COLUMN type ENUM('top_up', 'payment', 'withdrawal', 'refund', 'bonus', 'adjustment') NOT NULL");

        // Step 6: Update foreign key references if needed
        Schema::table('wallet_transactions', function (Blueprint $table) {
            // Update package relationship to be more generic
            $table->foreign('package_id')->references('id')->on('credit_packages')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reverse Step 6: Remove foreign key
        Schema::table('wallet_transactions', function (Blueprint $table) {
            $table->dropForeign(['package_id']);
        });

        // Reverse Step 5: Revert constraints
        Schema::table('wallet_transactions', function (Blueprint $table) {
            $table->decimal('amount', 10, 2)->nullable()->change();
            $table->enum('type', ['purchase', 'usage', 'refund', 'bonus', 'adjustment'])->change();
        });

        // Reverse Step 4: Rename package_id back to credit_package_id
        Schema::table('wallet_transactions', function (Blueprint $table) {
            $table->renameColumn('package_id', 'credit_package_id');
        });

        // Reverse Step 3: Restore credit_balance column
        Schema::table('users', function (Blueprint $table) {
            $table->integer('credit_balance')->default(0)->after('wallet_balance');
        });

        // Reverse Step 2: Restore credit_amount column
        Schema::table('wallet_transactions', function (Blueprint $table) {
            $table->integer('credit_amount')->after('amount');
        });

        // Reverse Step 1: Rename table back
        Schema::rename('wallet_transactions', 'credit_transactions');
    }
};
