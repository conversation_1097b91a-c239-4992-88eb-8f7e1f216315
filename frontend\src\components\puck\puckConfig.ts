import React from 'react';

// Comprehensive Puck configuration with all essential components
export const puckConfig = {
  components: {
    // Layout Components
    Hero: {
      label: 'Hero Section',
      defaultProps: {
        title: 'Hero Title',
        subtitle: 'Hero subtitle text',
        textAlign: 'center',
        minHeight: '400px',
        backgroundType: 'gradient',
        backgroundColor: '#667eea',
        backgroundGradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        textColor: '#ffffff',
      },
      fields: {
        title: { type: 'text' as const, label: 'Title' },
        subtitle: { type: 'textarea' as const, label: 'Subtitle' },
        textAlign: {
          type: 'select' as const,
          label: 'Text Alignment',
          options: [
            { label: 'Left', value: 'left' },
            { label: 'Center', value: 'center' },
            { label: 'Right', value: 'right' },
          ],
        },
        minHeight: { type: 'text' as const, label: 'Min Height (px)' },
        backgroundType: {
          type: 'select' as const,
          label: 'Background Type',
          options: [
            { label: 'Solid Color', value: 'solid' },
            { label: 'Gradient', value: 'gradient' },
            { label: 'Image', value: 'image' },
          ],
        },
        backgroundColor: { type: 'text' as const, label: 'Background Color' },
        backgroundGradient: { type: 'text' as const, label: 'Background Gradient' },
        backgroundImage: { type: 'text' as const, label: 'Background Image URL' },
        textColor: { type: 'text' as const, label: 'Text Color' },
      },
      render: ({ title, subtitle, textAlign, minHeight, backgroundType, backgroundColor, backgroundGradient, backgroundImage, textColor }: any) => {
        let backgroundStyle = {};

        switch (backgroundType) {
          case 'solid':
            backgroundStyle = { backgroundColor: backgroundColor || '#667eea' };
            break;
          case 'gradient':
            backgroundStyle = { background: backgroundGradient || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' };
            break;
          case 'image':
            backgroundStyle = {
              backgroundImage: `url(${backgroundImage})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat'
            };
            break;
          default:
            backgroundStyle = { background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' };
        }

        return React.createElement('section', {
          style: {
            minHeight: minHeight || '400px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: textAlign === 'center' ? 'center' : textAlign === 'right' ? 'flex-end' : 'flex-start',
            textAlign: textAlign || 'center',
            padding: '4rem 2rem',
            color: textColor || 'white',
            position: 'relative',
            ...backgroundStyle,
          }
        }, React.createElement('div', {
          style: { position: 'relative', zIndex: 1, maxWidth: '800px' }
        }, [
          React.createElement('h1', {
            key: 'title',
            style: {
              fontSize: 'clamp(2rem, 5vw, 3.5rem)',
              marginBottom: '1rem',
              fontWeight: 'bold',
              lineHeight: 1.2,
            }
          }, title || 'Hero Title'),
          React.createElement('p', {
            key: 'subtitle',
            style: {
              fontSize: 'clamp(1rem, 2.5vw, 1.25rem)',
              opacity: 0.9,
              lineHeight: 1.6,
              marginBottom: '2rem',
            }
          }, subtitle || 'Hero subtitle text')
        ]));
      },
    },

    // Text Component
    Text: {
      label: 'Text Block',
      defaultProps: {
        content: '<p>Add your text content here...</p>',
        textAlign: 'left',
        fontSize: '16px',
        color: '#333333',
        lineHeight: '1.6',
        marginTop: '0px',
        marginBottom: '20px',
      },
      fields: {
        content: { type: 'textarea' as const, label: 'Content (HTML)' },
        textAlign: {
          type: 'select' as const,
          label: 'Text Alignment',
          options: [
            { label: 'Left', value: 'left' },
            { label: 'Center', value: 'center' },
            { label: 'Right', value: 'right' },
            { label: 'Justify', value: 'justify' },
          ],
        },
        fontSize: { type: 'text' as const, label: 'Font Size' },
        color: { type: 'text' as const, label: 'Text Color' },
        lineHeight: { type: 'text' as const, label: 'Line Height' },
        marginTop: { type: 'text' as const, label: 'Margin Top' },
        marginBottom: { type: 'text' as const, label: 'Margin Bottom' },
      },
      render: ({ content, textAlign, fontSize, color, lineHeight, marginTop, marginBottom }: any) => {
        return React.createElement('div', {
          style: {
            padding: '0 1rem',
            marginTop: marginTop || '0px',
            marginBottom: marginBottom || '20px',
          }
        }, React.createElement('div', {
          style: {
            textAlign: textAlign || 'left',
            fontSize: fontSize || '16px',
            color: color || '#333333',
            lineHeight: lineHeight || '1.6',
          },
          dangerouslySetInnerHTML: { __html: content || '<p>Add your text content here...</p>' }
        }));
      },
    },

    // Heading Component
    Heading: {
      label: 'Heading',
      defaultProps: {
        text: 'Heading Text',
        level: 'h2',
        textAlign: 'left',
        color: '#333333',
        marginTop: '0px',
        marginBottom: '20px',
      },
      fields: {
        text: { type: 'text' as const, label: 'Heading Text' },
        level: {
          type: 'select' as const,
          label: 'Heading Level',
          options: [
            { label: 'H1', value: 'h1' },
            { label: 'H2', value: 'h2' },
            { label: 'H3', value: 'h3' },
            { label: 'H4', value: 'h4' },
            { label: 'H5', value: 'h5' },
            { label: 'H6', value: 'h6' },
          ],
        },
        textAlign: {
          type: 'select' as const,
          label: 'Text Alignment',
          options: [
            { label: 'Left', value: 'left' },
            { label: 'Center', value: 'center' },
            { label: 'Right', value: 'right' },
          ],
        },
        color: { type: 'text' as const, label: 'Text Color' },
        marginTop: { type: 'text' as const, label: 'Margin Top' },
        marginBottom: { type: 'text' as const, label: 'Margin Bottom' },
      },
      render: ({ text, level, textAlign, color, marginTop, marginBottom }: any) => {
        const HeadingTag = level || 'h2';
        const fontSizes: { [key: string]: string } = {
          h1: '2.5rem',
          h2: '2rem',
          h3: '1.75rem',
          h4: '1.5rem',
          h5: '1.25rem',
          h6: '1rem',
        };
        const fontSize = fontSizes[level] || '2rem';

        return React.createElement(
          HeadingTag,
          {
            style: {
              textAlign: textAlign || 'left',
              color: color || '#333333',
              fontSize,
              fontWeight: 'bold',
              marginTop: marginTop || '0px',
              marginBottom: marginBottom || '20px',
              padding: '0 1rem',
              lineHeight: 1.2,
            }
          },
          text || 'Heading Text'
        );
      },
    },

    // Layout Components
    Grid: {
      label: 'Grid Layout',
      defaultProps: {
        columns: '3',
        gap: '20px',
        alignItems: 'stretch',
        justifyItems: 'stretch',
        padding: '20px',
        backgroundColor: 'transparent',
        minHeight: 'auto',
      },
      fields: {
        columns: {
          type: 'select' as const,
          label: 'Columns',
          options: [
            { label: '1 Column', value: '1' },
            { label: '2 Columns', value: '2' },
            { label: '3 Columns', value: '3' },
            { label: '4 Columns', value: '4' },
            { label: '5 Columns', value: '5' },
            { label: '6 Columns', value: '6' },
          ],
        },
        gap: { type: 'text' as const, label: 'Gap (px)' },
        alignItems: {
          type: 'select' as const,
          label: 'Align Items',
          options: [
            { label: 'Stretch', value: 'stretch' },
            { label: 'Start', value: 'start' },
            { label: 'Center', value: 'center' },
            { label: 'End', value: 'end' },
          ],
        },
        justifyItems: {
          type: 'select' as const,
          label: 'Justify Items',
          options: [
            { label: 'Stretch', value: 'stretch' },
            { label: 'Start', value: 'start' },
            { label: 'Center', value: 'center' },
            { label: 'End', value: 'end' },
          ],
        },
        padding: { type: 'text' as const, label: 'Padding' },
        backgroundColor: { type: 'text' as const, label: 'Background Color' },
        minHeight: { type: 'text' as const, label: 'Min Height' },
      },
      render: ({ columns, gap, alignItems, justifyItems, padding, backgroundColor, minHeight }: any) => {
        return React.createElement('div', {
          style: {
            display: 'grid',
            gridTemplateColumns: `repeat(${columns || 3}, 1fr)`,
            gap: gap || '20px',
            alignItems: alignItems || 'stretch',
            justifyItems: justifyItems || 'stretch',
            padding: padding || '20px',
            backgroundColor: backgroundColor || 'transparent',
            minHeight: minHeight || 'auto',
            width: '100%',
          }
        }, React.createElement('div', {
          style: {
            gridColumn: '1 / -1',
            textAlign: 'center',
            padding: '40px 20px',
            border: '2px dashed #e2e8f0',
            borderRadius: '8px',
            color: '#64748b',
            fontSize: '14px',
          }
        }, `Grid with ${columns || 3} columns - Drop components here`));
      },
    },

    Flex: {
      label: 'Flex Container',
      defaultProps: {
        direction: 'row',
        justifyContent: 'flex-start',
        alignItems: 'stretch',
        flexWrap: 'nowrap',
        gap: '16px',
        padding: '20px',
        backgroundColor: 'transparent',
        minHeight: 'auto',
      },
      fields: {
        direction: {
          type: 'select' as const,
          label: 'Direction',
          options: [
            { label: 'Row', value: 'row' },
            { label: 'Column', value: 'column' },
            { label: 'Row Reverse', value: 'row-reverse' },
            { label: 'Column Reverse', value: 'column-reverse' },
          ],
        },
        justifyContent: {
          type: 'select' as const,
          label: 'Justify Content',
          options: [
            { label: 'Flex Start', value: 'flex-start' },
            { label: 'Center', value: 'center' },
            { label: 'Flex End', value: 'flex-end' },
            { label: 'Space Between', value: 'space-between' },
            { label: 'Space Around', value: 'space-around' },
            { label: 'Space Evenly', value: 'space-evenly' },
          ],
        },
        alignItems: {
          type: 'select' as const,
          label: 'Align Items',
          options: [
            { label: 'Stretch', value: 'stretch' },
            { label: 'Flex Start', value: 'flex-start' },
            { label: 'Center', value: 'center' },
            { label: 'Flex End', value: 'flex-end' },
            { label: 'Baseline', value: 'baseline' },
          ],
        },
        flexWrap: {
          type: 'select' as const,
          label: 'Flex Wrap',
          options: [
            { label: 'No Wrap', value: 'nowrap' },
            { label: 'Wrap', value: 'wrap' },
            { label: 'Wrap Reverse', value: 'wrap-reverse' },
          ],
        },
        gap: { type: 'text' as const, label: 'Gap' },
        padding: { type: 'text' as const, label: 'Padding' },
        backgroundColor: { type: 'text' as const, label: 'Background Color' },
        minHeight: { type: 'text' as const, label: 'Min Height' },
      },
      render: ({ direction, justifyContent, alignItems, flexWrap, gap, padding, backgroundColor, minHeight }: any) => {
        return React.createElement('div', {
          style: {
            display: 'flex',
            flexDirection: direction || 'row',
            justifyContent: justifyContent || 'flex-start',
            alignItems: alignItems || 'stretch',
            flexWrap: flexWrap || 'nowrap',
            gap: gap || '16px',
            padding: padding || '20px',
            backgroundColor: backgroundColor || 'transparent',
            minHeight: minHeight || 'auto',
            width: '100%',
          }
        }, React.createElement('div', {
          style: {
            flex: '1',
            textAlign: 'center',
            padding: '40px 20px',
            border: '2px dashed #e2e8f0',
            borderRadius: '8px',
            color: '#64748b',
            fontSize: '14px',
          }
        }, 'Flex Container - Drop components here'));
      },
    },

    Space: {
      label: 'Spacer',
      defaultProps: {
        height: '40px',
        width: '100%',
        backgroundColor: 'transparent',
      },
      fields: {
        height: { type: 'text' as const, label: 'Height' },
        width: { type: 'text' as const, label: 'Width' },
        backgroundColor: { type: 'text' as const, label: 'Background Color' },
      },
      render: ({ height, width, backgroundColor }: any) => {
        return React.createElement('div', {
          style: {
            height: height || '40px',
            width: width || '100%',
            backgroundColor: backgroundColor || 'transparent',
            display: 'block',
          }
        });
      },
    },

    // Action Components
    Button: {
      label: 'Button',
      defaultProps: {
        text: 'Click Me',
        href: '',
        variant: 'primary',
        size: 'medium',
        fullWidth: false,
        disabled: false,
        target: '_self',
        backgroundColor: '#3b82f6',
        textColor: '#ffffff',
        borderRadius: '6px',
        padding: '12px 24px',
        fontSize: '16px',
        fontWeight: '500',
      },
      fields: {
        text: { type: 'text' as const, label: 'Button Text' },
        href: { type: 'text' as const, label: 'Link URL (optional)' },
        variant: {
          type: 'select' as const,
          label: 'Variant',
          options: [
            { label: 'Primary', value: 'primary' },
            { label: 'Secondary', value: 'secondary' },
            { label: 'Outline', value: 'outline' },
            { label: 'Ghost', value: 'ghost' },
          ],
        },
        size: {
          type: 'select' as const,
          label: 'Size',
          options: [
            { label: 'Small', value: 'small' },
            { label: 'Medium', value: 'medium' },
            { label: 'Large', value: 'large' },
          ],
        },
        fullWidth: { type: 'radio' as const, label: 'Full Width', options: [{ label: 'Yes', value: true }, { label: 'No', value: false }] },
        disabled: { type: 'radio' as const, label: 'Disabled', options: [{ label: 'Yes', value: true }, { label: 'No', value: false }] },
        target: {
          type: 'select' as const,
          label: 'Link Target',
          options: [
            { label: 'Same Window', value: '_self' },
            { label: 'New Window', value: '_blank' },
          ],
        },
        backgroundColor: { type: 'text' as const, label: 'Background Color' },
        textColor: { type: 'text' as const, label: 'Text Color' },
        borderRadius: { type: 'text' as const, label: 'Border Radius' },
        padding: { type: 'text' as const, label: 'Padding' },
        fontSize: { type: 'text' as const, label: 'Font Size' },
        fontWeight: { type: 'text' as const, label: 'Font Weight' },
      },
      render: ({ text, href, variant, size, fullWidth, disabled, target, backgroundColor, textColor, borderRadius, padding, fontSize, fontWeight }: any) => {
        const sizeStyles = {
          small: { padding: '8px 16px', fontSize: '14px' },
          medium: { padding: '12px 24px', fontSize: '16px' },
          large: { padding: '16px 32px', fontSize: '18px' },
        };

        const variantStyles = {
          primary: { backgroundColor: backgroundColor || '#3b82f6', color: textColor || '#ffffff', border: 'none' },
          secondary: { backgroundColor: '#6b7280', color: '#ffffff', border: 'none' },
          outline: { backgroundColor: 'transparent', color: backgroundColor || '#3b82f6', border: `2px solid ${backgroundColor || '#3b82f6'}` },
          ghost: { backgroundColor: 'transparent', color: backgroundColor || '#3b82f6', border: 'none' },
        };

        const buttonStyle = {
          ...sizeStyles[size as keyof typeof sizeStyles] || sizeStyles.medium,
          ...variantStyles[variant as keyof typeof variantStyles] || variantStyles.primary,
          borderRadius: borderRadius || '6px',
          fontWeight: fontWeight || '500',
          cursor: disabled ? 'not-allowed' : 'pointer',
          opacity: disabled ? 0.6 : 1,
          textDecoration: 'none',
          display: fullWidth ? 'block' : 'inline-block',
          width: fullWidth ? '100%' : 'auto',
          textAlign: 'center' as const,
          border: variantStyles[variant as keyof typeof variantStyles]?.border || 'none',
          transition: 'all 0.2s ease-in-out',
        };

        if (href && !disabled) {
          return React.createElement('a', {
            href,
            target: target || '_self',
            style: buttonStyle,
            rel: target === '_blank' ? 'noopener noreferrer' : undefined,
          }, text || 'Click Me');
        }

        return React.createElement('button', {
          style: buttonStyle,
          disabled,
          type: 'button',
        }, text || 'Click Me');
      },
    },

    // Content Components
    Card: {
      label: 'Card',
      defaultProps: {
        title: 'Card Title',
        content: 'Card content goes here...',
        imageUrl: '',
        showImage: false,
        showHeader: true,
        showFooter: false,
        footerText: 'Card Footer',
        backgroundColor: '#ffffff',
        borderColor: '#e2e8f0',
        borderRadius: '8px',
        padding: '24px',
        shadow: 'medium',
      },
      fields: {
        title: { type: 'text' as const, label: 'Card Title' },
        content: { type: 'textarea' as const, label: 'Card Content' },
        imageUrl: { type: 'text' as const, label: 'Image URL' },
        showImage: { type: 'radio' as const, label: 'Show Image', options: [{ label: 'Yes', value: true }, { label: 'No', value: false }] },
        showHeader: { type: 'radio' as const, label: 'Show Header', options: [{ label: 'Yes', value: true }, { label: 'No', value: false }] },
        showFooter: { type: 'radio' as const, label: 'Show Footer', options: [{ label: 'Yes', value: true }, { label: 'No', value: false }] },
        footerText: { type: 'text' as const, label: 'Footer Text' },
        backgroundColor: { type: 'text' as const, label: 'Background Color' },
        borderColor: { type: 'text' as const, label: 'Border Color' },
        borderRadius: { type: 'text' as const, label: 'Border Radius' },
        padding: { type: 'text' as const, label: 'Padding' },
        shadow: {
          type: 'select' as const,
          label: 'Shadow',
          options: [
            { label: 'None', value: 'none' },
            { label: 'Small', value: 'small' },
            { label: 'Medium', value: 'medium' },
            { label: 'Large', value: 'large' },
          ],
        },
      },
      render: ({ title, content, imageUrl, showImage, showHeader, showFooter, footerText, backgroundColor, borderColor, borderRadius, padding, shadow }: any) => {
        const shadowStyles = {
          none: 'none',
          small: '0 1px 3px rgba(0, 0, 0, 0.1)',
          medium: '0 4px 6px rgba(0, 0, 0, 0.1)',
          large: '0 10px 15px rgba(0, 0, 0, 0.1)',
        };

        const cardStyle = {
          backgroundColor: backgroundColor || '#ffffff',
          border: `1px solid ${borderColor || '#e2e8f0'}`,
          borderRadius: borderRadius || '8px',
          boxShadow: shadowStyles[shadow as keyof typeof shadowStyles] || shadowStyles.medium,
          overflow: 'hidden' as const,
          transition: 'box-shadow 0.2s ease',
        };

        const elements = [];

        if (showImage && imageUrl) {
          elements.push(
            React.createElement('img', {
              key: 'image',
              src: imageUrl,
              alt: title || 'Card image',
              style: {
                width: '100%',
                height: '200px',
                objectFit: 'cover' as const,
                display: 'block',
              }
            })
          );
        }

        const bodyElements = [];

        if (showHeader && title) {
          bodyElements.push(
            React.createElement('h3', {
              key: 'title',
              style: {
                margin: '0 0 16px 0',
                fontSize: '1.25rem',
                fontWeight: '600',
                color: '#1f2937',
              }
            }, title)
          );
        }

        if (content) {
          bodyElements.push(
            React.createElement('div', {
              key: 'content',
              style: {
                color: '#6b7280',
                lineHeight: '1.6',
                margin: showFooter ? '0 0 16px 0' : '0',
              },
              dangerouslySetInnerHTML: { __html: content }
            })
          );
        }

        if (bodyElements.length > 0) {
          elements.push(
            React.createElement('div', {
              key: 'body',
              style: { padding: padding || '24px' }
            }, ...bodyElements)
          );
        }

        if (showFooter && footerText) {
          elements.push(
            React.createElement('div', {
              key: 'footer',
              style: {
                padding: `0 ${padding || '24px'} ${padding || '24px'}`,
                borderTop: `1px solid ${borderColor || '#e2e8f0'}`,
                backgroundColor: '#f9fafb',
                color: '#6b7280',
                fontSize: '14px',
              }
            }, footerText)
          );
        }

        return React.createElement('div', { style: cardStyle }, ...elements);
      },
    },

    // Display Components
    Logos: {
      label: 'Logo Grid',
      defaultProps: {
        title: 'Our Partners',
        showTitle: true,
        logos: [
          { name: 'Company 1', url: 'https://via.placeholder.com/120x60/cccccc/666666?text=Logo+1', link: '' },
          { name: 'Company 2', url: 'https://via.placeholder.com/120x60/cccccc/666666?text=Logo+2', link: '' },
          { name: 'Company 3', url: 'https://via.placeholder.com/120x60/cccccc/666666?text=Logo+3', link: '' },
          { name: 'Company 4', url: 'https://via.placeholder.com/120x60/cccccc/666666?text=Logo+4', link: '' },
        ],
        columns: '4',
        gap: '32px',
        padding: '40px 20px',
        backgroundColor: '#f9fafb',
        logoHeight: '60px',
        grayscale: false,
      },
      fields: {
        title: { type: 'text' as const, label: 'Section Title' },
        showTitle: { type: 'radio' as const, label: 'Show Title', options: [{ label: 'Yes', value: true }, { label: 'No', value: false }] },
        columns: {
          type: 'select' as const,
          label: 'Columns',
          options: [
            { label: '2 Columns', value: '2' },
            { label: '3 Columns', value: '3' },
            { label: '4 Columns', value: '4' },
            { label: '5 Columns', value: '5' },
            { label: '6 Columns', value: '6' },
          ],
        },
        gap: { type: 'text' as const, label: 'Gap between logos' },
        padding: { type: 'text' as const, label: 'Section Padding' },
        backgroundColor: { type: 'text' as const, label: 'Background Color' },
        logoHeight: { type: 'text' as const, label: 'Logo Height' },
        grayscale: { type: 'radio' as const, label: 'Grayscale Effect', options: [{ label: 'Yes', value: true }, { label: 'No', value: false }] },
      },
      render: ({ title, showTitle, logos, columns, gap, padding, backgroundColor, logoHeight, grayscale }: any) => {
        const elements = [];

        if (showTitle && title) {
          elements.push(
            React.createElement('h2', {
              key: 'title',
              style: {
                textAlign: 'center' as const,
                marginBottom: '40px',
                fontSize: '2rem',
                fontWeight: '600',
                color: '#1f2937',
              }
            }, title)
          );
        }

        const logoElements = (logos || []).map((logo: any, index: number) => {
          const logoImg = React.createElement('img', {
            src: logo.url,
            alt: logo.name,
            style: {
              height: logoHeight || '60px',
              width: 'auto',
              maxWidth: '100%',
              objectFit: 'contain' as const,
              filter: grayscale ? 'grayscale(100%)' : 'none',
              transition: 'all 0.3s ease',
              opacity: grayscale ? 0.7 : 1,
            }
          });

          if (logo.link) {
            return React.createElement('a', {
              key: index,
              href: logo.link,
              target: '_blank',
              rel: 'noopener noreferrer',
              style: {
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                textDecoration: 'none',
              }
            }, logoImg);
          }

          return React.createElement('div', {
            key: index,
            style: {
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }
          }, logoImg);
        });

        elements.push(
          React.createElement('div', {
            key: 'logos',
            style: {
              display: 'grid',
              gridTemplateColumns: `repeat(${columns || 4}, 1fr)`,
              gap: gap || '32px',
              alignItems: 'center',
              justifyItems: 'center',
            }
          }, ...logoElements)
        );

        return React.createElement('section', {
          style: {
            padding: padding || '40px 20px',
            backgroundColor: backgroundColor || '#f9fafb',
          }
        }, React.createElement('div', {
          style: {
            maxWidth: '1200px',
            margin: '0 auto',
          }
        }, ...elements));
      },
    },

    Stats: {
      label: 'Statistics',
      defaultProps: {
        title: 'Our Impact',
        showTitle: true,
        stats: [
          { number: '10K+', label: 'Happy Customers', description: 'Satisfied clients worldwide' },
          { number: '50+', label: 'Team Members', description: 'Dedicated professionals' },
          { number: '99%', label: 'Success Rate', description: 'Project completion rate' },
          { number: '24/7', label: 'Support', description: 'Always here to help' },
        ],
        columns: '4',
        backgroundColor: '#ffffff',
        padding: '60px 20px',
        numberColor: '#3b82f6',
        labelColor: '#1f2937',
        descriptionColor: '#6b7280',
      },
      fields: {
        title: { type: 'text' as const, label: 'Section Title' },
        showTitle: { type: 'radio' as const, label: 'Show Title', options: [{ label: 'Yes', value: true }, { label: 'No', value: false }] },
        columns: {
          type: 'select' as const,
          label: 'Columns',
          options: [
            { label: '2 Columns', value: '2' },
            { label: '3 Columns', value: '3' },
            { label: '4 Columns', value: '4' },
          ],
        },
        backgroundColor: { type: 'text' as const, label: 'Background Color' },
        padding: { type: 'text' as const, label: 'Section Padding' },
        numberColor: { type: 'text' as const, label: 'Number Color' },
        labelColor: { type: 'text' as const, label: 'Label Color' },
        descriptionColor: { type: 'text' as const, label: 'Description Color' },
      },
      render: ({ title, showTitle, stats, columns, backgroundColor, padding, numberColor, labelColor, descriptionColor }: any) => {
        const elements = [];

        if (showTitle && title) {
          elements.push(
            React.createElement('h2', {
              key: 'title',
              style: {
                textAlign: 'center' as const,
                marginBottom: '50px',
                fontSize: '2.5rem',
                fontWeight: '600',
                color: '#1f2937',
              }
            }, title)
          );
        }

        const statElements = (stats || []).map((stat: any, index: number) => {
          return React.createElement('div', {
            key: index,
            style: {
              textAlign: 'center' as const,
              padding: '20px',
            }
          }, [
            React.createElement('div', {
              key: 'number',
              style: {
                fontSize: '3rem',
                fontWeight: '700',
                color: numberColor || '#3b82f6',
                marginBottom: '8px',
                lineHeight: 1,
              }
            }, stat.number),
            React.createElement('h3', {
              key: 'label',
              style: {
                fontSize: '1.25rem',
                fontWeight: '600',
                color: labelColor || '#1f2937',
                marginBottom: '8px',
                margin: '8px 0',
              }
            }, stat.label),
            React.createElement('p', {
              key: 'description',
              style: {
                fontSize: '0.875rem',
                color: descriptionColor || '#6b7280',
                margin: '0',
                lineHeight: 1.5,
              }
            }, stat.description),
          ]);
        });

        elements.push(
          React.createElement('div', {
            key: 'stats',
            style: {
              display: 'grid',
              gridTemplateColumns: `repeat(${columns || 4}, 1fr)`,
              gap: '40px',
              maxWidth: '1200px',
              margin: '0 auto',
            }
          }, ...statElements)
        );

        return React.createElement('section', {
          style: {
            padding: padding || '60px 20px',
            backgroundColor: backgroundColor || '#ffffff',
          }
        }, ...elements);
      },
    },

    Template: {
      label: 'Content Template',
      defaultProps: {
        templateType: 'feature',
        title: 'Feature Section',
        subtitle: 'Discover amazing features',
        content: '<p>This is a flexible content template that can be customized for different purposes.</p>',
        imageUrl: 'https://via.placeholder.com/600x400/f3f4f6/9ca3af?text=Template+Image',
        showImage: true,
        imagePosition: 'left',
        backgroundColor: '#ffffff',
        padding: '80px 20px',
        maxWidth: '1200px',
        textAlign: 'left',
      },
      fields: {
        templateType: {
          type: 'select' as const,
          label: 'Template Type',
          options: [
            { label: 'Feature Section', value: 'feature' },
            { label: 'About Section', value: 'about' },
            { label: 'Service Section', value: 'service' },
            { label: 'Testimonial', value: 'testimonial' },
          ],
        },
        title: { type: 'text' as const, label: 'Title' },
        subtitle: { type: 'text' as const, label: 'Subtitle' },
        content: { type: 'textarea' as const, label: 'Content (HTML)' },
        imageUrl: { type: 'text' as const, label: 'Image URL' },
        showImage: { type: 'radio' as const, label: 'Show Image', options: [{ label: 'Yes', value: true }, { label: 'No', value: false }] },
        imagePosition: {
          type: 'select' as const,
          label: 'Image Position',
          options: [
            { label: 'Left', value: 'left' },
            { label: 'Right', value: 'right' },
            { label: 'Top', value: 'top' },
            { label: 'Bottom', value: 'bottom' },
          ],
        },
        backgroundColor: { type: 'text' as const, label: 'Background Color' },
        padding: { type: 'text' as const, label: 'Section Padding' },
        maxWidth: { type: 'text' as const, label: 'Max Width' },
        textAlign: {
          type: 'select' as const,
          label: 'Text Alignment',
          options: [
            { label: 'Left', value: 'left' },
            { label: 'Center', value: 'center' },
            { label: 'Right', value: 'right' },
          ],
        },
      },
      render: ({ templateType, title, subtitle, content, imageUrl, showImage, imagePosition, backgroundColor, padding, maxWidth, textAlign }: any) => {
        const isHorizontal = imagePosition === 'left' || imagePosition === 'right';
        const isImageFirst = imagePosition === 'left' || imagePosition === 'top';

        const textContent = React.createElement('div', {
          style: {
            flex: isHorizontal ? '1' : 'none',
            textAlign: textAlign || 'left',
          }
        }, [
          title && React.createElement('h2', {
            key: 'title',
            style: {
              fontSize: '2.5rem',
              fontWeight: '700',
              color: '#1f2937',
              marginBottom: '16px',
              lineHeight: 1.2,
            }
          }, title),
          subtitle && React.createElement('p', {
            key: 'subtitle',
            style: {
              fontSize: '1.25rem',
              color: '#6b7280',
              marginBottom: '24px',
              lineHeight: 1.6,
            }
          }, subtitle),
          content && React.createElement('div', {
            key: 'content',
            style: {
              color: '#374151',
              lineHeight: 1.7,
              fontSize: '1rem',
            },
            dangerouslySetInnerHTML: { __html: content }
          }),
        ].filter(Boolean));

        const imageContent = showImage && imageUrl ? React.createElement('div', {
          style: {
            flex: isHorizontal ? '1' : 'none',
            marginBottom: !isHorizontal && isImageFirst ? '40px' : '0',
            marginTop: !isHorizontal && !isImageFirst ? '40px' : '0',
            marginRight: isHorizontal && isImageFirst ? '40px' : '0',
            marginLeft: isHorizontal && !isImageFirst ? '40px' : '0',
          }
        }, React.createElement('img', {
          src: imageUrl,
          alt: title || 'Template image',
          style: {
            width: '100%',
            height: 'auto',
            borderRadius: '12px',
            boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
          }
        })) : null;

        const contentElements = isImageFirst
          ? [imageContent, textContent].filter(Boolean)
          : [textContent, imageContent].filter(Boolean);

        return React.createElement('section', {
          style: {
            padding: padding || '80px 20px',
            backgroundColor: backgroundColor || '#ffffff',
          }
        }, React.createElement('div', {
          style: {
            maxWidth: maxWidth || '1200px',
            margin: '0 auto',
            display: 'flex',
            flexDirection: isHorizontal ? 'row' : 'column',
            alignItems: 'center',
            gap: isHorizontal ? '40px' : '0',
          }
        }, ...contentElements));
      },
    },
  },
};
