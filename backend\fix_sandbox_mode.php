<?php

require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\PaymentSetting;
use Illuminate\Support\Facades\Artisan;

echo "=== BILLPLZ SANDBOX MODE FIX ===\n";

// 1. Clear any caches
echo "1. Clearing caches...\n";
try {
    Artisan::call('cache:clear');
    Artisan::call('config:clear');
    echo "✓ Caches cleared\n";
} catch (Exception $e) {
    echo "⚠ Cache clear warning: " . $e->getMessage() . "\n";
}

// 2. Check current state
echo "\n2. Current state:\n";
$currentSandbox = PaymentSetting::get('billplz_sandbox_mode');
echo "- Sandbox mode: " . var_export($currentSandbox, true) . " (" . ($currentSandbox ? 'SANDBOX' : 'PRODUCTION') . ")\n";

// 3. Force set to production mode
echo "\n3. Setting to PRODUCTION mode...\n";
PaymentSetting::set('billplz_sandbox_mode', false, 'boolean');

// 4. Verify the change
$newValue = PaymentSetting::get('billplz_sandbox_mode');
echo "✓ New value: " . var_export($newValue, true) . " (" . ($newValue ? 'SANDBOX' : 'PRODUCTION') . ")\n";

// 5. Check database directly
echo "\n4. Database verification:\n";
$dbValue = \Illuminate\Support\Facades\DB::table('payment_settings')
    ->where('key', 'billplz_sandbox_mode')
    ->value('value');
echo "- Database value: '{$dbValue}'\n";
echo "- Interpreted as: " . ($dbValue === '0' ? 'PRODUCTION' : 'SANDBOX') . "\n";

// 6. Show current configuration
echo "\n5. Current Billplz configuration:\n";
$settings = [
    'billplz_enabled' => PaymentSetting::get('billplz_enabled'),
    'billplz_sandbox_mode' => PaymentSetting::get('billplz_sandbox_mode'),
    'billplz_api_key' => PaymentSetting::get('billplz_api_key'),
    'billplz_collection_id' => PaymentSetting::get('billplz_collection_id'),
    'billplz_x_signature_key' => PaymentSetting::get('billplz_x_signature_key'),
];

foreach ($settings as $key => $value) {
    if (in_array($key, ['billplz_api_key', 'billplz_x_signature_key'])) {
        echo "- {$key}: " . ($value ? substr($value, 0, 8) . '...' : 'NOT SET') . "\n";
    } else {
        echo "- {$key}: " . var_export($value, true) . "\n";
    }
}

echo "\n=== INSTRUCTIONS ===\n";
echo "1. The sandbox mode has been fixed and set to PRODUCTION\n";
echo "2. Go to your admin panel: http://localhost:8000/admin/billplz-settings\n";
echo "3. Refresh the page (Ctrl+F5 or Cmd+Shift+R)\n";
echo "4. You should now see:\n";
echo "   - Sandbox Mode toggle should be OFF (production)\n";
echo "   - Your production API key should be visible\n";
echo "   - Your production collection ID should be visible\n";
echo "5. If the toggle still appears stuck:\n";
echo "   - Clear your browser cache\n";
echo "   - Try in an incognito/private window\n";
echo "   - The setting is already correctly saved in the database\n";

echo "\n=== TESTING PAYMENT ===\n";
echo "You can now test live payments with your production credentials.\n";
echo "The system will use:\n";
echo "- Production API endpoint: https://www.billplz.com/api/\n";
echo "- Your production API key\n";
echo "- Your production collection ID\n";

echo "\n✓ Fix completed successfully!\n";
