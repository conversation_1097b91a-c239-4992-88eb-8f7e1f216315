<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CorsMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Handle preflight OPTIONS requests
        if ($request->getMethod() === 'OPTIONS') {
            return $this->handlePreflightRequest($request);
        }

        $response = $next($request);

        return $this->addCorsHeaders($request, $response);
    }

    /**
     * Handle preflight OPTIONS requests
     */
    private function handlePreflightRequest(Request $request): Response
    {
        $response = response('', 200);
        return $this->addCorsHeaders($request, $response);
    }

    /**
     * Add CORS headers to response
     */
    private function addCorsHeaders(Request $request, Response $response): Response
    {
        $allowedOrigins = $this->getAllowedOrigins();
        $origin = $request->headers->get('Origin');

        // Handle null origin (same-origin requests, direct access, etc.)
        if ($origin === null) {
            // For same-origin requests or direct access, we can be more permissive
            // but still secure by checking if it's a local development request
            if ($this->isLocalRequest($request)) {
                $allowedOrigin = $request->getSchemeAndHttpHost();
                $response->headers->set('Access-Control-Allow-Origin', $allowedOrigin);
                $response->headers->set('Access-Control-Allow-Credentials', 'true');

                // Log in development for debugging
                if (config('app.debug')) {
                    \Log::debug('CORS: Allowed null origin for local request', [
                        'allowed_origin' => $allowedOrigin,
                        'request_host' => $request->getHost(),
                        'request_ip' => $request->ip(),
                    ]);
                }
            }
        } else {
            // Check if the origin is allowed
            if (in_array($origin, $allowedOrigins) || $this->isOriginAllowed($origin, $allowedOrigins)) {
                $response->headers->set('Access-Control-Allow-Origin', $origin);
                $response->headers->set('Access-Control-Allow-Credentials', 'true');

                // Log in development for debugging
                if (config('app.debug')) {
                    \Log::debug('CORS: Allowed origin', [
                        'origin' => $origin,
                        'allowed_origins' => $allowedOrigins,
                    ]);
                }
            } else {
                // Log rejected origins in development
                if (config('app.debug')) {
                    \Log::warning('CORS: Rejected origin', [
                        'origin' => $origin,
                        'allowed_origins' => $allowedOrigins,
                    ]);
                }
            }
        }

        $response->headers->set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        $response->headers->set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, X-CSRF-TOKEN');
        $response->headers->set('Access-Control-Max-Age', '86400'); // Cache preflight for 24 hours

        return $response;
    }

    /**
     * Get allowed origins from configuration
     */
    private function getAllowedOrigins(): array
    {
        $origins = [
            config('app.frontend_url', 'http://localhost:3000'),
            config('app.url', 'http://localhost:8000'),
            // Add your production frontend URL here
            // 'https://yourdomain.com',
        ];

        // Filter out null/empty values and ensure they're strings
        return array_filter($origins, function ($origin) {
            return is_string($origin) && !empty($origin);
        });
    }

    /**
     * Check if origin matches allowed patterns (for subdomain support)
     */
    private function isOriginAllowed(?string $origin, array $allowedOrigins): bool
    {
        // Return false if origin is null
        if ($origin === null) {
            return false;
        }

        foreach ($allowedOrigins as $allowed) {
            // Support wildcard subdomains like *.yourdomain.com
            if (str_contains($allowed, '*')) {
                $pattern = str_replace('*', '.*', preg_quote($allowed, '/'));
                if (preg_match('/^' . $pattern . '$/', $origin)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Check if the request is from a local development environment
     */
    private function isLocalRequest(Request $request): bool
    {
        $host = $request->getHost();
        $ip = $request->ip();

        // Check for localhost, local IPs, and development domains
        $localHosts = [
            'localhost',
            '127.0.0.1',
            '::1',
            '0.0.0.0',
        ];

        // Check if host is localhost or local IP
        if (in_array($host, $localHosts)) {
            return true;
        }

        // Check if IP is local
        if (in_array($ip, ['127.0.0.1', '::1']) || str_starts_with($ip, '192.168.') || str_starts_with($ip, '10.')) {
            return true;
        }

        // Check for .local domains (common in development)
        if (str_ends_with($host, '.local')) {
            return true;
        }

        return false;
    }
}
