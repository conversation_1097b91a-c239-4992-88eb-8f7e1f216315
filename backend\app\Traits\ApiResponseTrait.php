<?php

namespace App\Traits;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Pagination\LengthAwarePaginator;

trait ApiResponseTrait
{
    /**
     * Return a successful response
     */
    protected function successResponse(
        $data = null,
        string $message = 'Operation successful',
        int $statusCode = 200,
        array $meta = []
    ): JsonResponse {
        $response = [
            'success' => true,
            'message' => $message,
            'timestamp' => now()->toISOString(),
            'request_id' => request()->header('X-Request-ID', uniqid()),
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        if (!empty($meta)) {
            $response['meta'] = $meta;
        }

        return response()->json($response, $statusCode);
    }

    /**
     * Return a paginated response
     */
    protected function paginatedResponse(
        LengthAwarePaginator $paginator,
        string $message = 'Data retrieved successfully'
    ): JsonResponse {
        return $this->successResponse(
            $paginator->items(),
            $message,
            200,
            [
                'pagination' => [
                    'current_page' => $paginator->currentPage(),
                    'last_page' => $paginator->lastPage(),
                    'per_page' => $paginator->perPage(),
                    'total' => $paginator->total(),
                    'from' => $paginator->firstItem(),
                    'to' => $paginator->lastItem(),
                    'has_more_pages' => $paginator->hasMorePages(),
                    'links' => [
                        'first' => $paginator->url(1),
                        'last' => $paginator->url($paginator->lastPage()),
                        'prev' => $paginator->previousPageUrl(),
                        'next' => $paginator->nextPageUrl(),
                    ],
                ],
            ]
        );
    }

    /**
     * Return a resource response
     */
    protected function resourceResponse(
        JsonResource $resource,
        string $message = 'Resource retrieved successfully',
        int $statusCode = 200
    ): JsonResponse {
        return $this->successResponse(
            $resource->resolve(),
            $message,
            $statusCode
        );
    }

    /**
     * Return a created response
     */
    protected function createdResponse(
        $data = null,
        string $message = 'Resource created successfully'
    ): JsonResponse {
        return $this->successResponse($data, $message, 201);
    }

    /**
     * Return an updated response
     */
    protected function updatedResponse(
        $data = null,
        string $message = 'Resource updated successfully'
    ): JsonResponse {
        return $this->successResponse($data, $message, 200);
    }

    /**
     * Return a deleted response
     */
    protected function deletedResponse(
        string $message = 'Resource deleted successfully'
    ): JsonResponse {
        return $this->successResponse(null, $message, 200);
    }

    /**
     * Return a no content response
     */
    protected function noContentResponse(): JsonResponse
    {
        return response()->json(null, 204);
    }

    /**
     * Return an error response
     */
    protected function errorResponse(
        string $message = 'An error occurred',
        int $statusCode = 500,
        string $errorCode = 'INTERNAL_ERROR',
        array $errors = [],
        array $context = []
    ): JsonResponse {
        $response = [
            'success' => false,
            'error' => [
                'code' => $errorCode,
                'message' => $message,
                'status_code' => $statusCode,
            ],
            'timestamp' => now()->toISOString(),
            'request_id' => request()->header('X-Request-ID', uniqid()),
        ];

        if (!empty($errors)) {
            $response['errors'] = $errors;
        }

        // Add context in development/testing environments
        if (config('app.debug') && !empty($context)) {
            $response['debug'] = $context;
        }

        // Log the error
        $this->logApiError($message, $statusCode, $errorCode, $errors, $context);

        return response()->json($response, $statusCode);
    }

    /**
     * Return a validation error response
     */
    protected function validationErrorResponse(
        array $errors,
        string $message = 'Validation failed'
    ): JsonResponse {
        return $this->errorResponse(
            $message,
            422,
            'VALIDATION_FAILED',
            $errors
        );
    }

    /**
     * Return an unauthorized response
     */
    protected function unauthorizedResponse(
        string $message = 'Unauthorized access'
    ): JsonResponse {
        return $this->errorResponse($message, 401, 'UNAUTHORIZED');
    }

    /**
     * Return a forbidden response
     */
    protected function forbiddenResponse(
        string $message = 'Access forbidden'
    ): JsonResponse {
        return $this->errorResponse($message, 403, 'FORBIDDEN');
    }

    /**
     * Return a not found response
     */
    protected function notFoundResponse(
        string $message = 'Resource not found'
    ): JsonResponse {
        return $this->errorResponse($message, 404, 'NOT_FOUND');
    }

    /**
     * Return a conflict response
     */
    protected function conflictResponse(
        string $message = 'Resource conflict',
        array $context = []
    ): JsonResponse {
        return $this->errorResponse($message, 409, 'CONFLICT', [], $context);
    }

    /**
     * Return a server error response
     */
    protected function serverErrorResponse(
        string $message = 'Internal server error',
        array $context = []
    ): JsonResponse {
        return $this->errorResponse($message, 500, 'INTERNAL_ERROR', [], $context);
    }

    /**
     * Log API errors
     */
    private function logApiError(
        string $message,
        int $statusCode,
        string $errorCode,
        array $errors = [],
        array $context = []
    ): void {
        $logData = [
            'error_code' => $errorCode,
            'message' => $message,
            'status_code' => $statusCode,
            'errors' => $errors,
            'context' => $context,
            'request_id' => request()->header('X-Request-ID', uniqid()),
            'user_id' => auth()->id(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'url' => request()->fullUrl(),
            'method' => request()->method(),
        ];

        // Log based on severity
        if ($statusCode >= 500) {
            \Log::error('API Error: ' . $message, $logData);
        } elseif ($statusCode >= 400) {
            \Log::warning('API Warning: ' . $message, $logData);
        } else {
            \Log::info('API Info: ' . $message, $logData);
        }
    }
}
