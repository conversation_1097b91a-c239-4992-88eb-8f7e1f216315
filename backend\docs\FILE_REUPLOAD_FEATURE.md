# File Re-upload Feature Implementation

## Overview

This document describes the implementation of the file re-upload feature for the "My Orders" page, allowing users to replace files after placing an order while maintaining a complete audit trail.

## Features Implemented

### 1. Database Schema Changes

#### New Fields in `order_files` Table
- `previous_file_id`: Foreign key linking to the previous version of the file
- `re_upload_reason`: Text field storing the reason for re-upload
- `re_uploaded_at`: Timestamp when the file was re-uploaded
- `version`: Integer tracking the version number of the file
- `is_current_version`: <PERSON><PERSON><PERSON> indicating if this is the current active version

#### New `order_file_histories` Table
Complete audit trail table tracking all file operations:
- File upload history
- Re-upload history with before/after metadata
- Action types: uploaded, re_uploaded, deleted, approved, rejected
- Complete file metadata preservation
- User tracking for all actions

### 2. Models and Relationships

#### OrderFile Model Enhancements
- New relationships: `previousFile()`, `nextFile()`, `histories()`
- Version management methods: `getAllVersions()`, `isCurrentVersion()`
- Re-upload tracking: `hasBeenReUploaded()`, `markAsReplaced()`
- History creation: `createReUploadHistory()`

#### New OrderFileHistory Model
- Complete audit trail management
- Static factory methods for creating history records
- Formatted file size accessors
- Action type management

### 3. Validation Services

#### Extended FileValidationService
- `validateFileForReUpload()`: Validates new files against original requirements
- `validateReUploadReason()`: Ensures proper reason documentation
- `validateOrderStatusForReUpload()`: Prevents re-uploads in inappropriate order states
- `validateReUploadPermissions()`: Ensures proper user permissions

### 4. Notification System

#### FileReUploadNotificationService
- Automatic notifications to users when admins re-upload files
- Automatic notifications to admins when users re-upload files
- Failure notifications for failed re-upload attempts
- Customizable email templates with complete context

### 5. API Endpoint

#### POST `/api/orders/{orderId}/files/{fileId}/reupload`

**Request Parameters:**
- `file`: The new file to upload (required)
- `reason`: Reason for re-uploading (optional, but configurable to be required)

**Response:**
```json
{
    "success": true,
    "message": "File re-uploaded successfully",
    "data": {
        "id": 123,
        "original_name": "new_design.pdf",
        "file_name": "uuid-filename.pdf",
        "file_size": 1024000,
        "version": 2,
        "is_current_version": true,
        "re_uploaded_at": "2025-07-20T10:30:00Z",
        "re_upload_reason": "Updated design per customer request"
    }
}
```

### 6. Order Status Management

Automatic order status updates based on current status:

- **Completed → Quality Check**: When files are re-uploaded after completion
- **Shipped → Quality Check**: Requires admin attention
- **Delivered → Quality Check**: Urgent admin attention required
- **In Production → Confirmed**: Moves back for review
- **Other statuses**: Adds notes without status change

### 7. Configuration Settings

New configurable settings in `FileUploadSetting`:

- `reupload_same_type_only`: Require same file type as original (default: false)
- `reupload_maintain_file_type`: Maintain file category (artwork/reference/proof) (default: true)
- `reupload_require_reason`: Require reason for re-upload (default: true)
- `reupload_allowed_statuses`: Order statuses that allow re-uploads (default: pending, confirmed, in_production, quality_check)

## Security Features

### 1. Permission Validation
- Users can only re-upload files for their own orders
- Admins can re-upload files for any order
- Files must belong to the specified order
- Only current versions can be re-uploaded

### 2. Order Status Restrictions
- Configurable list of order statuses that allow re-uploads
- Prevents re-uploads in inappropriate states (e.g., cancelled orders)

### 3. File Validation
- Same validation rules as original uploads
- Optional enforcement of same file type
- File size and format restrictions maintained

## Audit Trail

### Complete History Tracking
- Every file operation is logged in `order_file_histories`
- Before and after file metadata preserved
- User attribution for all actions
- Timestamp tracking for all operations

### Version Management
- Sequential version numbering
- Previous file relationships maintained
- Current version flagging
- Complete version history accessible

## Error Handling

### Graceful Failure Management
- Database transaction rollback on errors
- Automatic failure notifications
- Detailed error logging
- User-friendly error messages

### Edge Case Handling
- File storage failures
- Compression errors
- Notification delivery failures
- Database constraint violations

## Usage Examples

### Basic Re-upload
```bash
curl -X POST \
  http://localhost/api/orders/123/files/456/reupload \
  -H 'Authorization: Bearer {token}' \
  -F 'file=@new_design.pdf' \
  -F 'reason=Updated design per customer feedback'
```

### Admin Re-upload
```bash
curl -X POST \
  http://localhost/api/orders/123/files/456/reupload \
  -H 'Authorization: Bearer {admin_token}' \
  -F 'file=@corrected_file.pdf' \
  -F 'reason=Corrected file quality issues'
```

## Integration Points

### Frontend Integration
- Add "Re-upload File" button to My Orders page
- File upload component with reason input
- Progress indicators and error handling
- Success notifications

### Admin Panel Integration
- File history viewing in order details
- Re-upload functionality for admin users
- Audit trail display
- Status change notifications

## Testing Recommendations

### Unit Tests
- File validation logic
- Permission checking
- Order status updates
- History record creation

### Integration Tests
- Complete re-upload workflow
- Notification delivery
- Database transaction integrity
- File storage operations

### Edge Case Tests
- Invalid file types
- Insufficient permissions
- Order status restrictions
- Storage failures

## Monitoring and Maintenance

### Logging
- All re-upload operations logged
- Error conditions tracked
- Performance metrics available
- Audit trail preservation

### Cleanup Considerations
- Old file versions storage management
- History record retention policies
- Notification cleanup procedures
- Performance optimization for large histories

## Future Enhancements

### Potential Improvements
- Bulk file re-upload functionality
- File comparison tools
- Automated quality checks
- Integration with external review systems
- Advanced notification templates
- File versioning UI components
