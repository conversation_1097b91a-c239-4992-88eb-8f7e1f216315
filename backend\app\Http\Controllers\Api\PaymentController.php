<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
// CreditPackage model removed - no longer needed
use App\Models\WalletTransaction;
use App\Services\BillplzService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class PaymentController extends Controller
{
    protected $billplzService;

    public function __construct(BillplzService $billplzService)
    {
        $this->billplzService = $billplzService;

        // Add middleware to check if Billplz is configured for payment endpoints
        $this->middleware(function ($request, $next) {
            if (!$this->billplzService->isConfigured()) {
                return response()->json([
                    'error' => 'Payment service is not configured. Please contact administrator.',
                    'billplz_configured' => false,
                ], 503);
            }
            return $next($request);
        })->except(['getPaymentConfig']);
    }

    /**
     * Create a payment for wallet top-up
     * @deprecated Credit package functionality has been removed
     */
    public function createPayment(Request $request): JsonResponse
    {
        // Credit package functionality has been removed
        // Return error message for backward compatibility
        return response()->json([
            'success' => false,
            'error' => 'Credit package functionality has been removed. Please use alternative wallet top-up methods.',
            'message' => 'The credit package system is no longer available. Please contact support for alternative top-up methods.',
        ], 410); // 410 Gone - resource no longer available
    }

    /**
     * Handle Billplz callback
     */
    public function billplzCallback(Request $request): JsonResponse
    {
        try {
            $result = $this->billplzService->handleCallback($request->all());

            if (!$result['success']) {
                return response()->json([
                    'error' => $result['error'],
                ], 400);
            }

            return response()->json([
                'success' => true,
                'transaction_id' => $result['transaction_id'],
                'status' => $result['status'],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Payment service error: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Check payment status
     */
    public function checkPaymentStatus(Request $request): JsonResponse
    {
        $request->validate([
            'transaction_id' => 'required|exists:credit_transactions,id',
        ]);

        $user = $request->user();
        $transaction = WalletTransaction::where('id', $request->transaction_id)
            ->where('user_id', $user->id)
            ->firstOrFail();

        // If payment is still pending and we have a bill ID, check with Billplz
        if ($transaction->payment_status === 'pending' && $transaction->payment_reference) {
            try {
                $billStatus = $this->billplzService->getBillStatus($transaction->payment_reference);

                if ($billStatus['success']) {
                    $billData = $billStatus['data'];

                    // Update transaction if status changed
                    if (isset($billData['state']) && $billData['state'] === 'paid' && $transaction->payment_status !== 'completed') {
                        $this->billplzService->handleCallback($billData);
                        $transaction->refresh();
                    }
                }
            } catch (\Exception $e) {
                // Log error but don't fail the request - just return current status
                \Log::warning('Failed to check Billplz status: ' . $e->getMessage());
            }
        }

        return response()->json([
            'transaction_id' => $transaction->id,
            'payment_status' => $transaction->payment_status,
            'type' => $transaction->type,
            'amount' => $transaction->amount,
            'formatted_amount' => $transaction->getFormattedAmount(),
            'amount_paid' => $transaction->amount_paid,
            'formatted_amount_paid' => $transaction->getFormattedAmountPaid(),
            'processed_at' => $transaction->processed_at?->format('Y-m-d H:i:s'),
            'description' => $transaction->description,
        ]);
    }

    /**
     * Get payment configuration
     */
    public function getPaymentConfig(): JsonResponse
    {
        try {
            return response()->json([
                'billplz_enabled' => $this->billplzService->isEnabled(),
                'billplz_configured' => $this->billplzService->isConfigured(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'billplz_enabled' => false,
                'billplz_configured' => false,
                'error' => 'Payment configuration error: ' . $e->getMessage(),
            ]);
        }
    }
}
