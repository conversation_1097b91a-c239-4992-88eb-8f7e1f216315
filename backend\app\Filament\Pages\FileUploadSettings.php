<?php

namespace App\Filament\Pages;

use App\Models\FileUploadSetting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Notifications\Notification;

class FileUploadSettings extends Page implements Forms\Contracts\HasForms
{
    use Forms\Concerns\InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-cloud-arrow-up';
    protected static ?string $navigationGroup = 'Printing Services';
    protected static ?string $navigationLabel = 'File Upload Settings';
    protected static ?int $navigationSort = 4;
    protected static string $view = 'filament.pages.file-upload-settings';

    public ?array $data = [];

    public function mount(): void
    {
        // Load current settings with defaults
        $formData = [
            // File Format Settings
            'allowed_file_types' => FileUploadSetting::get('allowed_file_types', ['pdf', 'ai', 'eps', 'png', 'jpg', 'jpeg', 'tiff', 'svg']),
            'max_file_size_mb' => (int) FileUploadSetting::get('max_file_size_mb', 50),
            'max_total_upload_size_mb' => (int) FileUploadSetting::get('max_total_upload_size_mb', 200),
            'max_files_per_order' => (int) FileUploadSetting::get('max_files_per_order', 10),

            // DPI Settings
            'min_dpi_requirement' => (int) FileUploadSetting::get('min_dpi_requirement', 300),
            'dpi_warning_threshold' => (int) FileUploadSetting::get('dpi_warning_threshold', 150),
            'enable_dpi_validation' => (bool) FileUploadSetting::get('enable_dpi_validation', true),
            'auto_dpi_detection' => (bool) FileUploadSetting::get('auto_dpi_detection', true),

            // Dimension Settings
            'min_width_px' => (int) FileUploadSetting::get('min_width_px', 100),
            'min_height_px' => (int) FileUploadSetting::get('min_height_px', 100),
            'max_width_px' => (int) FileUploadSetting::get('max_width_px', 10000),
            'max_height_px' => (int) FileUploadSetting::get('max_height_px', 10000),
            'enable_dimension_validation' => (bool) FileUploadSetting::get('enable_dimension_validation', true),

            // Compression Settings
            'enable_auto_compression' => (bool) FileUploadSetting::get('enable_auto_compression', false),
            'compression_quality' => (int) FileUploadSetting::get('compression_quality', 85),
            'compression_threshold_mb' => (int) FileUploadSetting::get('compression_threshold_mb', 10),
        ];

        $this->data = $formData;
    }

    public function form(Form $form): Form
    {
        return $form
            ->statePath('data')
            ->schema([
                // File Format Settings Section
                Forms\Components\Section::make('File Format Settings')
                    ->description('Configure allowed file types and size limits')
                    ->schema([
                        Forms\Components\CheckboxList::make('allowed_file_types')
                            ->label('Allowed File Types')
                            ->helperText('Select which file formats are allowed for upload')
                            ->options([
                                'pdf' => 'PDF - Portable Document Format',
                                'ai' => 'AI - Adobe Illustrator',
                                'eps' => 'EPS - Encapsulated PostScript',
                                'png' => 'PNG - Portable Network Graphics',
                                'jpg' => 'JPG - JPEG Image',
                                'jpeg' => 'JPEG - JPEG Image',
                                'tiff' => 'TIFF - Tagged Image File Format',
                                'svg' => 'SVG - Scalable Vector Graphics',
                            ])
                            ->columns(2)
                            ->required(),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('max_file_size_mb')
                                    ->label('Maximum File Size (MB)')
                                    ->helperText('Maximum size per individual file')
                                    ->numeric()
                                    ->minValue(1)
                                    ->maxValue(500)
                                    ->suffix('MB')
                                    ->required(),

                                Forms\Components\TextInput::make('max_total_upload_size_mb')
                                    ->label('Maximum Total Upload Size (MB)')
                                    ->helperText('Maximum total size for all files per order')
                                    ->numeric()
                                    ->minValue(1)
                                    ->maxValue(1000)
                                    ->suffix('MB')
                                    ->required(),
                            ]),

                        Forms\Components\TextInput::make('max_files_per_order')
                            ->label('Maximum Files Per Order')
                            ->helperText('Maximum number of files that can be uploaded per order')
                            ->numeric()
                            ->minValue(1)
                            ->maxValue(50)
                            ->required(),
                    ])->columns(1),

                // DPI and Quality Settings Section
                Forms\Components\Section::make('DPI & Quality Settings')
                    ->description('Configure resolution and quality requirements for print-ready files')
                    ->schema([
                        Forms\Components\Toggle::make('enable_dpi_validation')
                            ->label('Enable DPI Validation')
                            ->helperText('Validate DPI requirements for uploaded images')
                            ->default(true),

                        Forms\Components\Toggle::make('auto_dpi_detection')
                            ->label('Automatic DPI Detection')
                            ->helperText('Automatically detect and extract DPI from image files')
                            ->default(true),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('min_dpi_requirement')
                                    ->label('Minimum DPI Requirement')
                                    ->helperText('Minimum DPI for print-ready files (typically 300)')
                                    ->numeric()
                                    ->minValue(72)
                                    ->maxValue(1200)
                                    ->suffix('DPI')
                                    ->required(),

                                Forms\Components\TextInput::make('dpi_warning_threshold')
                                    ->label('DPI Warning Threshold')
                                    ->helperText('Show warning below this DPI value')
                                    ->numeric()
                                    ->minValue(72)
                                    ->maxValue(600)
                                    ->suffix('DPI')
                                    ->required(),
                            ]),
                    ])->columns(1),

                // Dimension Settings Section
                Forms\Components\Section::make('Dimension Settings')
                    ->description('Configure image dimension requirements and validation')
                    ->schema([
                        Forms\Components\Toggle::make('enable_dimension_validation')
                            ->label('Enable Dimension Validation')
                            ->helperText('Validate image dimensions for uploaded files')
                            ->default(true),

                        Forms\Components\Fieldset::make('Minimum Dimensions')
                            ->schema([
                                Forms\Components\Grid::make(2)
                                    ->schema([
                                        Forms\Components\TextInput::make('min_width_px')
                                            ->label('Minimum Width')
                                            ->helperText('Minimum image width in pixels')
                                            ->numeric()
                                            ->minValue(1)
                                            ->maxValue(50000)
                                            ->suffix('px')
                                            ->required(),

                                        Forms\Components\TextInput::make('min_height_px')
                                            ->label('Minimum Height')
                                            ->helperText('Minimum image height in pixels')
                                            ->numeric()
                                            ->minValue(1)
                                            ->maxValue(50000)
                                            ->suffix('px')
                                            ->required(),
                                    ]),
                            ]),

                        Forms\Components\Fieldset::make('Maximum Dimensions')
                            ->schema([
                                Forms\Components\Grid::make(2)
                                    ->schema([
                                        Forms\Components\TextInput::make('max_width_px')
                                            ->label('Maximum Width')
                                            ->helperText('Maximum image width in pixels')
                                            ->numeric()
                                            ->minValue(100)
                                            ->maxValue(100000)
                                            ->suffix('px')
                                            ->required(),

                                        Forms\Components\TextInput::make('max_height_px')
                                            ->label('Maximum Height')
                                            ->helperText('Maximum image height in pixels')
                                            ->numeric()
                                            ->minValue(100)
                                            ->maxValue(100000)
                                            ->suffix('px')
                                            ->required(),
                                    ]),
                            ]),
                    ])->columns(1),

                // Compression Settings Section
                Forms\Components\Section::make('Compression Settings')
                    ->description('Configure automatic file compression options')
                    ->schema([
                        Forms\Components\Toggle::make('enable_auto_compression')
                            ->label('Enable Automatic Compression')
                            ->helperText('Automatically compress large files to reduce size')
                            ->default(false),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('compression_quality')
                                    ->label('Compression Quality')
                                    ->helperText('Quality level for compression (1-100, higher = better quality)')
                                    ->numeric()
                                    ->minValue(1)
                                    ->maxValue(100)
                                    ->suffix('%')
                                    ->required(),

                                Forms\Components\TextInput::make('compression_threshold_mb')
                                    ->label('Compression Threshold')
                                    ->helperText('File size threshold to trigger compression')
                                    ->numeric()
                                    ->minValue(1)
                                    ->maxValue(100)
                                    ->suffix('MB')
                                    ->required(),
                            ]),
                    ])->columns(1),
            ])
            ->statePath('data');
    }

    public function save(): void
    {
        $data = $this->form->getState();

        // Save File Format Settings
        FileUploadSetting::set('allowed_file_types', $data['allowed_file_types'], 'json');
        FileUploadSetting::set('max_file_size_mb', $data['max_file_size_mb'], 'integer');
        FileUploadSetting::set('max_total_upload_size_mb', $data['max_total_upload_size_mb'], 'integer');
        FileUploadSetting::set('max_files_per_order', $data['max_files_per_order'], 'integer');

        // Save DPI Settings
        FileUploadSetting::set('enable_dpi_validation', $data['enable_dpi_validation'], 'boolean');
        FileUploadSetting::set('auto_dpi_detection', $data['auto_dpi_detection'], 'boolean');
        FileUploadSetting::set('min_dpi_requirement', $data['min_dpi_requirement'], 'integer');
        FileUploadSetting::set('dpi_warning_threshold', $data['dpi_warning_threshold'], 'integer');

        // Save Dimension Settings
        FileUploadSetting::set('enable_dimension_validation', $data['enable_dimension_validation'], 'boolean');
        FileUploadSetting::set('min_width_px', $data['min_width_px'], 'integer');
        FileUploadSetting::set('min_height_px', $data['min_height_px'], 'integer');
        FileUploadSetting::set('max_width_px', $data['max_width_px'], 'integer');
        FileUploadSetting::set('max_height_px', $data['max_height_px'], 'integer');

        // Save Compression Settings
        FileUploadSetting::set('enable_auto_compression', $data['enable_auto_compression'], 'boolean');
        FileUploadSetting::set('compression_quality', $data['compression_quality'], 'integer');
        FileUploadSetting::set('compression_threshold_mb', $data['compression_threshold_mb'], 'integer');

        Notification::make()
            ->title('File upload settings saved successfully')
            ->body('All file upload configuration settings have been updated.')
            ->success()
            ->send();
    }
}
