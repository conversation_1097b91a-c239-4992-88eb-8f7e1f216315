{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\frontend\\\\src\\\\pages\\\\About.tsx\";\nimport React from 'react';\nimport StaticPage from '../components/pages/StaticPage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst About = () => {\n  return /*#__PURE__*/_jsxDEV(StaticPage, {\n    slug: \"about\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 10\n  }, this);\n};\n_c = About;\nexport default About;\nvar _c;\n$RefreshReg$(_c, \"About\");", "map": {"version": 3, "names": ["React", "StaticPage", "jsxDEV", "_jsxDEV", "About", "slug", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/frontend/src/pages/About.tsx"], "sourcesContent": ["import React from 'react';\nimport StaticPage from '../components/pages/StaticPage';\n\nconst About: React.FC = () => {\n  return <StaticPage slug=\"about\" />;\n};\n\nexport default About;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,KAAe,GAAGA,CAAA,KAAM;EAC5B,oBAAOD,OAAA,CAACF,UAAU;IAACI,IAAI,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACpC,CAAC;AAACC,EAAA,GAFIN,KAAe;AAIrB,eAAeA,KAAK;AAAC,IAAAM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}