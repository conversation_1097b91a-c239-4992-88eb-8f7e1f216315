<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PrintingProductResource\Pages;
use App\Filament\Resources\PrintingProductResource\RelationManagers;
use App\Models\PrintingProduct;
use App\Models\PrintingCategory;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;
use Filament\Forms\Get;
use Filament\Forms\Set;

class PrintingProductResource extends Resource
{
    protected static ?string $model = PrintingProduct::class;

    protected static ?string $navigationIcon = 'heroicon-o-cube';

    protected static ?string $navigationGroup = 'Printing Services';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Product Information')
                    ->schema([
                        Forms\Components\Select::make('printing_category_id')
                            ->label('Category')
                            ->options(PrintingCategory::active()->pluck('name', 'id'))
                            ->required()
                            ->searchable(),

                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->live(onBlur: true)
                            ->afterStateUpdated(fn (string $context, $state, Forms\Set $set) =>
                                $context === 'create' ? $set('slug', Str::slug($state)) : null
                            ),

                        Forms\Components\TextInput::make('slug')
                            ->required()
                            ->maxLength(255)
                            ->unique(PrintingProduct::class, 'slug', ignoreRecord: true)
                            ->rules(['alpha_dash']),

                        Forms\Components\Textarea::make('description')
                            ->maxLength(1000)
                            ->rows(3),

                        Forms\Components\FileUpload::make('image')
                            ->image()
                            ->directory('printing-products')
                            ->visibility('public'),
                    ])->columns(2),

                Forms\Components\Section::make('Pricing & Quantity')
                    ->schema([
                        Forms\Components\TextInput::make('base_price')
                            ->required()
                            ->numeric()
                            ->prefix('RM')
                            ->minValue(0)
                            ->step(0.01),

                        Forms\Components\TextInput::make('min_quantity')
                            ->required()
                            ->numeric()
                            ->default(1)
                            ->minValue(1),

                        Forms\Components\TextInput::make('max_quantity')
                            ->numeric()
                            ->minValue(1),

                        Forms\Components\TextInput::make('production_time_days')
                            ->required()
                            ->numeric()
                            ->default(3)
                            ->minValue(1)
                            ->suffix('days'),
                    ])->columns(2),

                Forms\Components\Section::make('Specifications')
                    ->schema([
                        Forms\Components\KeyValue::make('specifications')
                            ->keyLabel('Specification')
                            ->valueLabel('Value')
                            ->addActionLabel('Add specification'),
                    ]),

                Forms\Components\Section::make('Options & Pricing')
                    ->schema([
                        Forms\Components\Tabs::make('pricing_tabs')
                            ->tabs([
                                Forms\Components\Tabs\Tab::make('Quantity Pricing')
                                    ->schema([
                                        Forms\Components\Repeater::make('quantity_pricing_tiers')
                                            ->label('Quantity Pricing Tiers')
                                            ->schema([
                                                Forms\Components\TextInput::make('min_quantity')
                                                    ->label('Minimum Quantity')
                                                    ->required()
                                                    ->numeric()
                                                    ->minValue(1)
                                                    ->helperText('Orders of this quantity or more will use this price')
                                                    ->rules(['integer', 'min:1']),

                                                Forms\Components\TextInput::make('price_per_unit')
                                                    ->label('Price per Unit')
                                                    ->required()
                                                    ->numeric()
                                                    ->minValue(0)
                                                    ->step(0.01)
                                                    ->prefix('RM')
                                                    ->helperText('Price per unit for this quantity tier')
                                                    ->rules(['numeric', 'min:0']),
                                            ])
                                            ->columns(2)
                                            ->defaultItems(1)
                                            ->addActionLabel('Add Pricing Tier')
                                            ->reorderable()
                                            ->collapsible()
                                            ->itemLabel(fn (array $state): ?string =>
                                                isset($state['min_quantity']) && isset($state['price_per_unit'])
                                                    ? "Min {$state['min_quantity']} units - RM {$state['price_per_unit']}"
                                                    : 'New Pricing Tier'
                                            )
                                            ->helperText('Define different prices based on order quantity. Tiers are applied automatically - higher quantities get better rates.')
                                            ->afterStateHydrated(function (Forms\Components\Repeater $component, ?PrintingProduct $record) {
                                                if ($record && $record->options && isset($record->options['quantity_pricing'])) {
                                                    $component->state($record->options['quantity_pricing']);
                                                }
                                            })
                                            ->dehydrated(false),
                                    ]),

                                Forms\Components\Tabs\Tab::make('Product Options')
                                    ->schema([
                                        Forms\Components\Repeater::make('product_options')
                                            ->label('Product Options')
                                            ->schema([
                                                Forms\Components\TextInput::make('option_name')
                                                    ->label('Option Name')
                                                    ->required()
                                                    ->helperText('e.g., "finish", "sides", "paper_type"')
                                                    ->placeholder('finish'),

                                                Forms\Components\TextInput::make('option_label')
                                                    ->label('Display Label')
                                                    ->required()
                                                    ->helperText('User-friendly label shown to customers')
                                                    ->placeholder('Finish Type'),

                                                Forms\Components\Repeater::make('option_values')
                                                    ->label('Available Choices')
                                                    ->schema([
                                                        Forms\Components\TextInput::make('value')
                                                            ->label('Value')
                                                            ->required()
                                                            ->helperText('Internal value (e.g., "matt", "gloss")')
                                                            ->placeholder('matt'),

                                                        Forms\Components\TextInput::make('label')
                                                            ->label('Display Label')
                                                            ->required()
                                                            ->helperText('Label shown to customers')
                                                            ->placeholder('Matt Lamination'),

                                                        Forms\Components\TextInput::make('price_modifier')
                                                            ->label('Price Modifier')
                                                            ->numeric()
                                                            ->step(0.01)
                                                            ->default(0)
                                                            ->prefix('RM')
                                                            ->helperText('Additional cost for this option (0 for no extra cost)'),
                                                    ])
                                                    ->columns(3)
                                                    ->defaultItems(1)
                                                    ->addActionLabel('Add Choice')
                                                    ->itemLabel(fn (array $state): ?string =>
                                                        isset($state['label'])
                                                            ? $state['label'] . (isset($state['price_modifier']) && $state['price_modifier'] > 0 ? " (+RM {$state['price_modifier']})" : '')
                                                            : 'New Choice'
                                                    ),
                                            ])
                                            ->columns(1)
                                            ->defaultItems(0)
                                            ->addActionLabel('Add Product Option')
                                            ->collapsible()
                                            ->itemLabel(fn (array $state): ?string =>
                                                isset($state['option_label'])
                                                    ? $state['option_label']
                                                    : 'New Option'
                                            )
                                            ->helperText('Define customizable options like finish types, paper quality, etc. Each option can have different choices with individual pricing.')
                                            ->afterStateHydrated(function (Forms\Components\Repeater $component, ?PrintingProduct $record) {
                                                if ($record && $record->options) {
                                                    $component->state(static::extractProductOptionsFromJson($record->options));
                                                }
                                            })
                                            ->dehydrated(false),
                                    ]),

                                Forms\Components\Tabs\Tab::make('Advanced JSON')
                                    ->schema([
                                        Forms\Components\Textarea::make('options')
                                            ->label('Raw JSON Configuration')
                                            ->helperText('Advanced users can directly edit the JSON structure. This will override the structured fields above.')
                                            ->rows(15)
                                            ->placeholder('{"quantity_pricing": [{"min_quantity": 100, "price_per_unit": 0.25}]}')
                                            ->columnSpanFull()
                                            ->formatStateUsing(function ($state) {
                                                return is_array($state) ? json_encode($state, JSON_PRETTY_PRINT) : $state;
                                            })
                                            ->dehydrateStateUsing(function ($state, Get $get) {
                                                // If raw JSON is provided, use it
                                                if (!empty($state)) {
                                                    try {
                                                        $decoded = json_decode($state, true);
                                                        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                                                            return $decoded;
                                                        }
                                                    } catch (\Exception $e) {
                                                        // Fall through to structured data
                                                    }
                                                }

                                                // Otherwise, build from structured fields
                                                return static::buildOptionsFromStructuredFields($get);
                                            })
                                            ->rules([
                                                function () {
                                                    return function (string $attribute, $value, \Closure $fail) {
                                                        if (!empty($value)) {
                                                            $decoded = json_decode($value, true);
                                                            if (json_last_error() !== JSON_ERROR_NONE) {
                                                                $fail('The options field must contain valid JSON.');
                                                            }
                                                        }
                                                    };
                                                },
                                            ]),

                                        Forms\Components\Placeholder::make('json_help')
                                            ->label('JSON Structure Help')
                                            ->content(view('filament.components.json-help'))
                                            ->columnSpanFull(),
                                    ]),
                            ])
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Settings')
                    ->schema([
                        Forms\Components\Toggle::make('is_active')
                            ->default(true),

                        Forms\Components\TextInput::make('sort_order')
                            ->numeric()
                            ->default(0)
                            ->minValue(0),
                    ])->columns(2),

                Forms\Components\Section::make('SEO')
                    ->schema([
                        Forms\Components\TextInput::make('meta_title')
                            ->maxLength(255),

                        Forms\Components\Textarea::make('meta_description')
                            ->maxLength(500)
                            ->rows(2),
                    ])->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('image')
                    ->size(50),

                Tables\Columns\TextColumn::make('category.name')
                    ->label('Category')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('slug')
                    ->searchable()
                    ->copyable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('base_price')
                    ->money('MYR')
                    ->sortable(),

                Tables\Columns\TextColumn::make('min_quantity')
                    ->sortable(),

                Tables\Columns\TextColumn::make('production_time_days')
                    ->suffix(' days')
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('sort_order')
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('printing_category_id')
                    ->label('Category')
                    ->options(PrintingCategory::active()->pluck('name', 'id'))
                    ->searchable(),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active')
                    ->boolean()
                    ->trueLabel('Active only')
                    ->falseLabel('Inactive only')
                    ->native(false),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('sort_order');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPrintingProducts::route('/'),
            'create' => Pages\CreatePrintingProduct::route('/create'),
            'edit' => Pages\EditPrintingProduct::route('/{record}/edit'),
        ];
    }

    /**
     * Extract product options from JSON for structured form
     */
    protected static function extractProductOptionsFromJson(array $options): array
    {
        $productOptions = [];

        foreach ($options as $key => $value) {
            if (str_ends_with($key, '_options') && is_array($value)) {
                $optionName = str_replace('_options', '', $key);
                $optionValues = [];

                foreach ($value as $optionKey => $optionLabel) {
                    $priceModifier = 0;
                    if (isset($options['pricing'][$optionName][$optionKey])) {
                        $priceModifier = $options['pricing'][$optionName][$optionKey];
                    }

                    $optionValues[] = [
                        'value' => $optionKey,
                        'label' => $optionLabel,
                        'price_modifier' => $priceModifier,
                    ];
                }

                $productOptions[] = [
                    'option_name' => $optionName,
                    'option_label' => ucwords(str_replace('_', ' ', $optionName)),
                    'option_values' => $optionValues,
                ];
            }
        }

        return $productOptions;
    }

    /**
     * Build options JSON from structured form fields
     */
    protected static function buildOptionsFromStructuredFields(Get $get): array
    {
        $options = [];

        // Handle quantity pricing tiers
        $quantityTiers = $get('quantity_pricing_tiers');
        if (is_array($quantityTiers) && !empty($quantityTiers)) {
            $options['quantity_pricing'] = array_values(array_filter(array_map(function ($tier) {
                if (isset($tier['min_quantity']) && isset($tier['price_per_unit'])
                    && is_numeric($tier['min_quantity']) && is_numeric($tier['price_per_unit'])) {
                    return [
                        'min_quantity' => (int) $tier['min_quantity'],
                        'price_per_unit' => (float) $tier['price_per_unit'],
                    ];
                }
                return null;
            }, $quantityTiers), function ($tier) {
                return $tier !== null;
            }));
        }

        // Handle product options
        $productOptions = $get('product_options');
        if (is_array($productOptions) && !empty($productOptions)) {
            $pricing = [];

            foreach ($productOptions as $option) {
                if (!isset($option['option_name']) || !isset($option['option_values'])) {
                    continue;
                }

                $optionName = $option['option_name'];
                $optionsKey = $optionName . '_options';

                $options[$optionsKey] = [];
                $pricing[$optionName] = [];

                foreach ($option['option_values'] as $value) {
                    if (isset($value['value']) && isset($value['label'])) {
                        $options[$optionsKey][$value['value']] = $value['label'];
                        $pricing[$optionName][$value['value']] = floatval($value['price_modifier'] ?? 0);
                    }
                }
            }

            if (!empty($pricing)) {
                $options['pricing'] = $pricing;
            }
        }

        return $options;
    }
}
