<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_files', function (Blueprint $table) {
            // Add audit trail fields for file re-uploads
            $table->foreignId('previous_file_id')->nullable()->constrained('order_files')->onDelete('set null');
            $table->string('re_upload_reason')->nullable();
            $table->timestamp('re_uploaded_at')->nullable();
            $table->integer('version')->default(1);
            $table->boolean('is_current_version')->default(true);
            
            // Add indexes for performance
            $table->index(['printing_order_id', 'is_current_version']);
            $table->index(['previous_file_id']);
            $table->index(['re_uploaded_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_files', function (Blueprint $table) {
            $table->dropForeign(['previous_file_id']);
            $table->dropIndex(['printing_order_id', 'is_current_version']);
            $table->dropIndex(['previous_file_id']);
            $table->dropIndex(['re_uploaded_at']);
            
            $table->dropColumn([
                'previous_file_id',
                're_upload_reason',
                're_uploaded_at',
                'version',
                'is_current_version'
            ]);
        });
    }
};
