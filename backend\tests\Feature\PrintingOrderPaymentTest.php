<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\PrintingOrder;
use App\Models\PrintingProduct;
use App\Models\PrintingCategory;
use App\Models\WalletTransaction;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class PrintingOrderPaymentTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;
    protected PrintingProduct $product;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create([
            'wallet_balance' => 100.00,
        ]);

        $category = PrintingCategory::factory()->create();
        $this->product = PrintingProduct::factory()->create([
            'printing_category_id' => $category->id,
            'base_price' => 10.00,
            'is_active' => true,
        ]);
    }

    /** @test */
    public function it_can_create_order_with_sufficient_balance()
    {
        $orderData = [
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 2,
                    'specifications' => [],
                    'selected_options' => [],
                    'notes' => 'Test order',
                ]
            ],
            'special_instructions' => 'Handle with care',
        ];

        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/orders', $orderData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'order_number',
                    'total_amount',
                    'payment_status',
                    'status',
                ]
            ]);

        $order = PrintingOrder::first();
        $this->assertEquals('paid', $order->payment_status);
        $this->assertEquals('confirmed', $order->status);
        $this->assertEquals('wallet', $order->payment_method);
        $this->assertNotNull($order->payment_reference);

        // Check wallet transaction was created
        $transaction = WalletTransaction::where('user_id', $this->user->id)
            ->where('type', 'payment')
            ->first();

        $this->assertNotNull($transaction);
        $this->assertEquals(-$order->total_amount, $transaction->amount);
        $this->assertEquals('completed', $transaction->payment_status);
        $this->assertStringContains($order->order_number, $transaction->description);

        // Check user balance was deducted
        $this->user->refresh();
        $this->assertEquals(100.00 - $order->total_amount, $this->user->wallet_balance);
    }

    /** @test */
    public function it_rejects_order_with_insufficient_balance()
    {
        // Set user balance to a low amount
        $this->user->update(['wallet_balance' => 5.00]);

        $orderData = [
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 2, // This will cost more than 5.00
                    'specifications' => [],
                    'selected_options' => [],
                ]
            ],
        ];

        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/orders', $orderData);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
            ])
            ->assertJsonStructure([
                'message',
                'required_amount',
                'current_balance',
                'shortfall',
            ]);

        // Ensure no order was created
        $this->assertEquals(0, PrintingOrder::count());
        
        // Ensure no transaction was created
        $this->assertEquals(0, WalletTransaction::count());

        // Ensure balance wasn't changed
        $this->user->refresh();
        $this->assertEquals(5.00, $this->user->wallet_balance);
    }

    /** @test */
    public function it_can_process_payment_for_pending_order()
    {
        // Create an order manually with pending payment
        $order = PrintingOrder::create([
            'user_id' => $this->user->id,
            'order_number' => PrintingOrder::generateOrderNumber(),
            'total_amount' => 25.00,
            'status' => PrintingOrder::STATUS_PENDING,
            'payment_status' => PrintingOrder::PAYMENT_PENDING,
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson("/api/orders/{$order->id}/pay");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonStructure([
                'message',
                'data' => [
                    'payment_status',
                    'status',
                    'payment_reference',
                ],
                'transaction' => [
                    'id',
                    'amount',
                    'formatted_amount',
                    'description',
                ],
            ]);

        $order->refresh();
        $this->assertEquals('paid', $order->payment_status);
        $this->assertEquals('confirmed', $order->status);
        $this->assertNotNull($order->payment_reference);

        // Check user balance was deducted
        $this->user->refresh();
        $this->assertEquals(75.00, $this->user->wallet_balance);
    }

    /** @test */
    public function it_rejects_payment_for_insufficient_balance()
    {
        // Set user balance to insufficient amount
        $this->user->update(['wallet_balance' => 10.00]);

        $order = PrintingOrder::create([
            'user_id' => $this->user->id,
            'order_number' => PrintingOrder::generateOrderNumber(),
            'total_amount' => 25.00,
            'status' => PrintingOrder::STATUS_PENDING,
            'payment_status' => PrintingOrder::PAYMENT_PENDING,
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson("/api/orders/{$order->id}/pay");

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
            ])
            ->assertJsonStructure([
                'message',
                'required_amount',
                'current_balance',
                'shortfall',
            ]);

        $order->refresh();
        $this->assertEquals('pending', $order->payment_status);
        $this->assertEquals('pending', $order->status);
    }

    /** @test */
    public function it_provides_payment_info_for_order()
    {
        $order = PrintingOrder::create([
            'user_id' => $this->user->id,
            'order_number' => PrintingOrder::generateOrderNumber(),
            'total_amount' => 25.00,
            'status' => PrintingOrder::STATUS_PENDING,
            'payment_status' => PrintingOrder::PAYMENT_PENDING,
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
            ->getJson("/api/orders/{$order->id}/payment-info");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'order_id' => $order->id,
                    'total_amount' => 25.00,
                    'payment_status' => 'pending',
                    'user_balance' => 100.00,
                    'can_pay' => true,
                    'balance_sufficient' => true,
                    'shortfall' => 0,
                ]
            ]);
    }

    /** @test */
    public function it_shows_insufficient_balance_in_payment_info()
    {
        $this->user->update(['wallet_balance' => 10.00]);

        $order = PrintingOrder::create([
            'user_id' => $this->user->id,
            'order_number' => PrintingOrder::generateOrderNumber(),
            'total_amount' => 25.00,
            'status' => PrintingOrder::STATUS_PENDING,
            'payment_status' => PrintingOrder::PAYMENT_PENDING,
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
            ->getJson("/api/orders/{$order->id}/payment-info");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'user_balance' => 10.00,
                    'can_pay' => false,
                    'balance_sufficient' => false,
                    'shortfall' => 15.00,
                ]
            ]);
    }

    /** @test */
    public function it_prevents_payment_for_already_paid_order()
    {
        $order = PrintingOrder::create([
            'user_id' => $this->user->id,
            'order_number' => PrintingOrder::generateOrderNumber(),
            'total_amount' => 25.00,
            'status' => PrintingOrder::STATUS_CONFIRMED,
            'payment_status' => PrintingOrder::PAYMENT_PAID,
            'payment_reference' => 'WT-123',
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson("/api/orders/{$order->id}/pay");

        $response->assertStatus(404); // Order not found (filtered by pending payment status)
    }

    /** @test */
    public function it_prevents_access_to_other_users_orders()
    {
        $otherUser = User::factory()->create();
        
        $order = PrintingOrder::create([
            'user_id' => $otherUser->id,
            'order_number' => PrintingOrder::generateOrderNumber(),
            'total_amount' => 25.00,
            'status' => PrintingOrder::STATUS_PENDING,
            'payment_status' => PrintingOrder::PAYMENT_PENDING,
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson("/api/orders/{$order->id}/pay");

        $response->assertStatus(404);
    }

    /** @test */
    public function it_creates_proper_transaction_metadata()
    {
        $orderData = [
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 1,
                    'specifications' => [],
                    'selected_options' => [],
                ]
            ],
        ];

        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson('/api/orders', $orderData);

        $response->assertStatus(201);

        $order = PrintingOrder::first();
        $transaction = WalletTransaction::where('user_id', $this->user->id)->first();

        $this->assertNotNull($transaction->metadata);
        $this->assertEquals($order->id, $transaction->metadata['order_id']);
        $this->assertEquals($order->order_number, $transaction->metadata['order_number']);
        $this->assertEquals('printing', $transaction->metadata['order_type']);
    }
}
