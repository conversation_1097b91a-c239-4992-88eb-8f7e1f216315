<?php

namespace App\Services;

use DOMDocument;
use DOMXPath;
use Illuminate\Support\Str;

class PuckContentTransformationService
{
    private PuckValidationService $validationService;

    public function __construct(PuckValidationService $validationService)
    {
        $this->validationService = $validationService;
    }

    /**
     * Convert HTML content to Puck format
     */
    public function htmlToPuck(string $html, array $metadata = []): array
    {
        if (empty($html)) {
            return $this->createEmptyPuckData($metadata);
        }

        // Try to parse HTML and convert to components
        $components = $this->parseHtmlToComponents($html);

        $puckData = [
            'content' => $components,
            'root' => [
                'title' => $metadata['title'] ?? '',
                'metaDescription' => $metadata['meta_description'] ?? '',
                'metaKeywords' => $metadata['meta_keywords'] ?? '',
            ]
        ];

        // Ensure all components have IDs
        return $this->validationService->ensureComponentIds($puckData);
    }

    /**
     * Convert Puck data to HTML
     */
    public function puckToHtml(array $puckData): string
    {
        if (!$this->validationService->isPuckData($puckData)) {
            return '';
        }

        $html = '';
        
        if (isset($puckData['content']) && is_array($puckData['content'])) {
            foreach ($puckData['content'] as $component) {
                $html .= $this->renderComponent($component);
            }
        }

        // Handle zones if they exist
        if (isset($puckData['zones']) && is_array($puckData['zones'])) {
            foreach ($puckData['zones'] as $zoneName => $zoneComponents) {
                if (is_array($zoneComponents)) {
                    $html .= "<!-- Zone: {$zoneName} -->";
                    foreach ($zoneComponents as $component) {
                        $html .= $this->renderComponent($component);
                    }
                }
            }
        }

        return $html;
    }

    /**
     * Parse HTML content into Puck components
     */
    private function parseHtmlToComponents(string $html): array
    {
        $components = [];
        
        // Clean up HTML
        $html = $this->cleanHtml($html);
        
        if (empty($html)) {
            return $components;
        }

        // Try to parse as structured HTML
        $dom = new DOMDocument();
        libxml_use_internal_errors(true);
        
        // Wrap in a container to ensure valid HTML
        $wrappedHtml = '<!DOCTYPE html><html><body>' . $html . '</body></html>';
        $dom->loadHTML($wrappedHtml, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        
        $xpath = new DOMXPath($dom);
        $body = $xpath->query('//body')->item(0);
        
        if ($body) {
            $components = $this->parseNodeToComponents($body);
        } else {
            // Fallback: create a single text component
            $components[] = [
                'type' => 'Text',
                'props' => [
                    'id' => 'text-' . uniqid(),
                    'content' => $html,
                    'textAlign' => 'left',
                ]
            ];
        }

        return $components;
    }

    /**
     * Parse DOM node to components
     */
    private function parseNodeToComponents($node): array
    {
        $components = [];
        
        foreach ($node->childNodes as $child) {
            if ($child->nodeType === XML_TEXT_NODE) {
                $text = trim($child->textContent);
                if (!empty($text)) {
                    $components[] = [
                        'type' => 'Text',
                        'props' => [
                            'id' => 'text-' . uniqid(),
                            'content' => $text,
                            'textAlign' => 'left',
                        ]
                    ];
                }
            } elseif ($child->nodeType === XML_ELEMENT_NODE) {
                $component = $this->parseElementToComponent($child);
                if ($component) {
                    $components[] = $component;
                }
            }
        }

        return $components;
    }

    /**
     * Parse HTML element to Puck component
     */
    private function parseElementToComponent($element): ?array
    {
        $tagName = strtolower($element->tagName);
        
        switch ($tagName) {
            case 'h1':
            case 'h2':
            case 'h3':
            case 'h4':
            case 'h5':
            case 'h6':
                return [
                    'type' => 'Text',
                    'props' => [
                        'id' => 'heading-' . uniqid(),
                        'content' => "<{$tagName}>" . $element->textContent . "</{$tagName}>",
                        'textAlign' => 'left',
                    ]
                ];
                
            case 'p':
                return [
                    'type' => 'Text',
                    'props' => [
                        'id' => 'paragraph-' . uniqid(),
                        'content' => '<p>' . $element->textContent . '</p>',
                        'textAlign' => 'left',
                    ]
                ];
                
            case 'img':
                return [
                    'type' => 'Image',
                    'props' => [
                        'id' => 'image-' . uniqid(),
                        'src' => $element->getAttribute('src') ?: '',
                        'alt' => $element->getAttribute('alt') ?: '',
                        'width' => $element->getAttribute('width') ?: '',
                        'height' => $element->getAttribute('height') ?: '',
                    ]
                ];
                
            case 'div':
            case 'section':
                // Check if it looks like a hero section
                if ($this->isHeroLikeElement($element)) {
                    return $this->parseHeroElement($element);
                }
                
                // Otherwise, treat as container
                return [
                    'type' => 'Container',
                    'props' => [
                        'id' => 'container-' . uniqid(),
                        'maxWidth' => '1200px',
                        'padding' => '20px',
                    ]
                ];
                
            default:
                // For other elements, create a text component with the full HTML
                $innerHTML = '';
                foreach ($element->childNodes as $child) {
                    $innerHTML .= $element->ownerDocument->saveHTML($child);
                }
                
                return [
                    'type' => 'Text',
                    'props' => [
                        'id' => 'html-' . uniqid(),
                        'content' => "<{$tagName}>" . $innerHTML . "</{$tagName}>",
                        'textAlign' => 'left',
                    ]
                ];
        }
    }

    /**
     * Check if element looks like a hero section
     */
    private function isHeroLikeElement($element): bool
    {
        $class = $element->getAttribute('class');
        $style = $element->getAttribute('style');
        
        // Check for hero-like classes or styles
        return (
            stripos($class, 'hero') !== false ||
            stripos($class, 'banner') !== false ||
            stripos($class, 'jumbotron') !== false ||
            stripos($style, 'background-image') !== false ||
            stripos($style, 'min-height') !== false
        );
    }

    /**
     * Parse hero-like element
     */
    private function parseHeroElement($element): array
    {
        $title = '';
        $subtitle = '';
        
        // Try to find title and subtitle
        $xpath = new DOMXPath($element->ownerDocument);
        $h1 = $xpath->query('.//h1', $element)->item(0);
        $h2 = $xpath->query('.//h2', $element)->item(0);
        $p = $xpath->query('.//p', $element)->item(0);
        
        if ($h1) {
            $title = $h1->textContent;
        }
        
        if ($h2) {
            $subtitle = $h2->textContent;
        } elseif ($p && !$title) {
            $title = $p->textContent;
        } elseif ($p) {
            $subtitle = $p->textContent;
        }

        // Extract background image from style
        $backgroundImage = '';
        $style = $element->getAttribute('style');
        if (preg_match('/background-image:\s*url\(["\']?([^"\']+)["\']?\)/', $style, $matches)) {
            $backgroundImage = $matches[1];
        }

        return [
            'type' => 'Hero',
            'props' => [
                'id' => 'hero-' . uniqid(),
                'title' => $title,
                'subtitle' => $subtitle,
                'backgroundImage' => $backgroundImage,
                'textAlign' => 'center',
                'minHeight' => '400px',
            ]
        ];
    }

    /**
     * Render a Puck component to HTML
     */
    private function renderComponent(array $component): string
    {
        $type = $component['type'] ?? '';
        $props = $component['props'] ?? [];

        switch ($type) {
            case 'Hero':
                return $this->renderHero($props);
            case 'Text':
                return $this->renderText($props);
            case 'Heading':
                return $this->renderHeading($props);
            case 'Button':
                return $this->renderButton($props);
            case 'Image':
                return $this->renderImage($props);
            case 'Container':
                return $this->renderContainer($props);
            case 'Spacer':
                return $this->renderSpacer($props);
            case 'Divider':
                return $this->renderDivider($props);
            case 'Card':
                return $this->renderCard($props);
            case 'Columns':
                return $this->renderColumns($props);
            case 'List':
                return $this->renderList($props);
            case 'FeaturesGrid':
                return $this->renderFeaturesGrid($props);
            default:
                return "<!-- Unknown component: {$type} -->";
        }
    }

    /**
     * Render Hero component
     */
    private function renderHero(array $props): string
    {
        $title = htmlspecialchars($props['title'] ?? '');
        $subtitle = htmlspecialchars($props['subtitle'] ?? '');
        $backgroundImage = htmlspecialchars($props['backgroundImage'] ?? '');
        $textAlign = $props['textAlign'] ?? 'center';
        $minHeight = $props['minHeight'] ?? '400px';

        $styles = [
            'min-height: ' . $minHeight,
            'text-align: ' . $textAlign,
            'display: flex',
            'flex-direction: column',
            'justify-content: center',
            'align-items: center',
            'padding: 2rem',
        ];

        if ($backgroundImage) {
            $styles[] = 'background-image: url(' . $backgroundImage . ')';
            $styles[] = 'background-size: cover';
            $styles[] = 'background-position: center';
        }

        $html = '<section class="hero" style="' . implode('; ', $styles) . '">';
        
        if ($title) {
            $html .= '<h1 style="margin-bottom: 1rem; font-size: 2.5rem;">' . $title . '</h1>';
        }
        
        if ($subtitle) {
            $html .= '<p style="margin-bottom: 2rem; font-size: 1.25rem;">' . $subtitle . '</p>';
        }

        // Add buttons if they exist
        if (isset($props['primaryButton']) && is_array($props['primaryButton'])) {
            $button = $props['primaryButton'];
            $buttonClass = 'btn btn-' . ($button['variant'] ?? 'primary');
            $html .= '<a href="' . htmlspecialchars($button['href'] ?? '#') . '" class="' . $buttonClass . '" style="margin-right: 1rem; padding: 0.75rem 1.5rem; text-decoration: none; border-radius: 0.375rem; display: inline-block;">' . htmlspecialchars($button['text'] ?? 'Button') . '</a>';
        }

        if (isset($props['secondaryButton']) && is_array($props['secondaryButton'])) {
            $button = $props['secondaryButton'];
            $buttonClass = 'btn btn-' . ($button['variant'] ?? 'secondary');
            $html .= '<a href="' . htmlspecialchars($button['href'] ?? '#') . '" class="' . $buttonClass . '" style="padding: 0.75rem 1.5rem; text-decoration: none; border-radius: 0.375rem; display: inline-block;">' . htmlspecialchars($button['text'] ?? 'Button') . '</a>';
        }

        $html .= '</section>';
        
        return $html;
    }

    /**
     * Render Text component
     */
    private function renderText(array $props): string
    {
        $content = $props['content'] ?? '<p>Enter your text content here...</p>';
        $textAlign = $props['textAlign'] ?? 'left';
        $fontSize = $props['fontSize'] ?? '16px';
        $color = $props['color'] ?? '#333333';
        $lineHeight = $props['lineHeight'] ?? '1.6';
        $marginTop = $props['marginTop'] ?? '0px';
        $marginBottom = $props['marginBottom'] ?? '20px';

        $styles = [
            'text-align: ' . $textAlign,
            'font-size: ' . $fontSize,
            'color: ' . $color,
            'line-height: ' . $lineHeight,
            'margin-top: ' . $marginTop,
            'margin-bottom: ' . $marginBottom,
            'padding: 0 1rem',
        ];

        return '<div class="text-component" style="' . implode('; ', $styles) . '">' . $content . '</div>';
    }

    /**
     * Render Heading component
     */
    private function renderHeading(array $props): string
    {
        $text = htmlspecialchars($props['text'] ?? 'Heading Text');
        $level = $props['level'] ?? 'h2';
        $textAlign = $props['textAlign'] ?? 'left';
        $color = $props['color'] ?? '#333333';
        $marginTop = $props['marginTop'] ?? '0px';
        $marginBottom = $props['marginBottom'] ?? '20px';

        $fontSizes = [
            'h1' => '2.5rem',
            'h2' => '2rem',
            'h3' => '1.75rem',
            'h4' => '1.5rem',
            'h5' => '1.25rem',
            'h6' => '1rem',
        ];
        $fontSize = $fontSizes[$level] ?? '2rem';

        $styles = [
            'text-align: ' . $textAlign,
            'color: ' . $color,
            'font-size: ' . $fontSize,
            'font-weight: bold',
            'margin-top: ' . $marginTop,
            'margin-bottom: ' . $marginBottom,
            'padding: 0 1rem',
            'line-height: 1.2',
        ];

        return '<' . $level . ' style="' . implode('; ', $styles) . '">' . $text . '</' . $level . '>';
    }

    /**
     * Render Button component
     */
    private function renderButton(array $props): string
    {
        $text = htmlspecialchars($props['text'] ?? 'Button Text');
        $href = htmlspecialchars($props['href'] ?? '#');
        $variant = $props['variant'] ?? 'primary';
        $size = $props['size'] ?? 'md';
        $textAlign = $props['textAlign'] ?? 'center';
        $fullWidth = $props['fullWidth'] ?? false;
        $target = $props['target'] ?? '_self';

        $colors = [
            'primary' => '#007bff',
            'secondary' => '#6c757d',
            'success' => '#28a745',
            'danger' => '#dc3545',
            'warning' => '#ffc107',
            'info' => '#17a2b8',
            'light' => '#f8f9fa',
            'dark' => '#343a40',
        ];

        $textColors = [
            'primary' => 'white',
            'secondary' => 'white',
            'success' => 'white',
            'danger' => 'white',
            'warning' => '#212529',
            'info' => 'white',
            'light' => '#212529',
            'dark' => 'white',
        ];

        $padding = $size === 'sm' ? '0.5rem 1rem' : ($size === 'lg' ? '1rem 2rem' : '0.75rem 1.5rem');
        $fontSize = $size === 'sm' ? '0.875rem' : ($size === 'lg' ? '1.125rem' : '1rem');

        $buttonStyles = [
            'display: ' . ($fullWidth ? 'block' : 'inline-block'),
            'width: ' . ($fullWidth ? '100%' : 'auto'),
            'padding: ' . $padding,
            'background-color: ' . ($colors[$variant] ?? $colors['primary']),
            'color: ' . ($textColors[$variant] ?? 'white'),
            'text-decoration: none',
            'border-radius: 0.375rem',
            'font-weight: 500',
            'font-size: ' . $fontSize,
            'text-align: center',
            'transition: all 0.2s ease-in-out',
            'border: none',
            'cursor: pointer',
        ];

        $containerStyles = [
            'padding: 1rem',
            'text-align: ' . $textAlign,
        ];

        $targetAttr = $target === '_blank' ? ' target="_blank" rel="noopener noreferrer"' : '';

        return '<div style="' . implode('; ', $containerStyles) . '">' .
               '<a href="' . $href . '"' . $targetAttr . ' style="' . implode('; ', $buttonStyles) . '">' .
               $text . '</a></div>';
    }

    /**
     * Render Container component
     */
    private function renderContainer(array $props): string
    {
        $maxWidth = $props['maxWidth'] ?? 'lg';
        $padding = $props['padding'] ?? '2rem';
        $backgroundColor = $props['backgroundColor'] ?? 'transparent';
        $borderRadius = $props['borderRadius'] ?? '0px';
        $marginTop = $props['marginTop'] ?? '0px';
        $marginBottom = $props['marginBottom'] ?? '0px';

        $maxWidths = [
            'sm' => '540px',
            'md' => '720px',
            'lg' => '960px',
            'xl' => '1140px',
            'fluid' => '100%',
        ];

        $styles = [
            'max-width: ' . ($maxWidths[$maxWidth] ?? '960px'),
            'padding: ' . $padding,
            'margin: 0 auto',
            'background-color: ' . $backgroundColor,
            'border-radius: ' . $borderRadius,
            'margin-top: ' . $marginTop,
            'margin-bottom: ' . $marginBottom,
        ];

        return '<div class="container-component" style="' . implode('; ', $styles) . '"></div>';
    }

    /**
     * Render Spacer component
     */
    private function renderSpacer(array $props): string
    {
        $height = $props['height'] ?? '40px';
        return '<div style="height: ' . $height . '; width: 100%;"></div>';
    }

    /**
     * Render Divider component
     */
    private function renderDivider(array $props): string
    {
        $color = $props['color'] ?? '#e0e0e0';
        $thickness = $props['thickness'] ?? '1px';
        $style = $props['style'] ?? 'solid';
        $marginTop = $props['marginTop'] ?? '20px';
        $marginBottom = $props['marginBottom'] ?? '20px';

        $containerStyles = [
            'padding: 0 1rem',
            'margin-top: ' . $marginTop,
            'margin-bottom: ' . $marginBottom,
        ];

        $hrStyles = [
            'border: none',
            'border-top: ' . $thickness . ' ' . $style . ' ' . $color,
            'margin: 0',
        ];

        return '<div style="' . implode('; ', $containerStyles) . '">' .
               '<hr style="' . implode('; ', $hrStyles) . '" /></div>';
    }

    /**
     * Render Image component
     */
    private function renderImage(array $props): string
    {
        $src = htmlspecialchars($props['src'] ?? 'https://via.placeholder.com/600x400');
        $alt = htmlspecialchars($props['alt'] ?? 'Image description');
        $width = $props['width'] ?? '100%';
        $height = $props['height'] ?? 'auto';
        $objectFit = $props['objectFit'] ?? 'cover';
        $borderRadius = $props['borderRadius'] ?? '0px';
        $marginTop = $props['marginTop'] ?? '0px';
        $marginBottom = $props['marginBottom'] ?? '20px';

        $containerStyles = [
            'padding: 1rem',
            'margin-top: ' . $marginTop,
            'margin-bottom: ' . $marginBottom,
        ];

        $imgStyles = [
            'width: ' . $width,
            'height: ' . $height,
            'object-fit: ' . $objectFit,
            'border-radius: ' . $borderRadius,
            'display: block',
            'max-width: 100%',
        ];

        return '<div style="' . implode('; ', $containerStyles) . '">' .
               '<img src="' . $src . '" alt="' . $alt . '" style="' . implode('; ', $imgStyles) . '" /></div>';
    }

    /**
     * Render Card component
     */
    private function renderCard(array $props): string
    {
        $title = htmlspecialchars($props['title'] ?? 'Card Title');
        $content = $props['content'] ?? '<p>Card content goes here...</p>';
        $imageUrl = htmlspecialchars($props['imageUrl'] ?? '');
        $backgroundColor = $props['backgroundColor'] ?? '#ffffff';
        $borderColor = $props['borderColor'] ?? '#e0e0e0';
        $borderRadius = $props['borderRadius'] ?? '8px';
        $padding = $props['padding'] ?? '1.5rem';
        $shadow = $props['shadow'] ?? true;

        $containerStyles = ['padding: 1rem'];

        $cardStyles = [
            'background-color: ' . $backgroundColor,
            'border: 1px solid ' . $borderColor,
            'border-radius: ' . $borderRadius,
            'padding: ' . $padding,
            'overflow: hidden',
        ];

        if ($shadow) {
            $cardStyles[] = 'box-shadow: 0 2px 4px rgba(0,0,0,0.1)';
        }

        $html = '<div style="' . implode('; ', $containerStyles) . '">';
        $html .= '<div style="' . implode('; ', $cardStyles) . '">';

        if ($imageUrl) {
            $html .= '<img src="' . $imageUrl . '" alt="' . $title . '" style="width: 100%; height: 200px; object-fit: cover; margin-bottom: 1rem; border-radius: 4px;" />';
        }

        if ($title) {
            $html .= '<h3 style="margin: 0 0 1rem 0; font-size: 1.25rem; font-weight: bold; color: #333;">' . $title . '</h3>';
        }

        $html .= $content;
        $html .= '</div></div>';

        return $html;
    }

    /**
     * Render Columns component
     */
    private function renderColumns(array $props): string
    {
        $columns = $props['columns'] ?? 2;
        $gap = $props['gap'] ?? '2rem';
        $alignItems = $props['alignItems'] ?? 'stretch';

        $containerStyles = ['padding: 1rem'];

        $gridStyles = [
            'display: grid',
            'grid-template-columns: repeat(' . $columns . ', 1fr)',
            'gap: ' . $gap,
            'align-items: ' . $alignItems,
        ];

        return '<div style="' . implode('; ', $containerStyles) . '">' .
               '<div style="' . implode('; ', $gridStyles) . '"></div></div>';
    }

    /**
     * Render List component
     */
    private function renderList(array $props): string
    {
        $items = $props['items'] ?? ['List item 1', 'List item 2', 'List item 3'];
        $listType = $props['listType'] ?? 'ul';
        $color = $props['color'] ?? '#333333';
        $fontSize = $props['fontSize'] ?? '16px';

        if (!is_array($items)) {
            $items = ['List item 1', 'List item 2', 'List item 3'];
        }

        $containerStyles = ['padding: 1rem'];

        $listStyles = [
            'color: ' . $color,
            'font-size: ' . $fontSize,
            'line-height: 1.6',
            'padding-left: 1.5rem',
        ];

        $listTag = $listType === 'ol' ? 'ol' : 'ul';

        $html = '<div style="' . implode('; ', $containerStyles) . '">';
        $html .= '<' . $listTag . ' style="' . implode('; ', $listStyles) . '">';

        foreach ($items as $item) {
            $html .= '<li style="margin-bottom: 0.5rem;">' . htmlspecialchars($item) . '</li>';
        }

        $html .= '</' . $listTag . '></div>';

        return $html;
    }

    /**
     * Render FeaturesGrid component
     */
    private function renderFeaturesGrid(array $props): string
    {
        $title = htmlspecialchars($props['title'] ?? '');
        $features = $props['features'] ?? [];
        $columns = $props['columns'] ?? 3;
        $spacing = $props['spacing'] ?? '2rem';

        $html = '<div class="features-grid" style="padding: ' . $spacing . ';">';
        
        if ($title) {
            $html .= '<h2 style="text-align: center; margin-bottom: 2rem;">' . $title . '</h2>';
        }

        if (!empty($features)) {
            $html .= '<div style="display: grid; grid-template-columns: repeat(' . $columns . ', 1fr); gap: ' . $spacing . ';">';
            
            foreach ($features as $feature) {
                $featureTitle = htmlspecialchars($feature['title'] ?? '');
                $description = htmlspecialchars($feature['description'] ?? '');
                $icon = htmlspecialchars($feature['icon'] ?? '');
                
                $html .= '<div class="feature-item" style="text-align: center; padding: 1rem;">';
                
                if ($icon) {
                    $html .= '<div class="feature-icon" style="margin-bottom: 1rem;">' . $icon . '</div>';
                }
                
                if ($featureTitle) {
                    $html .= '<h3 style="margin-bottom: 0.5rem;">' . $featureTitle . '</h3>';
                }
                
                if ($description) {
                    $html .= '<p>' . $description . '</p>';
                }
                
                $html .= '</div>';
            }
            
            $html .= '</div>';
        }

        $html .= '</div>';
        
        return $html;
    }

    /**
     * Clean HTML content
     */
    private function cleanHtml(string $html): string
    {
        // Remove extra whitespace and normalize
        $html = preg_replace('/\s+/', ' ', $html);
        $html = trim($html);
        
        return $html;
    }

    /**
     * Create empty Puck data structure
     */
    private function createEmptyPuckData(array $metadata = []): array
    {
        return [
            'content' => [],
            'root' => [
                'title' => $metadata['title'] ?? '',
                'metaDescription' => $metadata['meta_description'] ?? '',
                'metaKeywords' => $metadata['meta_keywords'] ?? '',
            ]
        ];
    }

    /**
     * Extract SEO data from Puck content
     */
    public function extractSeoData(array $puckData): array
    {
        $seoData = [
            'title' => $puckData['root']['title'] ?? '',
            'meta_description' => $puckData['root']['metaDescription'] ?? '',
            'meta_keywords' => $puckData['root']['metaKeywords'] ?? '',
        ];

        // Extract additional SEO data from components
        if (isset($puckData['content']) && is_array($puckData['content'])) {
            foreach ($puckData['content'] as $component) {
                if ($component['type'] === 'Hero' && isset($component['props']['title'])) {
                    if (empty($seoData['title'])) {
                        $seoData['title'] = $component['props']['title'];
                    }
                }
            }
        }

        return $seoData;
    }
}
