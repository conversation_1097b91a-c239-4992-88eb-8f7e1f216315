<?php

namespace App\Filament\Resources\PrintingProductResource\Pages;

use App\Filament\Resources\PrintingProductResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPrintingProducts extends ListRecords
{
    protected static string $resource = PrintingProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
