<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PrintingOrder;
use App\Models\PrintingProduct;
use App\Models\OrderItem;
use App\Models\OrderFile;
use App\Models\OrderFileHistory;
use App\Models\User;
use App\Services\WalletTransactionService;
use App\Services\FileValidationService;
use App\Services\FileCompressionService;
use App\Services\FileReUploadNotificationService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class OrderController extends Controller
{
    protected FileValidationService $validationService;
    protected FileCompressionService $compressionService;
    protected FileReUploadNotificationService $notificationService;

    public function __construct(
        FileValidationService $validationService,
        FileCompressionService $compressionService,
        FileReUploadNotificationService $notificationService
    ) {
        $this->validationService = $validationService;
        $this->compressionService = $compressionService;
        $this->notificationService = $notificationService;
    }
    /**
     * Get user's orders
     */
    public function index(Request $request)
    {
        $orders = PrintingOrder::with(['items.product', 'files'])
            ->where('user_id', $request->user()->id)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return response()->json([
            'success' => true,
            'data' => $orders->items(),
            'meta' => [
                'current_page' => $orders->currentPage(),
                'last_page' => $orders->lastPage(),
                'per_page' => $orders->perPage(),
                'total' => $orders->total(),
                'from' => $orders->firstItem(),
                'to' => $orders->lastItem(),
            ]
        ]);
    }

    /**
     * Get single order details
     */
    public function show(Request $request, $id)
    {
        $order = PrintingOrder::with(['items.product.category', 'files.uploader'])
            ->where('user_id', $request->user()->id)
            ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $order
        ]);
    }

    /**
     * Create new order
     */
    public function store(Request $request)
    {
        $request->validate([
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:printing_products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.specifications' => 'array',
            'items.*.selected_options' => 'array',
            'items.*.notes' => 'nullable|string',
            'special_instructions' => 'nullable|string',
            'delivery_address' => 'nullable|array',
            'delivery_method' => 'nullable|string',
            'is_temporary' => 'nullable|boolean', // Flag to indicate temporary order for file uploads
        ]);

        try {
            DB::beginTransaction();

            // Calculate total amount
            $totalAmount = 0;
            $orderItems = [];

            foreach ($request->items as $itemData) {
                $product = PrintingProduct::findOrFail($itemData['product_id']);

                // Validate quantity limits
                if ($itemData['quantity'] < $product->min_quantity) {
                    throw new \Exception("Minimum quantity for {$product->name} is {$product->min_quantity}");
                }

                if ($product->max_quantity && $itemData['quantity'] > $product->max_quantity) {
                    throw new \Exception("Maximum quantity for {$product->name} is {$product->max_quantity}");
                }

                $unitPrice = $product->calculatePrice($itemData['quantity'], $itemData['selected_options'] ?? []) / $itemData['quantity'];
                $totalPrice = $unitPrice * $itemData['quantity'];
                $totalAmount += $totalPrice;

                $orderItems[] = [
                    'product' => $product,
                    'quantity' => $itemData['quantity'],
                    'unit_price' => $unitPrice,
                    'total_price' => $totalPrice,
                    'specifications' => $itemData['specifications'] ?? [],
                    'selected_options' => $itemData['selected_options'] ?? [],
                    'notes' => $itemData['notes'] ?? null,
                ];
            }

            $user = $request->user();
            $isTemporary = $request->boolean('is_temporary', false);

            // For non-temporary orders, check wallet balance
            if (!$isTemporary && $user->wallet_balance < $totalAmount) {
                return response()->json([
                    'success' => false,
                    'message' => "Insufficient wallet balance. You have RM " . number_format($user->wallet_balance, 2) . ", but RM " . number_format($totalAmount, 2) . " is required.",
                    'required_amount' => $totalAmount,
                    'current_balance' => $user->wallet_balance,
                    'shortfall' => $totalAmount - $user->wallet_balance,
                ], 400);
            }

            // Create order
            $order = PrintingOrder::create([
                'user_id' => $user->id,
                'order_number' => PrintingOrder::generateOrderNumber(),
                'total_amount' => $totalAmount,
                'status' => PrintingOrder::STATUS_PENDING,
                'payment_status' => PrintingOrder::PAYMENT_PENDING,
                'payment_method' => 'wallet',
                'special_instructions' => $request->special_instructions,
                'delivery_address' => $request->delivery_address,
                'delivery_method' => $request->delivery_method,
                'estimated_completion_date' => now()->addDays(7), // Default 7 days
            ]);

            // Create order items
            foreach ($orderItems as $itemData) {
                OrderItem::create([
                    'printing_order_id' => $order->id,
                    'printing_product_id' => $itemData['product']->id,
                    'quantity' => $itemData['quantity'],
                    'unit_price' => $itemData['unit_price'],
                    'total_price' => $itemData['total_price'],
                    'specifications' => $itemData['specifications'],
                    'selected_options' => $itemData['selected_options'],
                    'notes' => $itemData['notes'],
                ]);
            }

            // For temporary orders, don't process payment immediately
            if ($isTemporary) {
                DB::commit();

                // Load the order with relationships
                $order->load(['items.product', 'files']);

                return response()->json([
                    'success' => true,
                    'message' => 'Temporary order created successfully. Payment will be processed after admin approval.',
                    'data' => $order
                ], 201);
            }

            // For regular orders (non-temporary), process payment immediately (legacy behavior)
            // Create wallet transaction for payment
            $transactionService = new WalletTransactionService();
            $transaction = $transactionService->createTransaction([
                'user_id' => $user->id,
                'type' => 'payment',
                'amount' => -$totalAmount, // Negative amount for payment
                'payment_method' => 'system',
                'payment_status' => 'completed',
                'description' => "Payment for printing order #{$order->order_number}",
                'metadata' => [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'order_type' => 'printing',
                ],
            ]);

            // Update order payment status and reference
            $order->update([
                'payment_status' => PrintingOrder::PAYMENT_PAID,
                'payment_reference' => "WT-{$transaction->id}",
                'status' => PrintingOrder::STATUS_CONFIRMED,
            ]);

            DB::commit();

            // Load the order with relationships
            $order->load(['items.product', 'files']);

            return response()->json([
                'success' => true,
                'message' => 'Order created and payment processed successfully',
                'data' => $order,
                'transaction' => [
                    'id' => $transaction->id,
                    'amount' => $transaction->amount,
                    'formatted_amount' => $transaction->getFormattedAmount(),
                    'description' => $transaction->description,
                ],
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Update order status (admin only)
     */
    public function updateStatus(Request $request, $id)
    {
        // This would typically be restricted to admin users
        $request->validate([
            'status' => ['required', Rule::in(array_keys(PrintingOrder::getStatuses()))],
            'notes' => 'nullable|string',
        ]);

        $order = PrintingOrder::findOrFail($id);

        $order->update([
            'status' => $request->status,
            'notes' => $request->notes,
            'completed_at' => $request->status === PrintingOrder::STATUS_COMPLETED ? now() : null,
            'shipped_at' => $request->status === PrintingOrder::STATUS_SHIPPED ? now() : null,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Order status updated successfully',
            'data' => $order
        ]);
    }

    /**
     * Cancel order
     */
    public function cancel(Request $request, $id)
    {
        $order = PrintingOrder::where('user_id', $request->user()->id)
            ->where('status', PrintingOrder::STATUS_PENDING)
            ->findOrFail($id);

        $order->update([
            'status' => PrintingOrder::STATUS_CANCELLED
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Order cancelled successfully',
            'data' => $order
        ]);
    }

    /**
     * Reorder - create new order based on existing order
     */
    public function reorder(Request $request, $id)
    {
        $originalOrder = PrintingOrder::with('items.product')
            ->where('user_id', $request->user()->id)
            ->findOrFail($id);

        try {
            DB::beginTransaction();

            // Create new order
            $newOrder = PrintingOrder::create([
                'user_id' => $request->user()->id,
                'total_amount' => $originalOrder->total_amount,
                'special_instructions' => $originalOrder->special_instructions,
                'delivery_address' => $originalOrder->delivery_address,
                'delivery_method' => $originalOrder->delivery_method,
                'estimated_completion_date' => now()->addDays(7),
            ]);

            // Copy order items
            foreach ($originalOrder->items as $item) {
                OrderItem::create([
                    'printing_order_id' => $newOrder->id,
                    'printing_product_id' => $item->printing_product_id,
                    'quantity' => $item->quantity,
                    'unit_price' => $item->unit_price,
                    'total_price' => $item->total_price,
                    'specifications' => $item->specifications,
                    'selected_options' => $item->selected_options,
                    'notes' => $item->notes,
                ]);
            }

            DB::commit();

            $newOrder->load(['items.product', 'files']);

            return response()->json([
                'success' => true,
                'message' => 'Order recreated successfully',
                'data' => $newOrder
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to recreate order: ' . $e->getMessage()
            ], 400);
        }
    }

    /**
     * Process payment for an existing order
     */
    public function processPayment(Request $request, $id)
    {
        $order = PrintingOrder::where('user_id', $request->user()->id)
            ->where('payment_status', PrintingOrder::PAYMENT_PENDING)
            ->findOrFail($id);

        $user = $request->user();

        // Check if user has sufficient wallet balance
        if ($user->wallet_balance < $order->total_amount) {
            return response()->json([
                'success' => false,
                'message' => "Insufficient wallet balance. You have RM " . number_format($user->wallet_balance, 2) . ", but RM " . number_format($order->total_amount, 2) . " is required.",
                'required_amount' => $order->total_amount,
                'current_balance' => $user->wallet_balance,
                'shortfall' => $order->total_amount - $user->wallet_balance,
            ], 400);
        }

        try {
            DB::beginTransaction();

            // Create wallet transaction for payment
            $transactionService = new WalletTransactionService();
            $transaction = $transactionService->createTransaction([
                'user_id' => $user->id,
                'type' => 'payment',
                'amount' => -$order->total_amount, // Negative amount for payment
                'payment_method' => 'system',
                'payment_status' => 'completed',
                'description' => "Payment for printing order #{$order->order_number}",
                'metadata' => [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'order_type' => 'printing',
                ],
            ]);

            // Update order payment status and reference
            $order->update([
                'payment_status' => PrintingOrder::PAYMENT_PAID,
                'payment_reference' => "WT-{$transaction->id}",
                'status' => PrintingOrder::STATUS_CONFIRMED,
            ]);

            DB::commit();

            $order->load(['items.product', 'files']);

            return response()->json([
                'success' => true,
                'message' => 'Payment processed successfully',
                'data' => $order,
                'transaction' => [
                    'id' => $transaction->id,
                    'amount' => $transaction->amount,
                    'formatted_amount' => $transaction->getFormattedAmount(),
                    'description' => $transaction->description,
                ],
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Payment processing failed: ' . $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get order payment status and balance check
     */
    public function getPaymentInfo(Request $request, $id)
    {
        $order = PrintingOrder::where('user_id', $request->user()->id)
            ->findOrFail($id);

        $user = $request->user();

        return response()->json([
            'success' => true,
            'data' => [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'total_amount' => $order->total_amount,
                'formatted_total_amount' => $order->formatted_total_amount,
                'payment_status' => $order->payment_status,
                'payment_status_label' => $order->payment_status_label,
                'payment_method' => $order->payment_method,
                'payment_reference' => $order->payment_reference,
                'user_balance' => $user->wallet_balance,
                'formatted_user_balance' => $user->getFormattedWalletBalance(),
                'can_pay' => $order->payment_status === PrintingOrder::PAYMENT_PENDING && $user->wallet_balance >= $order->total_amount,
                'balance_sufficient' => $user->wallet_balance >= $order->total_amount,
                'shortfall' => max(0, $order->total_amount - $user->wallet_balance),
            ]
        ]);
    }

    /**
     * Re-upload a file for an existing order
     */
    public function reUploadFile(Request $request, $orderId, $fileId)
    {
        $request->validate([
            'file' => 'required|file',
            'reason' => 'nullable|string|max:500',
        ]);

        try {
            DB::beginTransaction();

            $user = $request->user();

            // Get the order and verify ownership
            $order = PrintingOrder::where('user_id', $user->id)
                ->findOrFail($orderId);

            // Get the original file
            $originalFile = OrderFile::where('printing_order_id', $order->id)
                ->where('id', $fileId)
                ->where('is_current_version', true)
                ->firstOrFail();

            // Validate permissions
            $permissionValidation = $this->validationService->validateReUploadPermissions($user, $order, $originalFile);
            if (!$permissionValidation['valid']) {
                return response()->json([
                    'success' => false,
                    'message' => $permissionValidation['message']
                ], 403, [], JSON_UNESCAPED_UNICODE);
            }

            // Validate order status
            $statusValidation = $this->validationService->validateOrderStatusForReUpload($order);
            if (!$statusValidation['valid']) {
                return response()->json([
                    'success' => false,
                    'message' => $statusValidation['message']
                ], 400, [], JSON_UNESCAPED_UNICODE);
            }

            // Validate re-upload reason
            $reasonValidation = $this->validationService->validateReUploadReason($request->reason);
            if (!$reasonValidation['valid']) {
                return response()->json([
                    'success' => false,
                    'message' => $reasonValidation['message']
                ], 400, [], JSON_UNESCAPED_UNICODE);
            }

            $file = $request->file('file');

            // Validate the new file
            $fileValidation = $this->validationService->validateFileForReUpload($file, $originalFile);
            if (!$fileValidation['valid']) {
                return response()->json([
                    'success' => false,
                    'message' => $fileValidation['message']
                ], 400, [], JSON_UNESCAPED_UNICODE);
            }

            // Generate unique filename with proper extension handling
            $extension = strtolower($file->getClientOriginalExtension());
            $fileName = Str::uuid() . '.' . $extension;
            $filePath = "order-files/{$orderId}/" . $fileName;

            // Store the new file
            $storedPath = $file->storeAs('order-files/' . $orderId, $fileName, 'public');

            // Compress file if needed
            $compressionResult = $this->compressionService->compressIfNeeded($file, $storedPath);
            $finalPath = $compressionResult['path'];

            // Get file dimensions and DPI for images
            $dimensions = null;
            $dpi = null;

            if (in_array($file->getMimeType(), ['image/jpeg', 'image/png'])) {
                $imageInfo = $this->getImageInfo($file);
                $dimensions = $imageInfo['dimensions'];
                $dpi = $imageInfo['dpi'];
            }

            // Mark the original file as replaced
            $originalFile->markAsReplaced();

            // Create new file record with proper UTF-8 handling
            $originalName = $file->getClientOriginalName();

            // Ensure proper UTF-8 encoding for Chinese characters
            if (!mb_check_encoding($originalName, 'UTF-8')) {
                $originalName = mb_convert_encoding($originalName, 'UTF-8', 'auto');
            }

            $newFile = OrderFile::create([
                'printing_order_id' => $order->id,
                'original_name' => $originalName,
                'file_name' => $fileName,
                'file_path' => $finalPath,
                'file_size' => $compressionResult['compressed_size'],
                'mime_type' => $file->getMimeType(),
                'file_type' => $originalFile->file_type, // Maintain the same file type
                'dimensions' => $dimensions,
                'dpi' => $dpi,
                'uploaded_by' => $user->id,
                'previous_file_id' => $originalFile->id,
                're_upload_reason' => $request->reason,
                're_uploaded_at' => now(),
                'version' => $originalFile->version + 1,
                'is_current_version' => true,
            ]);

            // Create history record
            OrderFileHistory::createReUploadHistory(
                $newFile,
                $originalFile,
                $user,
                $request->reason,
                [
                    'compression_stats' => $compressionResult['compressed'] ?
                        $this->compressionService->getCompressionStats(
                            $compressionResult['original_size'],
                            $compressionResult['compressed_size']
                        ) : null,
                    'dimensions' => $dimensions,
                    'dpi' => $dpi,
                ]
            );

            // Update order status if necessary
            $this->updateOrderStatusAfterReUpload($order, $user);

            DB::commit();

            // Send notifications
            $this->notificationService->sendFileReUploadNotifications(
                $newFile,
                $originalFile,
                $user,
                $request->reason
            );

            // Load relationships for response
            $newFile->load('uploader:id,name');

            return response()->json([
                'success' => true,
                'message' => 'File re-uploaded successfully',
                'data' => $newFile
            ], 200, [], JSON_UNESCAPED_UNICODE);

        } catch (\Exception $e) {
            DB::rollBack();

            // Log the error for debugging
            \Log::error('File re-upload error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'order_id' => $orderId ?? null,
                'file_id' => $fileId ?? null,
                'user_id' => $user->id ?? null,
                'original_filename' => isset($file) ? $this->sanitizeFilename($file->getClientOriginalName()) : null,
            ]);

            // Send failure notification
            if (isset($order) && isset($file)) {
                $this->notificationService->sendFileReUploadFailureNotification(
                    $user,
                    $order,
                    $this->sanitizeFilename($file->getClientOriginalName()),
                    $this->getUserFriendlyErrorMessage($e->getMessage())
                );
            }

            return response()->json([
                'success' => false,
                'message' => $this->getUserFriendlyErrorMessage($e->getMessage())
            ], 500, [], JSON_UNESCAPED_UNICODE);
        }
    }

    /**
     * Get image information (dimensions and DPI)
     */
    private function getImageInfo($file): array
    {
        $dimensions = null;
        $dpi = null;

        try {
            // Get image dimensions
            $imageSize = getimagesize($file->getPathname());
            if ($imageSize) {
                $dimensions = [
                    'width' => $imageSize[0],
                    'height' => $imageSize[1]
                ];

                // Try to get DPI from EXIF data
                if (function_exists('exif_read_data') && in_array($file->getMimeType(), ['image/jpeg', 'image/tiff'])) {
                    $exif = @exif_read_data($file->getPathname());
                    if ($exif && isset($exif['XResolution']) && isset($exif['YResolution'])) {
                        // Convert fraction to decimal if needed
                        $xRes = $exif['XResolution'];
                        $yRes = $exif['YResolution'];

                        if (is_string($xRes) && strpos($xRes, '/') !== false) {
                            $parts = explode('/', $xRes);
                            $xRes = $parts[0] / $parts[1];
                        }

                        if (is_string($yRes) && strpos($yRes, '/') !== false) {
                            $parts = explode('/', $yRes);
                            $yRes = $parts[0] / $parts[1];
                        }

                        $dpi = round(($xRes + $yRes) / 2);
                    }
                }
            }
        } catch (\Exception $e) {
            // If we can't get image info, just continue without it
        }

        return [
            'dimensions' => $dimensions,
            'dpi' => $dpi
        ];
    }

    /**
     * Update order status after file re-upload
     */
    private function updateOrderStatusAfterReUpload(PrintingOrder $order, User $user): void
    {
        $currentStatus = $order->status;
        $newStatus = null;
        $notes = null;

        // Define status update rules based on current status
        switch ($currentStatus) {
            case PrintingOrder::STATUS_COMPLETED:
                // If order was completed, revert to quality check for review
                $newStatus = PrintingOrder::STATUS_QUALITY_CHECK;
                $notes = "Order status reverted to quality check due to file re-upload by " .
                        ($user->isAdmin() ? "admin ({$user->name})" : "customer");
                break;

            case PrintingOrder::STATUS_SHIPPED:
                // If order was shipped, this might need admin attention
                $newStatus = PrintingOrder::STATUS_QUALITY_CHECK;
                $notes = "ATTENTION: File re-uploaded after shipping. Order status reverted to quality check. " .
                        "Re-uploaded by " . ($user->isAdmin() ? "admin ({$user->name})" : "customer");
                break;

            case PrintingOrder::STATUS_DELIVERED:
                // If order was delivered, this definitely needs admin attention
                $newStatus = PrintingOrder::STATUS_QUALITY_CHECK;
                $notes = "URGENT: File re-uploaded after delivery. Order status reverted to quality check. " .
                        "Re-uploaded by " . ($user->isAdmin() ? "admin ({$user->name})" : "customer");
                break;

            case PrintingOrder::STATUS_IN_PRODUCTION:
                // If in production, move back to confirmed for review
                $newStatus = PrintingOrder::STATUS_CONFIRMED;
                $notes = "Order moved back to confirmed status due to file re-upload during production. " .
                        "Re-uploaded by " . ($user->isAdmin() ? "admin ({$user->name})" : "customer");
                break;

            case PrintingOrder::STATUS_QUALITY_CHECK:
                // If in quality check, keep the same status but add note
                $notes = "File re-uploaded during quality check phase. " .
                        "Re-uploaded by " . ($user->isAdmin() ? "admin ({$user->name})" : "customer");
                break;

            case PrintingOrder::STATUS_CONFIRMED:
            case PrintingOrder::STATUS_PENDING:
                // For pending/confirmed orders, just add a note
                $notes = "File re-uploaded. " .
                        "Re-uploaded by " . ($user->isAdmin() ? "admin ({$user->name})" : "customer");
                break;

            default:
                // For any other status, just add a note
                $notes = "File re-uploaded. " .
                        "Re-uploaded by " . ($user->isAdmin() ? "admin ({$user->name})" : "customer");
                break;
        }

        // Update the order
        $updateData = [];

        if ($newStatus && $newStatus !== $currentStatus) {
            $updateData['status'] = $newStatus;

            // Clear completion/shipping timestamps if reverting
            if (in_array($currentStatus, [PrintingOrder::STATUS_COMPLETED, PrintingOrder::STATUS_SHIPPED, PrintingOrder::STATUS_DELIVERED])) {
                $updateData['completed_at'] = null;
                $updateData['shipped_at'] = null;
            }
        }

        if ($notes) {
            // Append to existing notes or create new
            $existingNotes = $order->notes;
            $timestamp = now()->format('Y-m-d H:i:s');
            $newNote = "[{$timestamp}] {$notes}";

            $updateData['notes'] = $existingNotes ?
                $existingNotes . "\n\n" . $newNote :
                $newNote;
        }

        if (!empty($updateData)) {
            $order->update($updateData);
        }
    }

    /**
     * Sanitize filename for safe handling of Chinese characters
     */
    private function sanitizeFilename(string $filename): string
    {
        // Ensure proper UTF-8 encoding
        if (!mb_check_encoding($filename, 'UTF-8')) {
            $filename = mb_convert_encoding($filename, 'UTF-8', 'auto');
        }

        // Remove any null bytes or control characters that might cause issues
        $filename = preg_replace('/[\x00-\x1F\x7F]/', '', $filename);

        return $filename;
    }

    /**
     * Convert technical error messages to user-friendly ones
     */
    private function getUserFriendlyErrorMessage(string $errorMessage): string
    {
        // Handle common PHP/Laravel errors
        if (strpos($errorMessage, 'Undefined array key') !== false) {
            return 'There was a problem processing your file. Please check that your file meets all requirements and try again. If the problem persists, contact support.';
        }

        if (strpos($errorMessage, 'file_get_contents') !== false || strpos($errorMessage, 'fopen') !== false) {
            return 'Unable to read your file. The file may be corrupted or in an unsupported format. Please try saving your file again and re-uploading.';
        }

        if (strpos($errorMessage, 'getimagesize') !== false) {
            return 'Unable to process your image file. Please make sure it\'s a valid image format (JPG, PNG, etc.) and not corrupted.';
        }

        if (strpos($errorMessage, 'storage') !== false || strpos($errorMessage, 'disk') !== false) {
            return 'Server storage error. Please try again in a few moments. If the problem continues, contact support.';
        }

        if (strpos($errorMessage, 'database') !== false || strpos($errorMessage, 'SQL') !== false) {
            return 'Database error occurred. Please try again. If the problem persists, contact support with your order details.';
        }

        // Handle encoding issues
        if (strpos($errorMessage, 'encoding') !== false || strpos($errorMessage, 'charset') !== false) {
            return 'File name encoding issue. If your file name contains special characters, try renaming it with English characters only and upload again.';
        }

        // If it's already a user-friendly message from validation, return as-is
        if (strpos($errorMessage, 'Please') !== false || strpos($errorMessage, 'please') !== false) {
            return $errorMessage;
        }

        // Default fallback for unknown errors
        return 'An unexpected error occurred while processing your file. Please check that your file meets all requirements (correct format, size under limit, good quality) and try again. If the problem continues, contact support.';
    }
}
