<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('wallet_transactions', function (Blueprint $table) {
            // Drop the foreign key constraint to credit_packages table
            // since we're removing credit packages functionality
            $table->dropForeign(['package_id']);

            // Keep the package_id column for data integrity but make it nullable
            // and remove the constraint
            $table->unsignedBigInteger('package_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('wallet_transactions', function (Blueprint $table) {
            // Restore the foreign key constraint
            // Note: This assumes credit_packages table exists
            $table->foreign('package_id')->references('id')->on('credit_packages')->onDelete('set null');
        });
    }
};
