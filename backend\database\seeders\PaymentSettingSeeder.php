<?php

namespace Database\Seeders;

use App\Models\PaymentSetting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PaymentSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            [
                'key' => 'billplz_enabled',
                'value' => 'false',
                'type' => 'boolean',
                'description' => 'Enable or disable Billplz payment gateway',
                'is_encrypted' => false,
            ],
            [
                'key' => 'billplz_sandbox_mode',
                'value' => 'true',
                'type' => 'boolean',
                'description' => 'Enable sandbox mode for testing',
                'is_encrypted' => false,
            ],
            [
                'key' => 'billplz_api_key',
                'value' => '',
                'type' => 'string',
                'description' => 'Billplz API key',
                'is_encrypted' => true,
            ],
            [
                'key' => 'billplz_collection_id',
                'value' => '',
                'type' => 'string',
                'description' => 'Billplz collection ID',
                'is_encrypted' => false,
            ],
            [
                'key' => 'billplz_x_signature_key',
                'value' => '',
                'type' => 'string',
                'description' => 'Billplz X Signature key for webhook verification',
                'is_encrypted' => true,
            ],
        ];

        foreach ($settings as $setting) {
            PaymentSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
