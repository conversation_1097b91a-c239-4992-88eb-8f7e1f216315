<?php

namespace App\Http\Requests\File;

use App\Http\Requests\BaseRequest;
use App\Models\FileUploadSetting;

class FileUploadRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        // Get dynamic validation rules from settings
        $maxFiles = FileUploadSetting::get('max_files_per_order', 10);
        $maxFileSizeMB = FileUploadSetting::get('max_file_size_mb', 50);
        $allowedTypes = FileUploadSetting::getTyped('allowed_file_types', ['pdf', 'png', 'jpg', 'jpeg']);

        // Ensure allowed_file_types is always an array
        if (!is_array($allowedTypes)) {
            if (is_string($allowedTypes)) {
                $allowedTypes = json_decode($allowedTypes, true) ?: ['pdf', 'png', 'jpg', 'jpeg'];
            } else {
                $allowedTypes = ['pdf', 'png', 'jpg', 'jpeg'];
            }
        }

        $allowedMimes = $this->getMimeTypesForExtensions($allowedTypes);

        return [
            'files' => "required|array|min:1|max:{$maxFiles}",
            'files.*' => [
                'required',
                'file',
                "max:" . ($maxFileSizeMB * 1024), // Convert MB to KB
                'mimes:' . implode(',', $allowedTypes),
                'mimetypes:' . implode(',', $allowedMimes),
            ],
            'file_type' => 'required|in:artwork,reference,proof',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        $maxFiles = FileUploadSetting::get('max_files_per_order', 10);
        $maxFileSizeMB = FileUploadSetting::get('max_file_size_mb', 50);
        $allowedTypes = FileUploadSetting::getTyped('allowed_file_types', ['pdf', 'png', 'jpg', 'jpeg']);

        return array_merge(parent::messages(), [
            'files.required' => 'Please select at least one file to upload.',
            'files.max' => "You can upload a maximum of {$maxFiles} files per order.",
            'files.*.required' => 'Each file is required.',
            'files.*.file' => 'Each upload must be a valid file.',
            'files.*.max' => "Each file must be smaller than {$maxFileSizeMB}MB.",
            'files.*.mimes' => 'Files must be of type: ' . implode(', ', (array)$allowedTypes) . '.',
            'files.*.mimetypes' => 'Invalid file type detected. Please upload only allowed file types.',
            'file_type.required' => 'Please specify the file type.',
            'file_type.in' => 'File type must be artwork, reference, or proof.',
        ]);
    }

    /**
     * Get MIME types for file extensions
     */
    private function getMimeTypesForExtensions(array $extensions): array
    {
        $mimeMap = [
            'pdf' => 'application/pdf',
            'png' => 'image/png',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'bmp' => 'image/bmp',
            'webp' => 'image/webp',
            'svg' => 'image/svg+xml',
            'tiff' => 'image/tiff',
            'tif' => 'image/tiff',
        ];

        $mimeTypes = [];
        foreach ($extensions as $ext) {
            if (isset($mimeMap[strtolower($ext)])) {
                $mimeTypes[] = $mimeMap[strtolower($ext)];
            }
        }

        return array_unique($mimeTypes);
    }

    /**
     * Fields that are allowed to contain HTML (none for file uploads)
     */
    protected function getAllowedHtmlFields(): array
    {
        return [];
    }

    /**
     * Additional security validation
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Additional file security checks
            if ($this->hasFile('files')) {
                foreach ($this->file('files') as $index => $file) {
                    // Check for suspicious file names
                    $filename = $file->getClientOriginalName();
                    if ($this->isSuspiciousFilename($filename)) {
                        $validator->errors()->add("files.{$index}", 'Suspicious filename detected.');
                    }

                    // Check file size again (double check)
                    $maxSizeBytes = FileUploadSetting::get('max_file_size_mb', 50) * 1024 * 1024;
                    if ($file->getSize() > $maxSizeBytes) {
                        $validator->errors()->add("files.{$index}", 'File size exceeds maximum allowed size.');
                    }

                    // Basic file content validation
                    if (!$this->isValidFileContent($file)) {
                        $validator->errors()->add("files.{$index}", 'File content appears to be invalid or corrupted.');
                    }
                }
            }
        });
    }

    /**
     * Check for suspicious filenames
     */
    private function isSuspiciousFilename(string $filename): bool
    {
        $suspiciousPatterns = [
            '/\.(php|phtml|php3|php4|php5|phar|exe|bat|cmd|com|scr|vbs|js|jar)$/i',
            '/\.\./i', // Directory traversal
            '/[<>:"|?*]/i', // Invalid characters
            '/^(con|prn|aux|nul|com[1-9]|lpt[1-9])$/i', // Windows reserved names
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $filename)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Basic file content validation
     */
    private function isValidFileContent($file): bool
    {
        // Check if file is readable
        if (!is_readable($file->getPathname())) {
            return false;
        }

        // Check for minimum file size (avoid empty files)
        if ($file->getSize() < 10) {
            return false;
        }

        // Basic MIME type validation
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $detectedMime = finfo_file($finfo, $file->getPathname());
        finfo_close($finfo);

        // Get expected MIME types
        $allowedTypes = FileUploadSetting::getTyped('allowed_file_types', ['pdf', 'png', 'jpg', 'jpeg']);
        $allowedMimes = $this->getMimeTypesForExtensions((array)$allowedTypes);

        return in_array($detectedMime, $allowedMimes);
    }
}
