<?php

namespace App\Services;

use App\Models\FileUploadSetting;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use Intervention\Image\Facades\Image;

class FileCompressionService
{
    /**
     * Compress file if needed based on settings
     */
    public function compressIfNeeded(UploadedFile $file, string $storagePath): array
    {
        $enableCompression = FileUploadSetting::get('enable_auto_compression', false);
        
        if (!$enableCompression) {
            return [
                'compressed' => false,
                'original_size' => $file->getSize(),
                'compressed_size' => $file->getSize(),
                'path' => $storagePath
            ];
        }

        $thresholdMB = FileUploadSetting::get('compression_threshold_mb', 10);
        $thresholdBytes = $thresholdMB * 1024 * 1024;

        if ($file->getSize() <= $thresholdBytes) {
            return [
                'compressed' => false,
                'original_size' => $file->getSize(),
                'compressed_size' => $file->getSize(),
                'path' => $storagePath
            ];
        }

        // Only compress images
        if (!$this->isCompressibleImage($file)) {
            return [
                'compressed' => false,
                'original_size' => $file->getSize(),
                'compressed_size' => $file->getSize(),
                'path' => $storagePath
            ];
        }

        try {
            $compressedPath = $this->compressImage($file, $storagePath);
            $compressedSize = filesize(storage_path('app/public/' . $compressedPath));

            return [
                'compressed' => true,
                'original_size' => $file->getSize(),
                'compressed_size' => $compressedSize,
                'path' => $compressedPath
            ];

        } catch (\Exception $e) {
            Log::warning('Failed to compress image: ' . $e->getMessage());
            
            return [
                'compressed' => false,
                'original_size' => $file->getSize(),
                'compressed_size' => $file->getSize(),
                'path' => $storagePath
            ];
        }
    }

    /**
     * Check if file is a compressible image
     */
    private function isCompressibleImage(UploadedFile $file): bool
    {
        return in_array($file->getMimeType(), [
            'image/jpeg',
            'image/png',
            'image/webp'
        ]);
    }

    /**
     * Compress image using Intervention Image
     */
    private function compressImage(UploadedFile $file, string $originalPath): string
    {
        $quality = FileUploadSetting::get('compression_quality', 85);
        
        // Create compressed filename
        $pathInfo = pathinfo($originalPath);
        $compressedPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_compressed.' . $pathInfo['extension'];
        
        $fullPath = storage_path('app/public/' . $compressedPath);
        
        // Ensure directory exists
        $directory = dirname($fullPath);
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }

        // Load and compress image
        $image = Image::make($file->getPathname());
        
        // Optimize based on file type
        switch ($file->getMimeType()) {
            case 'image/jpeg':
                $image->save($fullPath, $quality);
                break;
                
            case 'image/png':
                // For PNG, we can reduce colors to compress
                $image->limitColors(256)->save($fullPath);
                break;
                
            case 'image/webp':
                $image->save($fullPath, $quality);
                break;
                
            default:
                $image->save($fullPath, $quality);
        }

        return $compressedPath;
    }

    /**
     * Get compression statistics
     */
    public function getCompressionStats(int $originalSize, int $compressedSize): array
    {
        $savedBytes = $originalSize - $compressedSize;
        $compressionRatio = $originalSize > 0 ? ($savedBytes / $originalSize) * 100 : 0;

        return [
            'original_size_formatted' => $this->formatFileSize($originalSize),
            'compressed_size_formatted' => $this->formatFileSize($compressedSize),
            'saved_bytes' => $savedBytes,
            'saved_bytes_formatted' => $this->formatFileSize($savedBytes),
            'compression_ratio' => round($compressionRatio, 2),
        ];
    }

    /**
     * Format file size for display
     */
    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }
}
