{"ast": null, "code": "'use client';\n\nvar _ClearIcon, _ArrowDropDownIcon;\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport integerPropType from '@mui/utils/integerPropType';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport useAutocomplete, { createFilterOptions } from \"../useAutocomplete/index.js\";\nimport Popper from \"../Popper/index.js\";\nimport ListSubheader from \"../ListSubheader/index.js\";\nimport Paper from \"../Paper/index.js\";\nimport IconButton from \"../IconButton/index.js\";\nimport Chip from \"../Chip/index.js\";\nimport inputClasses from \"../Input/inputClasses.js\";\nimport inputBaseClasses from \"../InputBase/inputBaseClasses.js\";\nimport outlinedInputClasses from \"../OutlinedInput/outlinedInputClasses.js\";\nimport filledInputClasses from \"../FilledInput/filledInputClasses.js\";\nimport ClearIcon from \"../internal/svg-icons/Close.js\";\nimport ArrowDropDownIcon from \"../internal/svg-icons/ArrowDropDown.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport autocompleteClasses, { getAutocompleteUtilityClass } from \"./autocompleteClasses.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disablePortal,\n    expanded,\n    focused,\n    fullWidth,\n    hasClearIcon,\n    hasPopupIcon,\n    inputFocused,\n    popupOpen,\n    size\n  } = ownerState;\n  const slots = {\n    root: ['root', expanded && 'expanded', focused && 'focused', fullWidth && 'fullWidth', hasClearIcon && 'hasClearIcon', hasPopupIcon && 'hasPopupIcon'],\n    inputRoot: ['inputRoot'],\n    input: ['input', inputFocused && 'inputFocused'],\n    tag: ['tag', `tagSize${capitalize(size)}`],\n    endAdornment: ['endAdornment'],\n    clearIndicator: ['clearIndicator'],\n    popupIndicator: ['popupIndicator', popupOpen && 'popupIndicatorOpen'],\n    popper: ['popper', disablePortal && 'popperDisablePortal'],\n    paper: ['paper'],\n    listbox: ['listbox'],\n    loading: ['loading'],\n    noOptions: ['noOptions'],\n    option: ['option'],\n    groupLabel: ['groupLabel'],\n    groupUl: ['groupUl']\n  };\n  return composeClasses(slots, getAutocompleteUtilityClass, classes);\n};\nconst AutocompleteRoot = styled('div', {\n  name: 'MuiAutocomplete',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    const {\n      fullWidth,\n      hasClearIcon,\n      hasPopupIcon,\n      inputFocused,\n      size\n    } = ownerState;\n    return [{\n      [`& .${autocompleteClasses.tag}`]: styles.tag\n    }, {\n      [`& .${autocompleteClasses.tag}`]: styles[`tagSize${capitalize(size)}`]\n    }, {\n      [`& .${autocompleteClasses.inputRoot}`]: styles.inputRoot\n    }, {\n      [`& .${autocompleteClasses.input}`]: styles.input\n    }, {\n      [`& .${autocompleteClasses.input}`]: inputFocused && styles.inputFocused\n    }, styles.root, fullWidth && styles.fullWidth, hasPopupIcon && styles.hasPopupIcon, hasClearIcon && styles.hasClearIcon];\n  }\n})({\n  [`&.${autocompleteClasses.focused} .${autocompleteClasses.clearIndicator}`]: {\n    visibility: 'visible'\n  },\n  /* Avoid double tap issue on iOS */\n  '@media (pointer: fine)': {\n    [`&:hover .${autocompleteClasses.clearIndicator}`]: {\n      visibility: 'visible'\n    }\n  },\n  [`& .${autocompleteClasses.tag}`]: {\n    margin: 3,\n    maxWidth: 'calc(100% - 6px)'\n  },\n  [`& .${autocompleteClasses.inputRoot}`]: {\n    [`.${autocompleteClasses.hasPopupIcon}&, .${autocompleteClasses.hasClearIcon}&`]: {\n      paddingRight: 26 + 4\n    },\n    [`.${autocompleteClasses.hasPopupIcon}.${autocompleteClasses.hasClearIcon}&`]: {\n      paddingRight: 52 + 4\n    },\n    [`& .${autocompleteClasses.input}`]: {\n      width: 0,\n      minWidth: 30\n    }\n  },\n  [`& .${inputClasses.root}`]: {\n    paddingBottom: 1,\n    '& .MuiInput-input': {\n      padding: '4px 4px 4px 0px'\n    }\n  },\n  [`& .${inputClasses.root}.${inputBaseClasses.sizeSmall}`]: {\n    [`& .${inputClasses.input}`]: {\n      padding: '2px 4px 3px 0'\n    }\n  },\n  [`& .${outlinedInputClasses.root}`]: {\n    padding: 9,\n    [`.${autocompleteClasses.hasPopupIcon}&, .${autocompleteClasses.hasClearIcon}&`]: {\n      paddingRight: 26 + 4 + 9\n    },\n    [`.${autocompleteClasses.hasPopupIcon}.${autocompleteClasses.hasClearIcon}&`]: {\n      paddingRight: 52 + 4 + 9\n    },\n    [`& .${autocompleteClasses.input}`]: {\n      padding: '7.5px 4px 7.5px 5px'\n    },\n    [`& .${autocompleteClasses.endAdornment}`]: {\n      right: 9\n    }\n  },\n  [`& .${outlinedInputClasses.root}.${inputBaseClasses.sizeSmall}`]: {\n    // Don't specify paddingRight, as it overrides the default value set when there is only\n    // one of the popup or clear icon as the specificity is equal so the latter one wins\n    paddingTop: 6,\n    paddingBottom: 6,\n    paddingLeft: 6,\n    [`& .${autocompleteClasses.input}`]: {\n      padding: '2.5px 4px 2.5px 8px'\n    }\n  },\n  [`& .${filledInputClasses.root}`]: {\n    paddingTop: 19,\n    paddingLeft: 8,\n    [`.${autocompleteClasses.hasPopupIcon}&, .${autocompleteClasses.hasClearIcon}&`]: {\n      paddingRight: 26 + 4 + 9\n    },\n    [`.${autocompleteClasses.hasPopupIcon}.${autocompleteClasses.hasClearIcon}&`]: {\n      paddingRight: 52 + 4 + 9\n    },\n    [`& .${filledInputClasses.input}`]: {\n      padding: '7px 4px'\n    },\n    [`& .${autocompleteClasses.endAdornment}`]: {\n      right: 9\n    }\n  },\n  [`& .${filledInputClasses.root}.${inputBaseClasses.sizeSmall}`]: {\n    paddingBottom: 1,\n    [`& .${filledInputClasses.input}`]: {\n      padding: '2.5px 4px'\n    }\n  },\n  [`& .${inputBaseClasses.hiddenLabel}`]: {\n    paddingTop: 8\n  },\n  [`& .${filledInputClasses.root}.${inputBaseClasses.hiddenLabel}`]: {\n    paddingTop: 0,\n    paddingBottom: 0,\n    [`& .${autocompleteClasses.input}`]: {\n      paddingTop: 16,\n      paddingBottom: 17\n    }\n  },\n  [`& .${filledInputClasses.root}.${inputBaseClasses.hiddenLabel}.${inputBaseClasses.sizeSmall}`]: {\n    [`& .${autocompleteClasses.input}`]: {\n      paddingTop: 8,\n      paddingBottom: 9\n    }\n  },\n  [`& .${autocompleteClasses.input}`]: {\n    flexGrow: 1,\n    textOverflow: 'ellipsis',\n    opacity: 0\n  },\n  variants: [{\n    props: {\n      fullWidth: true\n    },\n    style: {\n      width: '100%'\n    }\n  }, {\n    props: {\n      size: 'small'\n    },\n    style: {\n      [`& .${autocompleteClasses.tag}`]: {\n        margin: 2,\n        maxWidth: 'calc(100% - 4px)'\n      }\n    }\n  }, {\n    props: {\n      inputFocused: true\n    },\n    style: {\n      [`& .${autocompleteClasses.input}`]: {\n        opacity: 1\n      }\n    }\n  }, {\n    props: {\n      multiple: true\n    },\n    style: {\n      [`& .${autocompleteClasses.inputRoot}`]: {\n        flexWrap: 'wrap'\n      }\n    }\n  }]\n});\nconst AutocompleteEndAdornment = styled('div', {\n  name: 'MuiAutocomplete',\n  slot: 'EndAdornment'\n})({\n  // We use a position absolute to support wrapping tags.\n  position: 'absolute',\n  right: 0,\n  top: '50%',\n  transform: 'translate(0, -50%)'\n});\nconst AutocompleteClearIndicator = styled(IconButton, {\n  name: 'MuiAutocomplete',\n  slot: 'ClearIndicator'\n})({\n  marginRight: -2,\n  padding: 4,\n  visibility: 'hidden'\n});\nconst AutocompletePopupIndicator = styled(IconButton, {\n  name: 'MuiAutocomplete',\n  slot: 'PopupIndicator',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.popupIndicator, ownerState.popupOpen && styles.popupIndicatorOpen];\n  }\n})({\n  padding: 2,\n  marginRight: -2,\n  variants: [{\n    props: {\n      popupOpen: true\n    },\n    style: {\n      transform: 'rotate(180deg)'\n    }\n  }]\n});\nconst AutocompletePopper = styled(Popper, {\n  name: 'MuiAutocomplete',\n  slot: 'Popper',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${autocompleteClasses.option}`]: styles.option\n    }, styles.popper, ownerState.disablePortal && styles.popperDisablePortal];\n  }\n})(memoTheme(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    zIndex: (theme.vars || theme).zIndex.modal,\n    variants: [{\n      props: {\n        disablePortal: true\n      },\n      style: {\n        position: 'absolute'\n      }\n    }]\n  };\n}));\nconst AutocompletePaper = styled(Paper, {\n  name: 'MuiAutocomplete',\n  slot: 'Paper'\n})(memoTheme(_ref2 => {\n  let {\n    theme\n  } = _ref2;\n  return {\n    ...theme.typography.body1,\n    overflow: 'auto'\n  };\n}));\nconst AutocompleteLoading = styled('div', {\n  name: 'MuiAutocomplete',\n  slot: 'Loading'\n})(memoTheme(_ref3 => {\n  let {\n    theme\n  } = _ref3;\n  return {\n    color: (theme.vars || theme).palette.text.secondary,\n    padding: '14px 16px'\n  };\n}));\nconst AutocompleteNoOptions = styled('div', {\n  name: 'MuiAutocomplete',\n  slot: 'NoOptions'\n})(memoTheme(_ref4 => {\n  let {\n    theme\n  } = _ref4;\n  return {\n    color: (theme.vars || theme).palette.text.secondary,\n    padding: '14px 16px'\n  };\n}));\nconst AutocompleteListbox = styled('ul', {\n  name: 'MuiAutocomplete',\n  slot: 'Listbox'\n})(memoTheme(_ref5 => {\n  let {\n    theme\n  } = _ref5;\n  return {\n    listStyle: 'none',\n    margin: 0,\n    padding: '8px 0',\n    maxHeight: '40vh',\n    overflow: 'auto',\n    position: 'relative',\n    [`& .${autocompleteClasses.option}`]: {\n      minHeight: 48,\n      display: 'flex',\n      overflow: 'hidden',\n      justifyContent: 'flex-start',\n      alignItems: 'center',\n      cursor: 'pointer',\n      paddingTop: 6,\n      boxSizing: 'border-box',\n      outline: '0',\n      WebkitTapHighlightColor: 'transparent',\n      paddingBottom: 6,\n      paddingLeft: 16,\n      paddingRight: 16,\n      [theme.breakpoints.up('sm')]: {\n        minHeight: 'auto'\n      },\n      [`&.${autocompleteClasses.focused}`]: {\n        backgroundColor: (theme.vars || theme).palette.action.hover,\n        // Reset on touch devices, it doesn't add specificity\n        '@media (hover: none)': {\n          backgroundColor: 'transparent'\n        }\n      },\n      '&[aria-disabled=\"true\"]': {\n        opacity: (theme.vars || theme).palette.action.disabledOpacity,\n        pointerEvents: 'none'\n      },\n      [`&.${autocompleteClasses.focusVisible}`]: {\n        backgroundColor: (theme.vars || theme).palette.action.focus\n      },\n      '&[aria-selected=\"true\"]': {\n        backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / ${theme.vars.palette.action.selectedOpacity})` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity),\n        [`&.${autocompleteClasses.focused}`]: {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.hoverOpacity}))` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity + theme.palette.action.hoverOpacity),\n          // Reset on touch devices, it doesn't add specificity\n          '@media (hover: none)': {\n            backgroundColor: (theme.vars || theme).palette.action.selected\n          }\n        },\n        [`&.${autocompleteClasses.focusVisible}`]: {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.focusOpacity}))` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity + theme.palette.action.focusOpacity)\n        }\n      }\n    }\n  };\n}));\nconst AutocompleteGroupLabel = styled(ListSubheader, {\n  name: 'MuiAutocomplete',\n  slot: 'GroupLabel'\n})(memoTheme(_ref6 => {\n  let {\n    theme\n  } = _ref6;\n  return {\n    backgroundColor: (theme.vars || theme).palette.background.paper,\n    top: -8\n  };\n}));\nconst AutocompleteGroupUl = styled('ul', {\n  name: 'MuiAutocomplete',\n  slot: 'GroupUl'\n})({\n  padding: 0,\n  [`& .${autocompleteClasses.option}`]: {\n    paddingLeft: 24\n  }\n});\nexport { createFilterOptions };\nconst Autocomplete = /*#__PURE__*/React.forwardRef(function Autocomplete(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiAutocomplete'\n  });\n\n  /* eslint-disable @typescript-eslint/no-unused-vars */\n  const {\n    autoComplete = false,\n    autoHighlight = false,\n    autoSelect = false,\n    blurOnSelect = false,\n    ChipProps: ChipPropsProp,\n    className,\n    clearIcon = _ClearIcon || (_ClearIcon = /*#__PURE__*/_jsx(ClearIcon, {\n      fontSize: \"small\"\n    })),\n    clearOnBlur = !props.freeSolo,\n    clearOnEscape = false,\n    clearText = 'Clear',\n    closeText = 'Close',\n    componentsProps,\n    defaultValue = props.multiple ? [] : null,\n    disableClearable = false,\n    disableCloseOnSelect = false,\n    disabled = false,\n    disabledItemsFocusable = false,\n    disableListWrap = false,\n    disablePortal = false,\n    filterOptions,\n    filterSelectedOptions = false,\n    forcePopupIcon = 'auto',\n    freeSolo = false,\n    fullWidth = false,\n    getLimitTagsText = more => `+${more}`,\n    getOptionDisabled,\n    getOptionKey,\n    getOptionLabel: getOptionLabelProp,\n    isOptionEqualToValue,\n    groupBy,\n    handleHomeEndKeys = !props.freeSolo,\n    id: idProp,\n    includeInputInList = false,\n    inputValue: inputValueProp,\n    limitTags = -1,\n    ListboxComponent: ListboxComponentProp,\n    ListboxProps: ListboxPropsProp,\n    loading = false,\n    loadingText = 'Loading…',\n    multiple = false,\n    noOptionsText = 'No options',\n    onChange,\n    onClose,\n    onHighlightChange,\n    onInputChange,\n    onOpen,\n    open,\n    openOnFocus = false,\n    openText = 'Open',\n    options,\n    PaperComponent: PaperComponentProp,\n    PopperComponent: PopperComponentProp,\n    popupIcon = _ArrowDropDownIcon || (_ArrowDropDownIcon = /*#__PURE__*/_jsx(ArrowDropDownIcon, {})),\n    readOnly = false,\n    renderGroup: renderGroupProp,\n    renderInput,\n    renderOption: renderOptionProp,\n    renderTags,\n    renderValue,\n    selectOnFocus = !props.freeSolo,\n    size = 'medium',\n    slots = {},\n    slotProps = {},\n    value: valueProp,\n    ...other\n  } = props;\n  /* eslint-enable @typescript-eslint/no-unused-vars */\n\n  const {\n    getRootProps,\n    getInputProps,\n    getInputLabelProps,\n    getPopupIndicatorProps,\n    getClearProps,\n    getItemProps,\n    getListboxProps,\n    getOptionProps,\n    value,\n    dirty,\n    expanded,\n    id,\n    popupOpen,\n    focused,\n    focusedItem,\n    anchorEl,\n    setAnchorEl,\n    inputValue,\n    groupedOptions\n  } = useAutocomplete({\n    ...props,\n    componentName: 'Autocomplete'\n  });\n  const hasClearIcon = !disableClearable && !disabled && dirty && !readOnly;\n  const hasPopupIcon = (!freeSolo || forcePopupIcon === true) && forcePopupIcon !== false;\n  const {\n    onMouseDown: handleInputMouseDown\n  } = getInputProps();\n  const {\n    ref: listboxRef,\n    ...otherListboxProps\n  } = getListboxProps();\n  const defaultGetOptionLabel = option => option.label ?? option;\n  const getOptionLabel = getOptionLabelProp || defaultGetOptionLabel;\n\n  // If you modify this, make sure to keep the `AutocompleteOwnerState` type in sync.\n  const ownerState = {\n    ...props,\n    disablePortal,\n    expanded,\n    focused,\n    fullWidth,\n    getOptionLabel,\n    hasClearIcon,\n    hasPopupIcon,\n    inputFocused: focusedItem === -1,\n    popupOpen,\n    size\n  };\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots: {\n      paper: PaperComponentProp,\n      popper: PopperComponentProp,\n      ...slots\n    },\n    slotProps: {\n      chip: ChipPropsProp,\n      listbox: ListboxPropsProp,\n      ...componentsProps,\n      ...slotProps\n    }\n  };\n  const [ListboxSlot, listboxProps] = useSlot('listbox', {\n    elementType: AutocompleteListbox,\n    externalForwardedProps,\n    ownerState,\n    className: classes.listbox,\n    additionalProps: otherListboxProps,\n    ref: listboxRef\n  });\n  const [PaperSlot, paperProps] = useSlot('paper', {\n    elementType: Paper,\n    externalForwardedProps,\n    ownerState,\n    className: classes.paper\n  });\n  const [PopperSlot, popperProps] = useSlot('popper', {\n    elementType: Popper,\n    externalForwardedProps,\n    ownerState,\n    className: classes.popper,\n    additionalProps: {\n      disablePortal,\n      style: {\n        width: anchorEl ? anchorEl.clientWidth : null\n      },\n      role: 'presentation',\n      anchorEl,\n      open: popupOpen\n    }\n  });\n  let startAdornment;\n  const getCustomizedItemProps = params => ({\n    className: classes.tag,\n    disabled,\n    ...getItemProps(params)\n  });\n  if (multiple) {\n    if (value.length > 0) {\n      if (renderTags) {\n        startAdornment = renderTags(value, getCustomizedItemProps, ownerState);\n      } else if (renderValue) {\n        startAdornment = renderValue(value, getCustomizedItemProps, ownerState);\n      } else {\n        startAdornment = value.map((option, index) => {\n          const {\n            key,\n            ...customItemProps\n          } = getCustomizedItemProps({\n            index\n          });\n          return /*#__PURE__*/_jsx(Chip, {\n            label: getOptionLabel(option),\n            size: size,\n            ...customItemProps,\n            ...externalForwardedProps.slotProps.chip\n          }, key);\n        });\n      }\n    }\n  } else if (renderValue && value != null) {\n    startAdornment = renderValue(value, getCustomizedItemProps, ownerState);\n  }\n  if (limitTags > -1 && Array.isArray(startAdornment)) {\n    const more = startAdornment.length - limitTags;\n    if (!focused && more > 0) {\n      startAdornment = startAdornment.splice(0, limitTags);\n      startAdornment.push(/*#__PURE__*/_jsx(\"span\", {\n        className: classes.tag,\n        children: getLimitTagsText(more)\n      }, startAdornment.length));\n    }\n  }\n  const defaultRenderGroup = params => /*#__PURE__*/_jsxs(\"li\", {\n    children: [/*#__PURE__*/_jsx(AutocompleteGroupLabel, {\n      className: classes.groupLabel,\n      ownerState: ownerState,\n      component: \"div\",\n      children: params.group\n    }), /*#__PURE__*/_jsx(AutocompleteGroupUl, {\n      className: classes.groupUl,\n      ownerState: ownerState,\n      children: params.children\n    })]\n  }, params.key);\n  const renderGroup = renderGroupProp || defaultRenderGroup;\n  const defaultRenderOption = (props2, option) => {\n    // Need to clearly apply key because of https://github.com/vercel/next.js/issues/55642\n    const {\n      key,\n      ...otherProps\n    } = props2;\n    return /*#__PURE__*/_jsx(\"li\", {\n      ...otherProps,\n      children: getOptionLabel(option)\n    }, key);\n  };\n  const renderOption = renderOptionProp || defaultRenderOption;\n  const renderListOption = (option, index) => {\n    const optionProps = getOptionProps({\n      option,\n      index\n    });\n    return renderOption({\n      ...optionProps,\n      className: classes.option\n    }, option, {\n      selected: optionProps['aria-selected'],\n      index,\n      inputValue\n    }, ownerState);\n  };\n  const clearIndicatorSlotProps = externalForwardedProps.slotProps.clearIndicator;\n  const popupIndicatorSlotProps = externalForwardedProps.slotProps.popupIndicator;\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(AutocompleteRoot, {\n      ref: ref,\n      className: clsx(classes.root, className),\n      ownerState: ownerState,\n      ...getRootProps(other),\n      children: renderInput({\n        id,\n        disabled,\n        fullWidth: true,\n        size: size === 'small' ? 'small' : undefined,\n        InputLabelProps: getInputLabelProps(),\n        InputProps: {\n          ref: setAnchorEl,\n          className: classes.inputRoot,\n          startAdornment,\n          onMouseDown: event => {\n            if (event.target === event.currentTarget) {\n              handleInputMouseDown(event);\n            }\n          },\n          ...((hasClearIcon || hasPopupIcon) && {\n            endAdornment: /*#__PURE__*/_jsxs(AutocompleteEndAdornment, {\n              className: classes.endAdornment,\n              ownerState: ownerState,\n              children: [hasClearIcon ? /*#__PURE__*/_jsx(AutocompleteClearIndicator, {\n                ...getClearProps(),\n                \"aria-label\": clearText,\n                title: clearText,\n                ownerState: ownerState,\n                ...clearIndicatorSlotProps,\n                className: clsx(classes.clearIndicator, clearIndicatorSlotProps?.className),\n                children: clearIcon\n              }) : null, hasPopupIcon ? /*#__PURE__*/_jsx(AutocompletePopupIndicator, {\n                ...getPopupIndicatorProps(),\n                disabled: disabled,\n                \"aria-label\": popupOpen ? closeText : openText,\n                title: popupOpen ? closeText : openText,\n                ownerState: ownerState,\n                ...popupIndicatorSlotProps,\n                className: clsx(classes.popupIndicator, popupIndicatorSlotProps?.className),\n                children: popupIcon\n              }) : null]\n            })\n          })\n        },\n        inputProps: {\n          className: classes.input,\n          disabled,\n          readOnly,\n          ...getInputProps()\n        }\n      })\n    }), anchorEl ? /*#__PURE__*/_jsx(AutocompletePopper, {\n      as: PopperSlot,\n      ...popperProps,\n      children: /*#__PURE__*/_jsxs(AutocompletePaper, {\n        as: PaperSlot,\n        ...paperProps,\n        children: [loading && groupedOptions.length === 0 ? /*#__PURE__*/_jsx(AutocompleteLoading, {\n          className: classes.loading,\n          ownerState: ownerState,\n          children: loadingText\n        }) : null, groupedOptions.length === 0 && !freeSolo && !loading ? /*#__PURE__*/_jsx(AutocompleteNoOptions, {\n          className: classes.noOptions,\n          ownerState: ownerState,\n          role: \"presentation\",\n          onMouseDown: event => {\n            // Prevent input blur when interacting with the \"no options\" content\n            event.preventDefault();\n          },\n          children: noOptionsText\n        }) : null, groupedOptions.length > 0 ? /*#__PURE__*/_jsx(ListboxSlot, {\n          as: ListboxComponentProp,\n          ...listboxProps,\n          children: groupedOptions.map((option, index) => {\n            if (groupBy) {\n              return renderGroup({\n                key: option.key,\n                group: option.group,\n                children: option.options.map((option2, index2) => renderListOption(option2, option.index + index2))\n              });\n            }\n            return renderListOption(option, index);\n          })\n        }) : null]\n      })\n    }) : null]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Autocomplete.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If `true`, the portion of the selected suggestion that the user hasn't typed,\n   * known as the completion string, appears inline after the input cursor in the textbox.\n   * The inline completion string is visually highlighted and has a selected state.\n   * @default false\n   */\n  autoComplete: PropTypes.bool,\n  /**\n   * If `true`, the first option is automatically highlighted.\n   * @default false\n   */\n  autoHighlight: PropTypes.bool,\n  /**\n   * If `true`, the selected option becomes the value of the input\n   * when the Autocomplete loses focus unless the user chooses\n   * a different option or changes the character string in the input.\n   *\n   * When using the `freeSolo` mode, the typed value will be the input value\n   * if the Autocomplete loses focus without highlighting an option.\n   * @default false\n   */\n  autoSelect: PropTypes.bool,\n  /**\n   * Control if the input should be blurred when an option is selected:\n   *\n   * - `false` the input is not blurred.\n   * - `true` the input is always blurred.\n   * - `touch` the input is blurred after a touch event.\n   * - `mouse` the input is blurred after a mouse event.\n   * @default false\n   */\n  blurOnSelect: PropTypes.oneOfType([PropTypes.oneOf(['mouse', 'touch']), PropTypes.bool]),\n  /**\n   * Props applied to the [`Chip`](https://mui.com/material-ui/api/chip/) element.\n   * @deprecated Use `slotProps.chip` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  ChipProps: PropTypes.object,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The icon to display in place of the default clear icon.\n   * @default <ClearIcon fontSize=\"small\" />\n   */\n  clearIcon: PropTypes.node,\n  /**\n   * If `true`, the input's text is cleared on blur if no value is selected.\n   *\n   * Set it to `true` if you want to help the user enter a new value.\n   * Set it to `false` if you want to help the user resume their search.\n   * @default !props.freeSolo\n   */\n  clearOnBlur: PropTypes.bool,\n  /**\n   * If `true`, clear all values when the user presses escape and the popup is closed.\n   * @default false\n   */\n  clearOnEscape: PropTypes.bool,\n  /**\n   * Override the default text for the *clear* icon button.\n   *\n   * For localization purposes, you can use the provided [translations](https://mui.com/material-ui/guides/localization/).\n   * @default 'Clear'\n   */\n  clearText: PropTypes.string,\n  /**\n   * Override the default text for the *close popup* icon button.\n   *\n   * For localization purposes, you can use the provided [translations](https://mui.com/material-ui/guides/localization/).\n   * @default 'Close'\n   */\n  closeText: PropTypes.string,\n  /**\n   * The props used for each slot inside.\n   * @deprecated Use the `slotProps` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  componentsProps: PropTypes.shape({\n    clearIndicator: PropTypes.object,\n    paper: PropTypes.object,\n    popper: PropTypes.object,\n    popupIndicator: PropTypes.object\n  }),\n  /**\n   * The default value. Use when the component is not controlled.\n   * @default props.multiple ? [] : null\n   */\n  defaultValue: chainPropTypes(PropTypes.any, props => {\n    if (props.multiple && props.defaultValue !== undefined && !Array.isArray(props.defaultValue)) {\n      return new Error(['MUI: The Autocomplete expects the `defaultValue` prop to be an array when `multiple={true}` or undefined.', `However, ${props.defaultValue} was provided.`].join('\\n'));\n    }\n    return null;\n  }),\n  /**\n   * If `true`, the input can't be cleared.\n   * @default false\n   */\n  disableClearable: PropTypes.bool,\n  /**\n   * If `true`, the popup won't close when a value is selected.\n   * @default false\n   */\n  disableCloseOnSelect: PropTypes.bool,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, will allow focus on disabled items.\n   * @default false\n   */\n  disabledItemsFocusable: PropTypes.bool,\n  /**\n   * If `true`, the list box in the popup will not wrap focus.\n   * @default false\n   */\n  disableListWrap: PropTypes.bool,\n  /**\n   * If `true`, the `Popper` content will be under the DOM hierarchy of the parent component.\n   * @default false\n   */\n  disablePortal: PropTypes.bool,\n  /**\n   * A function that determines the filtered options to be rendered on search.\n   *\n   * @default createFilterOptions()\n   * @param {Value[]} options The options to render.\n   * @param {object} state The state of the component.\n   * @returns {Value[]}\n   */\n  filterOptions: PropTypes.func,\n  /**\n   * If `true`, hide the selected options from the list box.\n   * @default false\n   */\n  filterSelectedOptions: PropTypes.bool,\n  /**\n   * Force the visibility display of the popup icon.\n   * @default 'auto'\n   */\n  forcePopupIcon: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.bool]),\n  /**\n   * If `true`, the Autocomplete is free solo, meaning that the user input is not bound to provided options.\n   * @default false\n   */\n  freeSolo: PropTypes.bool,\n  /**\n   * If `true`, the input will take up the full width of its container.\n   * @default false\n   */\n  fullWidth: PropTypes.bool,\n  /**\n   * The label to display when the tags are truncated (`limitTags`).\n   *\n   * @param {number} more The number of truncated tags.\n   * @returns {ReactNode}\n   * @default (more) => `+${more}`\n   */\n  getLimitTagsText: PropTypes.func,\n  /**\n   * Used to determine the disabled state for a given option.\n   *\n   * @param {Value} option The option to test.\n   * @returns {boolean}\n   */\n  getOptionDisabled: PropTypes.func,\n  /**\n   * Used to determine the key for a given option.\n   * This can be useful when the labels of options are not unique (since labels are used as keys by default).\n   *\n   * @param {Value} option The option to get the key for.\n   * @returns {string | number}\n   */\n  getOptionKey: PropTypes.func,\n  /**\n   * Used to determine the string value for a given option.\n   * It's used to fill the input (and the list box options if `renderOption` is not provided).\n   *\n   * If used in free solo mode, it must accept both the type of the options and a string.\n   *\n   * @param {Value} option\n   * @returns {string}\n   * @default (option) => option.label ?? option\n   */\n  getOptionLabel: PropTypes.func,\n  /**\n   * If provided, the options will be grouped under the returned string.\n   * The groupBy value is also used as the text for group headings when `renderGroup` is not provided.\n   *\n   * @param {Value} option The Autocomplete option.\n   * @returns {string}\n   */\n  groupBy: PropTypes.func,\n  /**\n   * If `true`, the component handles the \"Home\" and \"End\" keys when the popup is open.\n   * It should move focus to the first option and last option, respectively.\n   * @default !props.freeSolo\n   */\n  handleHomeEndKeys: PropTypes.bool,\n  /**\n   * This prop is used to help implement the accessibility logic.\n   * If you don't provide an id it will fall back to a randomly generated one.\n   */\n  id: PropTypes.string,\n  /**\n   * If `true`, the highlight can move to the input.\n   * @default false\n   */\n  includeInputInList: PropTypes.bool,\n  /**\n   * The input value.\n   */\n  inputValue: PropTypes.string,\n  /**\n   * Used to determine if the option represents the given value.\n   * Uses strict equality by default.\n   * ⚠️ Both arguments need to be handled, an option can only match with one value.\n   *\n   * @param {Value} option The option to test.\n   * @param {Value} value The value to test against.\n   * @returns {boolean}\n   */\n  isOptionEqualToValue: PropTypes.func,\n  /**\n   * The maximum number of tags that will be visible when not focused.\n   * Set `-1` to disable the limit.\n   * @default -1\n   */\n  limitTags: integerPropType,\n  /**\n   * The component used to render the listbox.\n   * @default 'ul'\n   * @deprecated Use `slotProps.listbox.component` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  ListboxComponent: PropTypes.elementType,\n  /**\n   * Props applied to the Listbox element.\n   * @deprecated Use `slotProps.listbox` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  ListboxProps: PropTypes.object,\n  /**\n   * If `true`, the component is in a loading state.\n   * This shows the `loadingText` in place of suggestions (only if there are no suggestions to show, for example `options` are empty).\n   * @default false\n   */\n  loading: PropTypes.bool,\n  /**\n   * Text to display when in a loading state.\n   *\n   * For localization purposes, you can use the provided [translations](https://mui.com/material-ui/guides/localization/).\n   * @default 'Loading…'\n   */\n  loadingText: PropTypes.node,\n  /**\n   * If `true`, `value` must be an array and the menu will support multiple selections.\n   * @default false\n   */\n  multiple: PropTypes.bool,\n  /**\n   * Text to display when there are no options.\n   *\n   * For localization purposes, you can use the provided [translations](https://mui.com/material-ui/guides/localization/).\n   * @default 'No options'\n   */\n  noOptionsText: PropTypes.node,\n  /**\n   * Callback fired when the value changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {Value|Value[]} value The new value of the component.\n   * @param {string} reason One of \"createOption\", \"selectOption\", \"removeOption\", \"blur\" or \"clear\".\n   * @param {string} [details]\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be closed.\n   * Use in controlled mode (see open).\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {string} reason Can be: `\"toggleInput\"`, `\"escape\"`, `\"selectOption\"`, `\"removeOption\"`, `\"blur\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * Callback fired when the highlight option changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {Value} option The highlighted option.\n   * @param {string} reason Can be: `\"keyboard\"`, `\"mouse\"`, `\"touch\"`.\n   */\n  onHighlightChange: PropTypes.func,\n  /**\n   * Callback fired when the input value changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {string} value The new value of the text input.\n   * @param {string} reason Can be: `\"input\"` (user input), `\"reset\"` (programmatic change), `\"clear\"`, `\"blur\"`, `\"selectOption\"`, `\"removeOption\"`\n   */\n  onInputChange: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyDown: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be opened.\n   * Use in controlled mode (see open).\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   */\n  onOpen: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * If `true`, the popup will open on input focus.\n   * @default false\n   */\n  openOnFocus: PropTypes.bool,\n  /**\n   * Override the default text for the *open popup* icon button.\n   *\n   * For localization purposes, you can use the provided [translations](https://mui.com/material-ui/guides/localization/).\n   * @default 'Open'\n   */\n  openText: PropTypes.string,\n  /**\n   * A list of options that will be shown in the Autocomplete.\n   */\n  options: PropTypes.array.isRequired,\n  /**\n   * The component used to render the body of the popup.\n   * @default Paper\n   * @deprecated Use `slots.paper` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  PaperComponent: PropTypes.elementType,\n  /**\n   * The component used to position the popup.\n   * @default Popper\n   * @deprecated Use `slots.popper` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  PopperComponent: PropTypes.elementType,\n  /**\n   * The icon to display in place of the default popup icon.\n   * @default <ArrowDropDownIcon />\n   */\n  popupIcon: PropTypes.node,\n  /**\n   * If `true`, the component becomes readonly. It is also supported for multiple tags where the tag cannot be deleted.\n   * @default false\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * Render the group.\n   *\n   * @param {AutocompleteRenderGroupParams} params The group to render.\n   * @returns {ReactNode}\n   */\n  renderGroup: PropTypes.func,\n  /**\n   * Render the input.\n   *\n   * **Note:** The `renderInput` prop must return a `TextField` component or a compatible custom component\n   * that correctly forwards `InputProps.ref` and spreads `inputProps`. This ensures proper integration\n   * with the Autocomplete's internal logic (e.g., focus management and keyboard navigation).\n   *\n   * Avoid using components like `DatePicker` or `Select` directly, as they may not forward the required props,\n   * leading to runtime errors or unexpected behavior.\n   *\n   * @param {object} params\n   * @returns {ReactNode}\n   */\n  renderInput: PropTypes.func.isRequired,\n  /**\n   * Render the option, use `getOptionLabel` by default.\n   *\n   * @param {object} props The props to apply on the li element.\n   * @param {Value} option The option to render.\n   * @param {object} state The state of each option.\n   * @param {object} ownerState The state of the Autocomplete component.\n   * @returns {ReactNode}\n   */\n  renderOption: PropTypes.func,\n  /**\n   * Render the selected value when doing multiple selections.\n   *\n   * @deprecated Use `renderValue` prop instead\n   *\n   * @param {Value[]} value The `value` provided to the component.\n   * @param {function} getTagProps A tag props getter.\n   * @param {object} ownerState The state of the Autocomplete component.\n   * @returns {ReactNode}\n   */\n  renderTags: PropTypes.func,\n  /**\n   * Renders the selected value(s) as rich content in the input for both single and multiple selections.\n   *\n   * @param {AutocompleteRenderValue<Value, Multiple, FreeSolo>} value The `value` provided to the component.\n   * @param {function} getItemProps The value item props.\n   * @param {object} ownerState The state of the Autocomplete component.\n   * @returns {ReactNode}\n   */\n  renderValue: PropTypes.func,\n  /**\n   * If `true`, the input's text is selected on focus.\n   * It helps the user clear the selected value.\n   * @default !props.freeSolo\n   */\n  selectOnFocus: PropTypes.bool,\n  /**\n   * The size of the component.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['small', 'medium']), PropTypes.string]),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    chip: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    clearIndicator: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    listbox: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    paper: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    popper: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    popupIndicator: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    listbox: PropTypes.elementType,\n    paper: PropTypes.elementType,\n    popper: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The value of the autocomplete.\n   *\n   * The value must have reference equality with the option in order to be selected.\n   * You can customize the equality behavior with the `isOptionEqualToValue` prop.\n   */\n  value: chainPropTypes(PropTypes.any, props => {\n    if (props.multiple && props.value !== undefined && !Array.isArray(props.value)) {\n      return new Error(['MUI: The Autocomplete expects the `value` prop to be an array when `multiple={true}` or undefined.', `However, ${props.value} was provided.`].join('\\n'));\n    }\n    return null;\n  })\n} : void 0;\nexport default Autocomplete;", "map": {"version": 3, "names": ["_ClearIcon", "_ArrowDropDownIcon", "React", "PropTypes", "clsx", "integerPropType", "chainPropTypes", "composeClasses", "alpha", "useAutocomplete", "createFilterOptions", "<PERSON><PERSON>", "ListSubheader", "Paper", "IconButton", "Chip", "inputClasses", "inputBaseClasses", "outlinedInputClasses", "filledInputClasses", "ClearIcon", "ArrowDropDownIcon", "styled", "memoTheme", "useDefaultProps", "autocompleteClasses", "getAutocompleteUtilityClass", "capitalize", "useSlot", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "classes", "disable<PERSON><PERSON><PERSON>", "expanded", "focused", "fullWidth", "hasClearIcon", "hasPopupIcon", "inputFocused", "popupOpen", "size", "slots", "root", "inputRoot", "input", "tag", "endAdornment", "clearIndicator", "popupIndicator", "popper", "paper", "listbox", "loading", "noOptions", "option", "groupLabel", "groupUl", "AutocompleteRoot", "name", "slot", "overridesResolver", "props", "styles", "visibility", "margin", "max<PERSON><PERSON><PERSON>", "paddingRight", "width", "min<PERSON><PERSON><PERSON>", "paddingBottom", "padding", "sizeSmall", "right", "paddingTop", "paddingLeft", "hidden<PERSON>abel", "flexGrow", "textOverflow", "opacity", "variants", "style", "multiple", "flexWrap", "AutocompleteEndAdornment", "position", "top", "transform", "AutocompleteClearIndicator", "marginRight", "AutocompletePopupIndicator", "popupIndicatorOpen", "AutocompletePopper", "popperDisablePortal", "_ref", "theme", "zIndex", "vars", "modal", "AutocompletePaper", "_ref2", "typography", "body1", "overflow", "AutocompleteLoading", "_ref3", "color", "palette", "text", "secondary", "AutocompleteNoOptions", "_ref4", "AutocompleteListbox", "_ref5", "listStyle", "maxHeight", "minHeight", "display", "justifyContent", "alignItems", "cursor", "boxSizing", "outline", "WebkitTapHighlightColor", "breakpoints", "up", "backgroundColor", "action", "hover", "disabledOpacity", "pointerEvents", "focusVisible", "focus", "primary", "mainChannel", "selectedOpacity", "main", "hoverOpacity", "selected", "focusOpacity", "AutocompleteGroupLabel", "_ref6", "background", "AutocompleteGroupUl", "Autocomplete", "forwardRef", "inProps", "ref", "autoComplete", "autoHighlight", "autoSelect", "blurOnSelect", "ChipProps", "ChipPropsProp", "className", "clearIcon", "fontSize", "clearOnBlur", "freeSolo", "clearOnEscape", "clearText", "closeText", "componentsProps", "defaultValue", "disableClearable", "disableCloseOnSelect", "disabled", "disabledItemsFocusable", "disableListWrap", "filterOptions", "filterSelectedOptions", "forcePopupIcon", "getLimitTagsText", "more", "getOptionDisabled", "getOption<PERSON>ey", "getOptionLabel", "getOptionLabelProp", "isOptionEqualToValue", "groupBy", "handleHomeEndKeys", "id", "idProp", "includeInputInList", "inputValue", "inputValueProp", "limitTags", "ListboxComponent", "ListboxComponentProp", "ListboxProps", "ListboxPropsProp", "loadingText", "noOptionsText", "onChange", "onClose", "onHighlightChange", "onInputChange", "onOpen", "open", "openOnFocus", "openText", "options", "PaperComponent", "PaperComponentProp", "PopperComponent", "PopperComponentProp", "popupIcon", "readOnly", "renderGroup", "renderGroupProp", "renderInput", "renderOption", "renderOptionProp", "renderTags", "renderValue", "selectOnFocus", "slotProps", "value", "valueProp", "other", "getRootProps", "getInputProps", "getInputLabelProps", "getPopupIndicatorProps", "getClearProps", "getItemProps", "getListboxProps", "getOptionProps", "dirty", "focusedItem", "anchorEl", "setAnchorEl", "groupedOptions", "componentName", "onMouseDown", "handleInputMouseDown", "listboxRef", "otherListboxProps", "defaultGetOptionLabel", "label", "externalForwardedProps", "chip", "ListboxSlot", "listboxProps", "elementType", "additionalProps", "PaperSlot", "paperProps", "PopperSlot", "popperProps", "clientWidth", "role", "startAdornment", "getCustomizedItemProps", "params", "length", "map", "index", "key", "customItemProps", "Array", "isArray", "splice", "push", "children", "defaultRenderGroup", "component", "group", "defaultRenderOption", "props2", "otherProps", "renderListOption", "optionProps", "clearIndicatorSlotProps", "popupIndicatorSlotProps", "Fragment", "undefined", "InputLabelProps", "InputProps", "event", "target", "currentTarget", "title", "inputProps", "as", "preventDefault", "option2", "index2", "process", "env", "NODE_ENV", "propTypes", "bool", "oneOfType", "oneOf", "object", "string", "node", "shape", "any", "Error", "join", "func", "onKeyDown", "array", "isRequired", "sx", "arrayOf"], "sources": ["C:/laragon/www/frontend/node_modules/@mui/material/esm/Autocomplete/Autocomplete.js"], "sourcesContent": ["'use client';\n\nvar _ClearIcon, _ArrowDropDownIcon;\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport integerPropType from '@mui/utils/integerPropType';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport useAutocomplete, { createFilterOptions } from \"../useAutocomplete/index.js\";\nimport Popper from \"../Popper/index.js\";\nimport ListSubheader from \"../ListSubheader/index.js\";\nimport Paper from \"../Paper/index.js\";\nimport IconButton from \"../IconButton/index.js\";\nimport Chip from \"../Chip/index.js\";\nimport inputClasses from \"../Input/inputClasses.js\";\nimport inputBaseClasses from \"../InputBase/inputBaseClasses.js\";\nimport outlinedInputClasses from \"../OutlinedInput/outlinedInputClasses.js\";\nimport filledInputClasses from \"../FilledInput/filledInputClasses.js\";\nimport ClearIcon from \"../internal/svg-icons/Close.js\";\nimport ArrowDropDownIcon from \"../internal/svg-icons/ArrowDropDown.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport autocompleteClasses, { getAutocompleteUtilityClass } from \"./autocompleteClasses.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disablePortal,\n    expanded,\n    focused,\n    fullWidth,\n    hasClearIcon,\n    hasPopupIcon,\n    inputFocused,\n    popupOpen,\n    size\n  } = ownerState;\n  const slots = {\n    root: ['root', expanded && 'expanded', focused && 'focused', fullWidth && 'fullWidth', hasClearIcon && 'hasClearIcon', hasPopupIcon && 'hasPopupIcon'],\n    inputRoot: ['inputRoot'],\n    input: ['input', inputFocused && 'inputFocused'],\n    tag: ['tag', `tagSize${capitalize(size)}`],\n    endAdornment: ['endAdornment'],\n    clearIndicator: ['clearIndicator'],\n    popupIndicator: ['popupIndicator', popupOpen && 'popupIndicatorOpen'],\n    popper: ['popper', disablePortal && 'popperDisablePortal'],\n    paper: ['paper'],\n    listbox: ['listbox'],\n    loading: ['loading'],\n    noOptions: ['noOptions'],\n    option: ['option'],\n    groupLabel: ['groupLabel'],\n    groupUl: ['groupUl']\n  };\n  return composeClasses(slots, getAutocompleteUtilityClass, classes);\n};\nconst AutocompleteRoot = styled('div', {\n  name: 'MuiAutocomplete',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    const {\n      fullWidth,\n      hasClearIcon,\n      hasPopupIcon,\n      inputFocused,\n      size\n    } = ownerState;\n    return [{\n      [`& .${autocompleteClasses.tag}`]: styles.tag\n    }, {\n      [`& .${autocompleteClasses.tag}`]: styles[`tagSize${capitalize(size)}`]\n    }, {\n      [`& .${autocompleteClasses.inputRoot}`]: styles.inputRoot\n    }, {\n      [`& .${autocompleteClasses.input}`]: styles.input\n    }, {\n      [`& .${autocompleteClasses.input}`]: inputFocused && styles.inputFocused\n    }, styles.root, fullWidth && styles.fullWidth, hasPopupIcon && styles.hasPopupIcon, hasClearIcon && styles.hasClearIcon];\n  }\n})({\n  [`&.${autocompleteClasses.focused} .${autocompleteClasses.clearIndicator}`]: {\n    visibility: 'visible'\n  },\n  /* Avoid double tap issue on iOS */\n  '@media (pointer: fine)': {\n    [`&:hover .${autocompleteClasses.clearIndicator}`]: {\n      visibility: 'visible'\n    }\n  },\n  [`& .${autocompleteClasses.tag}`]: {\n    margin: 3,\n    maxWidth: 'calc(100% - 6px)'\n  },\n  [`& .${autocompleteClasses.inputRoot}`]: {\n    [`.${autocompleteClasses.hasPopupIcon}&, .${autocompleteClasses.hasClearIcon}&`]: {\n      paddingRight: 26 + 4\n    },\n    [`.${autocompleteClasses.hasPopupIcon}.${autocompleteClasses.hasClearIcon}&`]: {\n      paddingRight: 52 + 4\n    },\n    [`& .${autocompleteClasses.input}`]: {\n      width: 0,\n      minWidth: 30\n    }\n  },\n  [`& .${inputClasses.root}`]: {\n    paddingBottom: 1,\n    '& .MuiInput-input': {\n      padding: '4px 4px 4px 0px'\n    }\n  },\n  [`& .${inputClasses.root}.${inputBaseClasses.sizeSmall}`]: {\n    [`& .${inputClasses.input}`]: {\n      padding: '2px 4px 3px 0'\n    }\n  },\n  [`& .${outlinedInputClasses.root}`]: {\n    padding: 9,\n    [`.${autocompleteClasses.hasPopupIcon}&, .${autocompleteClasses.hasClearIcon}&`]: {\n      paddingRight: 26 + 4 + 9\n    },\n    [`.${autocompleteClasses.hasPopupIcon}.${autocompleteClasses.hasClearIcon}&`]: {\n      paddingRight: 52 + 4 + 9\n    },\n    [`& .${autocompleteClasses.input}`]: {\n      padding: '7.5px 4px 7.5px 5px'\n    },\n    [`& .${autocompleteClasses.endAdornment}`]: {\n      right: 9\n    }\n  },\n  [`& .${outlinedInputClasses.root}.${inputBaseClasses.sizeSmall}`]: {\n    // Don't specify paddingRight, as it overrides the default value set when there is only\n    // one of the popup or clear icon as the specificity is equal so the latter one wins\n    paddingTop: 6,\n    paddingBottom: 6,\n    paddingLeft: 6,\n    [`& .${autocompleteClasses.input}`]: {\n      padding: '2.5px 4px 2.5px 8px'\n    }\n  },\n  [`& .${filledInputClasses.root}`]: {\n    paddingTop: 19,\n    paddingLeft: 8,\n    [`.${autocompleteClasses.hasPopupIcon}&, .${autocompleteClasses.hasClearIcon}&`]: {\n      paddingRight: 26 + 4 + 9\n    },\n    [`.${autocompleteClasses.hasPopupIcon}.${autocompleteClasses.hasClearIcon}&`]: {\n      paddingRight: 52 + 4 + 9\n    },\n    [`& .${filledInputClasses.input}`]: {\n      padding: '7px 4px'\n    },\n    [`& .${autocompleteClasses.endAdornment}`]: {\n      right: 9\n    }\n  },\n  [`& .${filledInputClasses.root}.${inputBaseClasses.sizeSmall}`]: {\n    paddingBottom: 1,\n    [`& .${filledInputClasses.input}`]: {\n      padding: '2.5px 4px'\n    }\n  },\n  [`& .${inputBaseClasses.hiddenLabel}`]: {\n    paddingTop: 8\n  },\n  [`& .${filledInputClasses.root}.${inputBaseClasses.hiddenLabel}`]: {\n    paddingTop: 0,\n    paddingBottom: 0,\n    [`& .${autocompleteClasses.input}`]: {\n      paddingTop: 16,\n      paddingBottom: 17\n    }\n  },\n  [`& .${filledInputClasses.root}.${inputBaseClasses.hiddenLabel}.${inputBaseClasses.sizeSmall}`]: {\n    [`& .${autocompleteClasses.input}`]: {\n      paddingTop: 8,\n      paddingBottom: 9\n    }\n  },\n  [`& .${autocompleteClasses.input}`]: {\n    flexGrow: 1,\n    textOverflow: 'ellipsis',\n    opacity: 0\n  },\n  variants: [{\n    props: {\n      fullWidth: true\n    },\n    style: {\n      width: '100%'\n    }\n  }, {\n    props: {\n      size: 'small'\n    },\n    style: {\n      [`& .${autocompleteClasses.tag}`]: {\n        margin: 2,\n        maxWidth: 'calc(100% - 4px)'\n      }\n    }\n  }, {\n    props: {\n      inputFocused: true\n    },\n    style: {\n      [`& .${autocompleteClasses.input}`]: {\n        opacity: 1\n      }\n    }\n  }, {\n    props: {\n      multiple: true\n    },\n    style: {\n      [`& .${autocompleteClasses.inputRoot}`]: {\n        flexWrap: 'wrap'\n      }\n    }\n  }]\n});\nconst AutocompleteEndAdornment = styled('div', {\n  name: 'MuiAutocomplete',\n  slot: 'EndAdornment'\n})({\n  // We use a position absolute to support wrapping tags.\n  position: 'absolute',\n  right: 0,\n  top: '50%',\n  transform: 'translate(0, -50%)'\n});\nconst AutocompleteClearIndicator = styled(IconButton, {\n  name: 'MuiAutocomplete',\n  slot: 'ClearIndicator'\n})({\n  marginRight: -2,\n  padding: 4,\n  visibility: 'hidden'\n});\nconst AutocompletePopupIndicator = styled(IconButton, {\n  name: 'MuiAutocomplete',\n  slot: 'PopupIndicator',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.popupIndicator, ownerState.popupOpen && styles.popupIndicatorOpen];\n  }\n})({\n  padding: 2,\n  marginRight: -2,\n  variants: [{\n    props: {\n      popupOpen: true\n    },\n    style: {\n      transform: 'rotate(180deg)'\n    }\n  }]\n});\nconst AutocompletePopper = styled(Popper, {\n  name: 'MuiAutocomplete',\n  slot: 'Popper',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${autocompleteClasses.option}`]: styles.option\n    }, styles.popper, ownerState.disablePortal && styles.popperDisablePortal];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  zIndex: (theme.vars || theme).zIndex.modal,\n  variants: [{\n    props: {\n      disablePortal: true\n    },\n    style: {\n      position: 'absolute'\n    }\n  }]\n})));\nconst AutocompletePaper = styled(Paper, {\n  name: 'MuiAutocomplete',\n  slot: 'Paper'\n})(memoTheme(({\n  theme\n}) => ({\n  ...theme.typography.body1,\n  overflow: 'auto'\n})));\nconst AutocompleteLoading = styled('div', {\n  name: 'MuiAutocomplete',\n  slot: 'Loading'\n})(memoTheme(({\n  theme\n}) => ({\n  color: (theme.vars || theme).palette.text.secondary,\n  padding: '14px 16px'\n})));\nconst AutocompleteNoOptions = styled('div', {\n  name: 'MuiAutocomplete',\n  slot: 'NoOptions'\n})(memoTheme(({\n  theme\n}) => ({\n  color: (theme.vars || theme).palette.text.secondary,\n  padding: '14px 16px'\n})));\nconst AutocompleteListbox = styled('ul', {\n  name: 'MuiAutocomplete',\n  slot: 'Listbox'\n})(memoTheme(({\n  theme\n}) => ({\n  listStyle: 'none',\n  margin: 0,\n  padding: '8px 0',\n  maxHeight: '40vh',\n  overflow: 'auto',\n  position: 'relative',\n  [`& .${autocompleteClasses.option}`]: {\n    minHeight: 48,\n    display: 'flex',\n    overflow: 'hidden',\n    justifyContent: 'flex-start',\n    alignItems: 'center',\n    cursor: 'pointer',\n    paddingTop: 6,\n    boxSizing: 'border-box',\n    outline: '0',\n    WebkitTapHighlightColor: 'transparent',\n    paddingBottom: 6,\n    paddingLeft: 16,\n    paddingRight: 16,\n    [theme.breakpoints.up('sm')]: {\n      minHeight: 'auto'\n    },\n    [`&.${autocompleteClasses.focused}`]: {\n      backgroundColor: (theme.vars || theme).palette.action.hover,\n      // Reset on touch devices, it doesn't add specificity\n      '@media (hover: none)': {\n        backgroundColor: 'transparent'\n      }\n    },\n    '&[aria-disabled=\"true\"]': {\n      opacity: (theme.vars || theme).palette.action.disabledOpacity,\n      pointerEvents: 'none'\n    },\n    [`&.${autocompleteClasses.focusVisible}`]: {\n      backgroundColor: (theme.vars || theme).palette.action.focus\n    },\n    '&[aria-selected=\"true\"]': {\n      backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / ${theme.vars.palette.action.selectedOpacity})` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity),\n      [`&.${autocompleteClasses.focused}`]: {\n        backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.hoverOpacity}))` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity + theme.palette.action.hoverOpacity),\n        // Reset on touch devices, it doesn't add specificity\n        '@media (hover: none)': {\n          backgroundColor: (theme.vars || theme).palette.action.selected\n        }\n      },\n      [`&.${autocompleteClasses.focusVisible}`]: {\n        backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.focusOpacity}))` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity + theme.palette.action.focusOpacity)\n      }\n    }\n  }\n})));\nconst AutocompleteGroupLabel = styled(ListSubheader, {\n  name: 'MuiAutocomplete',\n  slot: 'GroupLabel'\n})(memoTheme(({\n  theme\n}) => ({\n  backgroundColor: (theme.vars || theme).palette.background.paper,\n  top: -8\n})));\nconst AutocompleteGroupUl = styled('ul', {\n  name: 'MuiAutocomplete',\n  slot: 'GroupUl'\n})({\n  padding: 0,\n  [`& .${autocompleteClasses.option}`]: {\n    paddingLeft: 24\n  }\n});\nexport { createFilterOptions };\nconst Autocomplete = /*#__PURE__*/React.forwardRef(function Autocomplete(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiAutocomplete'\n  });\n\n  /* eslint-disable @typescript-eslint/no-unused-vars */\n  const {\n    autoComplete = false,\n    autoHighlight = false,\n    autoSelect = false,\n    blurOnSelect = false,\n    ChipProps: ChipPropsProp,\n    className,\n    clearIcon = _ClearIcon || (_ClearIcon = /*#__PURE__*/_jsx(ClearIcon, {\n      fontSize: \"small\"\n    })),\n    clearOnBlur = !props.freeSolo,\n    clearOnEscape = false,\n    clearText = 'Clear',\n    closeText = 'Close',\n    componentsProps,\n    defaultValue = props.multiple ? [] : null,\n    disableClearable = false,\n    disableCloseOnSelect = false,\n    disabled = false,\n    disabledItemsFocusable = false,\n    disableListWrap = false,\n    disablePortal = false,\n    filterOptions,\n    filterSelectedOptions = false,\n    forcePopupIcon = 'auto',\n    freeSolo = false,\n    fullWidth = false,\n    getLimitTagsText = more => `+${more}`,\n    getOptionDisabled,\n    getOptionKey,\n    getOptionLabel: getOptionLabelProp,\n    isOptionEqualToValue,\n    groupBy,\n    handleHomeEndKeys = !props.freeSolo,\n    id: idProp,\n    includeInputInList = false,\n    inputValue: inputValueProp,\n    limitTags = -1,\n    ListboxComponent: ListboxComponentProp,\n    ListboxProps: ListboxPropsProp,\n    loading = false,\n    loadingText = 'Loading…',\n    multiple = false,\n    noOptionsText = 'No options',\n    onChange,\n    onClose,\n    onHighlightChange,\n    onInputChange,\n    onOpen,\n    open,\n    openOnFocus = false,\n    openText = 'Open',\n    options,\n    PaperComponent: PaperComponentProp,\n    PopperComponent: PopperComponentProp,\n    popupIcon = _ArrowDropDownIcon || (_ArrowDropDownIcon = /*#__PURE__*/_jsx(ArrowDropDownIcon, {})),\n    readOnly = false,\n    renderGroup: renderGroupProp,\n    renderInput,\n    renderOption: renderOptionProp,\n    renderTags,\n    renderValue,\n    selectOnFocus = !props.freeSolo,\n    size = 'medium',\n    slots = {},\n    slotProps = {},\n    value: valueProp,\n    ...other\n  } = props;\n  /* eslint-enable @typescript-eslint/no-unused-vars */\n\n  const {\n    getRootProps,\n    getInputProps,\n    getInputLabelProps,\n    getPopupIndicatorProps,\n    getClearProps,\n    getItemProps,\n    getListboxProps,\n    getOptionProps,\n    value,\n    dirty,\n    expanded,\n    id,\n    popupOpen,\n    focused,\n    focusedItem,\n    anchorEl,\n    setAnchorEl,\n    inputValue,\n    groupedOptions\n  } = useAutocomplete({\n    ...props,\n    componentName: 'Autocomplete'\n  });\n  const hasClearIcon = !disableClearable && !disabled && dirty && !readOnly;\n  const hasPopupIcon = (!freeSolo || forcePopupIcon === true) && forcePopupIcon !== false;\n  const {\n    onMouseDown: handleInputMouseDown\n  } = getInputProps();\n  const {\n    ref: listboxRef,\n    ...otherListboxProps\n  } = getListboxProps();\n  const defaultGetOptionLabel = option => option.label ?? option;\n  const getOptionLabel = getOptionLabelProp || defaultGetOptionLabel;\n\n  // If you modify this, make sure to keep the `AutocompleteOwnerState` type in sync.\n  const ownerState = {\n    ...props,\n    disablePortal,\n    expanded,\n    focused,\n    fullWidth,\n    getOptionLabel,\n    hasClearIcon,\n    hasPopupIcon,\n    inputFocused: focusedItem === -1,\n    popupOpen,\n    size\n  };\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots: {\n      paper: PaperComponentProp,\n      popper: PopperComponentProp,\n      ...slots\n    },\n    slotProps: {\n      chip: ChipPropsProp,\n      listbox: ListboxPropsProp,\n      ...componentsProps,\n      ...slotProps\n    }\n  };\n  const [ListboxSlot, listboxProps] = useSlot('listbox', {\n    elementType: AutocompleteListbox,\n    externalForwardedProps,\n    ownerState,\n    className: classes.listbox,\n    additionalProps: otherListboxProps,\n    ref: listboxRef\n  });\n  const [PaperSlot, paperProps] = useSlot('paper', {\n    elementType: Paper,\n    externalForwardedProps,\n    ownerState,\n    className: classes.paper\n  });\n  const [PopperSlot, popperProps] = useSlot('popper', {\n    elementType: Popper,\n    externalForwardedProps,\n    ownerState,\n    className: classes.popper,\n    additionalProps: {\n      disablePortal,\n      style: {\n        width: anchorEl ? anchorEl.clientWidth : null\n      },\n      role: 'presentation',\n      anchorEl,\n      open: popupOpen\n    }\n  });\n  let startAdornment;\n  const getCustomizedItemProps = params => ({\n    className: classes.tag,\n    disabled,\n    ...getItemProps(params)\n  });\n  if (multiple) {\n    if (value.length > 0) {\n      if (renderTags) {\n        startAdornment = renderTags(value, getCustomizedItemProps, ownerState);\n      } else if (renderValue) {\n        startAdornment = renderValue(value, getCustomizedItemProps, ownerState);\n      } else {\n        startAdornment = value.map((option, index) => {\n          const {\n            key,\n            ...customItemProps\n          } = getCustomizedItemProps({\n            index\n          });\n          return /*#__PURE__*/_jsx(Chip, {\n            label: getOptionLabel(option),\n            size: size,\n            ...customItemProps,\n            ...externalForwardedProps.slotProps.chip\n          }, key);\n        });\n      }\n    }\n  } else if (renderValue && value != null) {\n    startAdornment = renderValue(value, getCustomizedItemProps, ownerState);\n  }\n  if (limitTags > -1 && Array.isArray(startAdornment)) {\n    const more = startAdornment.length - limitTags;\n    if (!focused && more > 0) {\n      startAdornment = startAdornment.splice(0, limitTags);\n      startAdornment.push(/*#__PURE__*/_jsx(\"span\", {\n        className: classes.tag,\n        children: getLimitTagsText(more)\n      }, startAdornment.length));\n    }\n  }\n  const defaultRenderGroup = params => /*#__PURE__*/_jsxs(\"li\", {\n    children: [/*#__PURE__*/_jsx(AutocompleteGroupLabel, {\n      className: classes.groupLabel,\n      ownerState: ownerState,\n      component: \"div\",\n      children: params.group\n    }), /*#__PURE__*/_jsx(AutocompleteGroupUl, {\n      className: classes.groupUl,\n      ownerState: ownerState,\n      children: params.children\n    })]\n  }, params.key);\n  const renderGroup = renderGroupProp || defaultRenderGroup;\n  const defaultRenderOption = (props2, option) => {\n    // Need to clearly apply key because of https://github.com/vercel/next.js/issues/55642\n    const {\n      key,\n      ...otherProps\n    } = props2;\n    return /*#__PURE__*/_jsx(\"li\", {\n      ...otherProps,\n      children: getOptionLabel(option)\n    }, key);\n  };\n  const renderOption = renderOptionProp || defaultRenderOption;\n  const renderListOption = (option, index) => {\n    const optionProps = getOptionProps({\n      option,\n      index\n    });\n    return renderOption({\n      ...optionProps,\n      className: classes.option\n    }, option, {\n      selected: optionProps['aria-selected'],\n      index,\n      inputValue\n    }, ownerState);\n  };\n  const clearIndicatorSlotProps = externalForwardedProps.slotProps.clearIndicator;\n  const popupIndicatorSlotProps = externalForwardedProps.slotProps.popupIndicator;\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(AutocompleteRoot, {\n      ref: ref,\n      className: clsx(classes.root, className),\n      ownerState: ownerState,\n      ...getRootProps(other),\n      children: renderInput({\n        id,\n        disabled,\n        fullWidth: true,\n        size: size === 'small' ? 'small' : undefined,\n        InputLabelProps: getInputLabelProps(),\n        InputProps: {\n          ref: setAnchorEl,\n          className: classes.inputRoot,\n          startAdornment,\n          onMouseDown: event => {\n            if (event.target === event.currentTarget) {\n              handleInputMouseDown(event);\n            }\n          },\n          ...((hasClearIcon || hasPopupIcon) && {\n            endAdornment: /*#__PURE__*/_jsxs(AutocompleteEndAdornment, {\n              className: classes.endAdornment,\n              ownerState: ownerState,\n              children: [hasClearIcon ? /*#__PURE__*/_jsx(AutocompleteClearIndicator, {\n                ...getClearProps(),\n                \"aria-label\": clearText,\n                title: clearText,\n                ownerState: ownerState,\n                ...clearIndicatorSlotProps,\n                className: clsx(classes.clearIndicator, clearIndicatorSlotProps?.className),\n                children: clearIcon\n              }) : null, hasPopupIcon ? /*#__PURE__*/_jsx(AutocompletePopupIndicator, {\n                ...getPopupIndicatorProps(),\n                disabled: disabled,\n                \"aria-label\": popupOpen ? closeText : openText,\n                title: popupOpen ? closeText : openText,\n                ownerState: ownerState,\n                ...popupIndicatorSlotProps,\n                className: clsx(classes.popupIndicator, popupIndicatorSlotProps?.className),\n                children: popupIcon\n              }) : null]\n            })\n          })\n        },\n        inputProps: {\n          className: classes.input,\n          disabled,\n          readOnly,\n          ...getInputProps()\n        }\n      })\n    }), anchorEl ? /*#__PURE__*/_jsx(AutocompletePopper, {\n      as: PopperSlot,\n      ...popperProps,\n      children: /*#__PURE__*/_jsxs(AutocompletePaper, {\n        as: PaperSlot,\n        ...paperProps,\n        children: [loading && groupedOptions.length === 0 ? /*#__PURE__*/_jsx(AutocompleteLoading, {\n          className: classes.loading,\n          ownerState: ownerState,\n          children: loadingText\n        }) : null, groupedOptions.length === 0 && !freeSolo && !loading ? /*#__PURE__*/_jsx(AutocompleteNoOptions, {\n          className: classes.noOptions,\n          ownerState: ownerState,\n          role: \"presentation\",\n          onMouseDown: event => {\n            // Prevent input blur when interacting with the \"no options\" content\n            event.preventDefault();\n          },\n          children: noOptionsText\n        }) : null, groupedOptions.length > 0 ? /*#__PURE__*/_jsx(ListboxSlot, {\n          as: ListboxComponentProp,\n          ...listboxProps,\n          children: groupedOptions.map((option, index) => {\n            if (groupBy) {\n              return renderGroup({\n                key: option.key,\n                group: option.group,\n                children: option.options.map((option2, index2) => renderListOption(option2, option.index + index2))\n              });\n            }\n            return renderListOption(option, index);\n          })\n        }) : null]\n      })\n    }) : null]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Autocomplete.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If `true`, the portion of the selected suggestion that the user hasn't typed,\n   * known as the completion string, appears inline after the input cursor in the textbox.\n   * The inline completion string is visually highlighted and has a selected state.\n   * @default false\n   */\n  autoComplete: PropTypes.bool,\n  /**\n   * If `true`, the first option is automatically highlighted.\n   * @default false\n   */\n  autoHighlight: PropTypes.bool,\n  /**\n   * If `true`, the selected option becomes the value of the input\n   * when the Autocomplete loses focus unless the user chooses\n   * a different option or changes the character string in the input.\n   *\n   * When using the `freeSolo` mode, the typed value will be the input value\n   * if the Autocomplete loses focus without highlighting an option.\n   * @default false\n   */\n  autoSelect: PropTypes.bool,\n  /**\n   * Control if the input should be blurred when an option is selected:\n   *\n   * - `false` the input is not blurred.\n   * - `true` the input is always blurred.\n   * - `touch` the input is blurred after a touch event.\n   * - `mouse` the input is blurred after a mouse event.\n   * @default false\n   */\n  blurOnSelect: PropTypes.oneOfType([PropTypes.oneOf(['mouse', 'touch']), PropTypes.bool]),\n  /**\n   * Props applied to the [`Chip`](https://mui.com/material-ui/api/chip/) element.\n   * @deprecated Use `slotProps.chip` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  ChipProps: PropTypes.object,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The icon to display in place of the default clear icon.\n   * @default <ClearIcon fontSize=\"small\" />\n   */\n  clearIcon: PropTypes.node,\n  /**\n   * If `true`, the input's text is cleared on blur if no value is selected.\n   *\n   * Set it to `true` if you want to help the user enter a new value.\n   * Set it to `false` if you want to help the user resume their search.\n   * @default !props.freeSolo\n   */\n  clearOnBlur: PropTypes.bool,\n  /**\n   * If `true`, clear all values when the user presses escape and the popup is closed.\n   * @default false\n   */\n  clearOnEscape: PropTypes.bool,\n  /**\n   * Override the default text for the *clear* icon button.\n   *\n   * For localization purposes, you can use the provided [translations](https://mui.com/material-ui/guides/localization/).\n   * @default 'Clear'\n   */\n  clearText: PropTypes.string,\n  /**\n   * Override the default text for the *close popup* icon button.\n   *\n   * For localization purposes, you can use the provided [translations](https://mui.com/material-ui/guides/localization/).\n   * @default 'Close'\n   */\n  closeText: PropTypes.string,\n  /**\n   * The props used for each slot inside.\n   * @deprecated Use the `slotProps` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  componentsProps: PropTypes.shape({\n    clearIndicator: PropTypes.object,\n    paper: PropTypes.object,\n    popper: PropTypes.object,\n    popupIndicator: PropTypes.object\n  }),\n  /**\n   * The default value. Use when the component is not controlled.\n   * @default props.multiple ? [] : null\n   */\n  defaultValue: chainPropTypes(PropTypes.any, props => {\n    if (props.multiple && props.defaultValue !== undefined && !Array.isArray(props.defaultValue)) {\n      return new Error(['MUI: The Autocomplete expects the `defaultValue` prop to be an array when `multiple={true}` or undefined.', `However, ${props.defaultValue} was provided.`].join('\\n'));\n    }\n    return null;\n  }),\n  /**\n   * If `true`, the input can't be cleared.\n   * @default false\n   */\n  disableClearable: PropTypes.bool,\n  /**\n   * If `true`, the popup won't close when a value is selected.\n   * @default false\n   */\n  disableCloseOnSelect: PropTypes.bool,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, will allow focus on disabled items.\n   * @default false\n   */\n  disabledItemsFocusable: PropTypes.bool,\n  /**\n   * If `true`, the list box in the popup will not wrap focus.\n   * @default false\n   */\n  disableListWrap: PropTypes.bool,\n  /**\n   * If `true`, the `Popper` content will be under the DOM hierarchy of the parent component.\n   * @default false\n   */\n  disablePortal: PropTypes.bool,\n  /**\n   * A function that determines the filtered options to be rendered on search.\n   *\n   * @default createFilterOptions()\n   * @param {Value[]} options The options to render.\n   * @param {object} state The state of the component.\n   * @returns {Value[]}\n   */\n  filterOptions: PropTypes.func,\n  /**\n   * If `true`, hide the selected options from the list box.\n   * @default false\n   */\n  filterSelectedOptions: PropTypes.bool,\n  /**\n   * Force the visibility display of the popup icon.\n   * @default 'auto'\n   */\n  forcePopupIcon: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.bool]),\n  /**\n   * If `true`, the Autocomplete is free solo, meaning that the user input is not bound to provided options.\n   * @default false\n   */\n  freeSolo: PropTypes.bool,\n  /**\n   * If `true`, the input will take up the full width of its container.\n   * @default false\n   */\n  fullWidth: PropTypes.bool,\n  /**\n   * The label to display when the tags are truncated (`limitTags`).\n   *\n   * @param {number} more The number of truncated tags.\n   * @returns {ReactNode}\n   * @default (more) => `+${more}`\n   */\n  getLimitTagsText: PropTypes.func,\n  /**\n   * Used to determine the disabled state for a given option.\n   *\n   * @param {Value} option The option to test.\n   * @returns {boolean}\n   */\n  getOptionDisabled: PropTypes.func,\n  /**\n   * Used to determine the key for a given option.\n   * This can be useful when the labels of options are not unique (since labels are used as keys by default).\n   *\n   * @param {Value} option The option to get the key for.\n   * @returns {string | number}\n   */\n  getOptionKey: PropTypes.func,\n  /**\n   * Used to determine the string value for a given option.\n   * It's used to fill the input (and the list box options if `renderOption` is not provided).\n   *\n   * If used in free solo mode, it must accept both the type of the options and a string.\n   *\n   * @param {Value} option\n   * @returns {string}\n   * @default (option) => option.label ?? option\n   */\n  getOptionLabel: PropTypes.func,\n  /**\n   * If provided, the options will be grouped under the returned string.\n   * The groupBy value is also used as the text for group headings when `renderGroup` is not provided.\n   *\n   * @param {Value} option The Autocomplete option.\n   * @returns {string}\n   */\n  groupBy: PropTypes.func,\n  /**\n   * If `true`, the component handles the \"Home\" and \"End\" keys when the popup is open.\n   * It should move focus to the first option and last option, respectively.\n   * @default !props.freeSolo\n   */\n  handleHomeEndKeys: PropTypes.bool,\n  /**\n   * This prop is used to help implement the accessibility logic.\n   * If you don't provide an id it will fall back to a randomly generated one.\n   */\n  id: PropTypes.string,\n  /**\n   * If `true`, the highlight can move to the input.\n   * @default false\n   */\n  includeInputInList: PropTypes.bool,\n  /**\n   * The input value.\n   */\n  inputValue: PropTypes.string,\n  /**\n   * Used to determine if the option represents the given value.\n   * Uses strict equality by default.\n   * ⚠️ Both arguments need to be handled, an option can only match with one value.\n   *\n   * @param {Value} option The option to test.\n   * @param {Value} value The value to test against.\n   * @returns {boolean}\n   */\n  isOptionEqualToValue: PropTypes.func,\n  /**\n   * The maximum number of tags that will be visible when not focused.\n   * Set `-1` to disable the limit.\n   * @default -1\n   */\n  limitTags: integerPropType,\n  /**\n   * The component used to render the listbox.\n   * @default 'ul'\n   * @deprecated Use `slotProps.listbox.component` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  ListboxComponent: PropTypes.elementType,\n  /**\n   * Props applied to the Listbox element.\n   * @deprecated Use `slotProps.listbox` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  ListboxProps: PropTypes.object,\n  /**\n   * If `true`, the component is in a loading state.\n   * This shows the `loadingText` in place of suggestions (only if there are no suggestions to show, for example `options` are empty).\n   * @default false\n   */\n  loading: PropTypes.bool,\n  /**\n   * Text to display when in a loading state.\n   *\n   * For localization purposes, you can use the provided [translations](https://mui.com/material-ui/guides/localization/).\n   * @default 'Loading…'\n   */\n  loadingText: PropTypes.node,\n  /**\n   * If `true`, `value` must be an array and the menu will support multiple selections.\n   * @default false\n   */\n  multiple: PropTypes.bool,\n  /**\n   * Text to display when there are no options.\n   *\n   * For localization purposes, you can use the provided [translations](https://mui.com/material-ui/guides/localization/).\n   * @default 'No options'\n   */\n  noOptionsText: PropTypes.node,\n  /**\n   * Callback fired when the value changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {Value|Value[]} value The new value of the component.\n   * @param {string} reason One of \"createOption\", \"selectOption\", \"removeOption\", \"blur\" or \"clear\".\n   * @param {string} [details]\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be closed.\n   * Use in controlled mode (see open).\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {string} reason Can be: `\"toggleInput\"`, `\"escape\"`, `\"selectOption\"`, `\"removeOption\"`, `\"blur\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * Callback fired when the highlight option changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {Value} option The highlighted option.\n   * @param {string} reason Can be: `\"keyboard\"`, `\"mouse\"`, `\"touch\"`.\n   */\n  onHighlightChange: PropTypes.func,\n  /**\n   * Callback fired when the input value changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {string} value The new value of the text input.\n   * @param {string} reason Can be: `\"input\"` (user input), `\"reset\"` (programmatic change), `\"clear\"`, `\"blur\"`, `\"selectOption\"`, `\"removeOption\"`\n   */\n  onInputChange: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyDown: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be opened.\n   * Use in controlled mode (see open).\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   */\n  onOpen: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * If `true`, the popup will open on input focus.\n   * @default false\n   */\n  openOnFocus: PropTypes.bool,\n  /**\n   * Override the default text for the *open popup* icon button.\n   *\n   * For localization purposes, you can use the provided [translations](https://mui.com/material-ui/guides/localization/).\n   * @default 'Open'\n   */\n  openText: PropTypes.string,\n  /**\n   * A list of options that will be shown in the Autocomplete.\n   */\n  options: PropTypes.array.isRequired,\n  /**\n   * The component used to render the body of the popup.\n   * @default Paper\n   * @deprecated Use `slots.paper` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  PaperComponent: PropTypes.elementType,\n  /**\n   * The component used to position the popup.\n   * @default Popper\n   * @deprecated Use `slots.popper` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  PopperComponent: PropTypes.elementType,\n  /**\n   * The icon to display in place of the default popup icon.\n   * @default <ArrowDropDownIcon />\n   */\n  popupIcon: PropTypes.node,\n  /**\n   * If `true`, the component becomes readonly. It is also supported for multiple tags where the tag cannot be deleted.\n   * @default false\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * Render the group.\n   *\n   * @param {AutocompleteRenderGroupParams} params The group to render.\n   * @returns {ReactNode}\n   */\n  renderGroup: PropTypes.func,\n  /**\n   * Render the input.\n   *\n   * **Note:** The `renderInput` prop must return a `TextField` component or a compatible custom component\n   * that correctly forwards `InputProps.ref` and spreads `inputProps`. This ensures proper integration\n   * with the Autocomplete's internal logic (e.g., focus management and keyboard navigation).\n   *\n   * Avoid using components like `DatePicker` or `Select` directly, as they may not forward the required props,\n   * leading to runtime errors or unexpected behavior.\n   *\n   * @param {object} params\n   * @returns {ReactNode}\n   */\n  renderInput: PropTypes.func.isRequired,\n  /**\n   * Render the option, use `getOptionLabel` by default.\n   *\n   * @param {object} props The props to apply on the li element.\n   * @param {Value} option The option to render.\n   * @param {object} state The state of each option.\n   * @param {object} ownerState The state of the Autocomplete component.\n   * @returns {ReactNode}\n   */\n  renderOption: PropTypes.func,\n  /**\n   * Render the selected value when doing multiple selections.\n   *\n   * @deprecated Use `renderValue` prop instead\n   *\n   * @param {Value[]} value The `value` provided to the component.\n   * @param {function} getTagProps A tag props getter.\n   * @param {object} ownerState The state of the Autocomplete component.\n   * @returns {ReactNode}\n   */\n  renderTags: PropTypes.func,\n  /**\n   * Renders the selected value(s) as rich content in the input for both single and multiple selections.\n   *\n   * @param {AutocompleteRenderValue<Value, Multiple, FreeSolo>} value The `value` provided to the component.\n   * @param {function} getItemProps The value item props.\n   * @param {object} ownerState The state of the Autocomplete component.\n   * @returns {ReactNode}\n   */\n  renderValue: PropTypes.func,\n  /**\n   * If `true`, the input's text is selected on focus.\n   * It helps the user clear the selected value.\n   * @default !props.freeSolo\n   */\n  selectOnFocus: PropTypes.bool,\n  /**\n   * The size of the component.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['small', 'medium']), PropTypes.string]),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    chip: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    clearIndicator: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    listbox: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    paper: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    popper: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    popupIndicator: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    listbox: PropTypes.elementType,\n    paper: PropTypes.elementType,\n    popper: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The value of the autocomplete.\n   *\n   * The value must have reference equality with the option in order to be selected.\n   * You can customize the equality behavior with the `isOptionEqualToValue` prop.\n   */\n  value: chainPropTypes(PropTypes.any, props => {\n    if (props.multiple && props.value !== undefined && !Array.isArray(props.value)) {\n      return new Error(['MUI: The Autocomplete expects the `value` prop to be an array when `multiple={true}` or undefined.', `However, ${props.value} was provided.`].join('\\n'));\n    }\n    return null;\n  })\n} : void 0;\nexport default Autocomplete;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,UAAU,EAAEC,kBAAkB;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,KAAK,QAAQ,8BAA8B;AACpD,OAAOC,eAAe,IAAIC,mBAAmB,QAAQ,6BAA6B;AAClF,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,KAAK,MAAM,mBAAmB;AACrC,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,IAAI,MAAM,kBAAkB;AACnC,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,oBAAoB,MAAM,0CAA0C;AAC3E,OAAOC,kBAAkB,MAAM,sCAAsC;AACrE,OAAOC,SAAS,MAAM,gCAAgC;AACtD,OAAOC,iBAAiB,MAAM,wCAAwC;AACtE,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,mBAAmB,IAAIC,2BAA2B,QAAQ,0BAA0B;AAC3F,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,aAAa;IACbC,QAAQ;IACRC,OAAO;IACPC,SAAS;IACTC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,SAAS;IACTC;EACF,CAAC,GAAGV,UAAU;EACd,MAAMW,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAET,QAAQ,IAAI,UAAU,EAAEC,OAAO,IAAI,SAAS,EAAEC,SAAS,IAAI,WAAW,EAAEC,YAAY,IAAI,cAAc,EAAEC,YAAY,IAAI,cAAc,CAAC;IACtJM,SAAS,EAAE,CAAC,WAAW,CAAC;IACxBC,KAAK,EAAE,CAAC,OAAO,EAAEN,YAAY,IAAI,cAAc,CAAC;IAChDO,GAAG,EAAE,CAAC,KAAK,EAAE,UAAUtB,UAAU,CAACiB,IAAI,CAAC,EAAE,CAAC;IAC1CM,YAAY,EAAE,CAAC,cAAc,CAAC;IAC9BC,cAAc,EAAE,CAAC,gBAAgB,CAAC;IAClCC,cAAc,EAAE,CAAC,gBAAgB,EAAET,SAAS,IAAI,oBAAoB,CAAC;IACrEU,MAAM,EAAE,CAAC,QAAQ,EAAEjB,aAAa,IAAI,qBAAqB,CAAC;IAC1DkB,KAAK,EAAE,CAAC,OAAO,CAAC;IAChBC,OAAO,EAAE,CAAC,SAAS,CAAC;IACpBC,OAAO,EAAE,CAAC,SAAS,CAAC;IACpBC,SAAS,EAAE,CAAC,WAAW,CAAC;IACxBC,MAAM,EAAE,CAAC,QAAQ,CAAC;IAClBC,UAAU,EAAE,CAAC,YAAY,CAAC;IAC1BC,OAAO,EAAE,CAAC,SAAS;EACrB,CAAC;EACD,OAAOrD,cAAc,CAACsC,KAAK,EAAEnB,2BAA2B,EAAES,OAAO,CAAC;AACpE,CAAC;AACD,MAAM0B,gBAAgB,GAAGvC,MAAM,CAAC,KAAK,EAAE;EACrCwC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJhC;IACF,CAAC,GAAG+B,KAAK;IACT,MAAM;MACJ1B,SAAS;MACTC,YAAY;MACZC,YAAY;MACZC,YAAY;MACZE;IACF,CAAC,GAAGV,UAAU;IACd,OAAO,CAAC;MACN,CAAC,MAAMT,mBAAmB,CAACwB,GAAG,EAAE,GAAGiB,MAAM,CAACjB;IAC5C,CAAC,EAAE;MACD,CAAC,MAAMxB,mBAAmB,CAACwB,GAAG,EAAE,GAAGiB,MAAM,CAAC,UAAUvC,UAAU,CAACiB,IAAI,CAAC,EAAE;IACxE,CAAC,EAAE;MACD,CAAC,MAAMnB,mBAAmB,CAACsB,SAAS,EAAE,GAAGmB,MAAM,CAACnB;IAClD,CAAC,EAAE;MACD,CAAC,MAAMtB,mBAAmB,CAACuB,KAAK,EAAE,GAAGkB,MAAM,CAAClB;IAC9C,CAAC,EAAE;MACD,CAAC,MAAMvB,mBAAmB,CAACuB,KAAK,EAAE,GAAGN,YAAY,IAAIwB,MAAM,CAACxB;IAC9D,CAAC,EAAEwB,MAAM,CAACpB,IAAI,EAAEP,SAAS,IAAI2B,MAAM,CAAC3B,SAAS,EAAEE,YAAY,IAAIyB,MAAM,CAACzB,YAAY,EAAED,YAAY,IAAI0B,MAAM,CAAC1B,YAAY,CAAC;EAC1H;AACF,CAAC,CAAC,CAAC;EACD,CAAC,KAAKf,mBAAmB,CAACa,OAAO,KAAKb,mBAAmB,CAAC0B,cAAc,EAAE,GAAG;IAC3EgB,UAAU,EAAE;EACd,CAAC;EACD;EACA,wBAAwB,EAAE;IACxB,CAAC,YAAY1C,mBAAmB,CAAC0B,cAAc,EAAE,GAAG;MAClDgB,UAAU,EAAE;IACd;EACF,CAAC;EACD,CAAC,MAAM1C,mBAAmB,CAACwB,GAAG,EAAE,GAAG;IACjCmB,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE;EACZ,CAAC;EACD,CAAC,MAAM5C,mBAAmB,CAACsB,SAAS,EAAE,GAAG;IACvC,CAAC,IAAItB,mBAAmB,CAACgB,YAAY,OAAOhB,mBAAmB,CAACe,YAAY,GAAG,GAAG;MAChF8B,YAAY,EAAE,EAAE,GAAG;IACrB,CAAC;IACD,CAAC,IAAI7C,mBAAmB,CAACgB,YAAY,IAAIhB,mBAAmB,CAACe,YAAY,GAAG,GAAG;MAC7E8B,YAAY,EAAE,EAAE,GAAG;IACrB,CAAC;IACD,CAAC,MAAM7C,mBAAmB,CAACuB,KAAK,EAAE,GAAG;MACnCuB,KAAK,EAAE,CAAC;MACRC,QAAQ,EAAE;IACZ;EACF,CAAC;EACD,CAAC,MAAMxD,YAAY,CAAC8B,IAAI,EAAE,GAAG;IAC3B2B,aAAa,EAAE,CAAC;IAChB,mBAAmB,EAAE;MACnBC,OAAO,EAAE;IACX;EACF,CAAC;EACD,CAAC,MAAM1D,YAAY,CAAC8B,IAAI,IAAI7B,gBAAgB,CAAC0D,SAAS,EAAE,GAAG;IACzD,CAAC,MAAM3D,YAAY,CAACgC,KAAK,EAAE,GAAG;MAC5B0B,OAAO,EAAE;IACX;EACF,CAAC;EACD,CAAC,MAAMxD,oBAAoB,CAAC4B,IAAI,EAAE,GAAG;IACnC4B,OAAO,EAAE,CAAC;IACV,CAAC,IAAIjD,mBAAmB,CAACgB,YAAY,OAAOhB,mBAAmB,CAACe,YAAY,GAAG,GAAG;MAChF8B,YAAY,EAAE,EAAE,GAAG,CAAC,GAAG;IACzB,CAAC;IACD,CAAC,IAAI7C,mBAAmB,CAACgB,YAAY,IAAIhB,mBAAmB,CAACe,YAAY,GAAG,GAAG;MAC7E8B,YAAY,EAAE,EAAE,GAAG,CAAC,GAAG;IACzB,CAAC;IACD,CAAC,MAAM7C,mBAAmB,CAACuB,KAAK,EAAE,GAAG;MACnC0B,OAAO,EAAE;IACX,CAAC;IACD,CAAC,MAAMjD,mBAAmB,CAACyB,YAAY,EAAE,GAAG;MAC1C0B,KAAK,EAAE;IACT;EACF,CAAC;EACD,CAAC,MAAM1D,oBAAoB,CAAC4B,IAAI,IAAI7B,gBAAgB,CAAC0D,SAAS,EAAE,GAAG;IACjE;IACA;IACAE,UAAU,EAAE,CAAC;IACbJ,aAAa,EAAE,CAAC;IAChBK,WAAW,EAAE,CAAC;IACd,CAAC,MAAMrD,mBAAmB,CAACuB,KAAK,EAAE,GAAG;MACnC0B,OAAO,EAAE;IACX;EACF,CAAC;EACD,CAAC,MAAMvD,kBAAkB,CAAC2B,IAAI,EAAE,GAAG;IACjC+B,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,CAAC;IACd,CAAC,IAAIrD,mBAAmB,CAACgB,YAAY,OAAOhB,mBAAmB,CAACe,YAAY,GAAG,GAAG;MAChF8B,YAAY,EAAE,EAAE,GAAG,CAAC,GAAG;IACzB,CAAC;IACD,CAAC,IAAI7C,mBAAmB,CAACgB,YAAY,IAAIhB,mBAAmB,CAACe,YAAY,GAAG,GAAG;MAC7E8B,YAAY,EAAE,EAAE,GAAG,CAAC,GAAG;IACzB,CAAC;IACD,CAAC,MAAMnD,kBAAkB,CAAC6B,KAAK,EAAE,GAAG;MAClC0B,OAAO,EAAE;IACX,CAAC;IACD,CAAC,MAAMjD,mBAAmB,CAACyB,YAAY,EAAE,GAAG;MAC1C0B,KAAK,EAAE;IACT;EACF,CAAC;EACD,CAAC,MAAMzD,kBAAkB,CAAC2B,IAAI,IAAI7B,gBAAgB,CAAC0D,SAAS,EAAE,GAAG;IAC/DF,aAAa,EAAE,CAAC;IAChB,CAAC,MAAMtD,kBAAkB,CAAC6B,KAAK,EAAE,GAAG;MAClC0B,OAAO,EAAE;IACX;EACF,CAAC;EACD,CAAC,MAAMzD,gBAAgB,CAAC8D,WAAW,EAAE,GAAG;IACtCF,UAAU,EAAE;EACd,CAAC;EACD,CAAC,MAAM1D,kBAAkB,CAAC2B,IAAI,IAAI7B,gBAAgB,CAAC8D,WAAW,EAAE,GAAG;IACjEF,UAAU,EAAE,CAAC;IACbJ,aAAa,EAAE,CAAC;IAChB,CAAC,MAAMhD,mBAAmB,CAACuB,KAAK,EAAE,GAAG;MACnC6B,UAAU,EAAE,EAAE;MACdJ,aAAa,EAAE;IACjB;EACF,CAAC;EACD,CAAC,MAAMtD,kBAAkB,CAAC2B,IAAI,IAAI7B,gBAAgB,CAAC8D,WAAW,IAAI9D,gBAAgB,CAAC0D,SAAS,EAAE,GAAG;IAC/F,CAAC,MAAMlD,mBAAmB,CAACuB,KAAK,EAAE,GAAG;MACnC6B,UAAU,EAAE,CAAC;MACbJ,aAAa,EAAE;IACjB;EACF,CAAC;EACD,CAAC,MAAMhD,mBAAmB,CAACuB,KAAK,EAAE,GAAG;IACnCgC,QAAQ,EAAE,CAAC;IACXC,YAAY,EAAE,UAAU;IACxBC,OAAO,EAAE;EACX,CAAC;EACDC,QAAQ,EAAE,CAAC;IACTlB,KAAK,EAAE;MACL1B,SAAS,EAAE;IACb,CAAC;IACD6C,KAAK,EAAE;MACLb,KAAK,EAAE;IACT;EACF,CAAC,EAAE;IACDN,KAAK,EAAE;MACLrB,IAAI,EAAE;IACR,CAAC;IACDwC,KAAK,EAAE;MACL,CAAC,MAAM3D,mBAAmB,CAACwB,GAAG,EAAE,GAAG;QACjCmB,MAAM,EAAE,CAAC;QACTC,QAAQ,EAAE;MACZ;IACF;EACF,CAAC,EAAE;IACDJ,KAAK,EAAE;MACLvB,YAAY,EAAE;IAChB,CAAC;IACD0C,KAAK,EAAE;MACL,CAAC,MAAM3D,mBAAmB,CAACuB,KAAK,EAAE,GAAG;QACnCkC,OAAO,EAAE;MACX;IACF;EACF,CAAC,EAAE;IACDjB,KAAK,EAAE;MACLoB,QAAQ,EAAE;IACZ,CAAC;IACDD,KAAK,EAAE;MACL,CAAC,MAAM3D,mBAAmB,CAACsB,SAAS,EAAE,GAAG;QACvCuC,QAAQ,EAAE;MACZ;IACF;EACF,CAAC;AACH,CAAC,CAAC;AACF,MAAMC,wBAAwB,GAAGjE,MAAM,CAAC,KAAK,EAAE;EAC7CwC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;EACD;EACAyB,QAAQ,EAAE,UAAU;EACpBZ,KAAK,EAAE,CAAC;EACRa,GAAG,EAAE,KAAK;EACVC,SAAS,EAAE;AACb,CAAC,CAAC;AACF,MAAMC,0BAA0B,GAAGrE,MAAM,CAACR,UAAU,EAAE;EACpDgD,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;EACD6B,WAAW,EAAE,CAAC,CAAC;EACflB,OAAO,EAAE,CAAC;EACVP,UAAU,EAAE;AACd,CAAC,CAAC;AACF,MAAM0B,0BAA0B,GAAGvE,MAAM,CAACR,UAAU,EAAE;EACpDgD,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,gBAAgB;EACtBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJhC;IACF,CAAC,GAAG+B,KAAK;IACT,OAAO,CAACC,MAAM,CAACd,cAAc,EAAElB,UAAU,CAACS,SAAS,IAAIuB,MAAM,CAAC4B,kBAAkB,CAAC;EACnF;AACF,CAAC,CAAC,CAAC;EACDpB,OAAO,EAAE,CAAC;EACVkB,WAAW,EAAE,CAAC,CAAC;EACfT,QAAQ,EAAE,CAAC;IACTlB,KAAK,EAAE;MACLtB,SAAS,EAAE;IACb,CAAC;IACDyC,KAAK,EAAE;MACLM,SAAS,EAAE;IACb;EACF,CAAC;AACH,CAAC,CAAC;AACF,MAAMK,kBAAkB,GAAGzE,MAAM,CAACX,MAAM,EAAE;EACxCmD,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,QAAQ;EACdC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJhC;IACF,CAAC,GAAG+B,KAAK;IACT,OAAO,CAAC;MACN,CAAC,MAAMxC,mBAAmB,CAACiC,MAAM,EAAE,GAAGQ,MAAM,CAACR;IAC/C,CAAC,EAAEQ,MAAM,CAACb,MAAM,EAAEnB,UAAU,CAACE,aAAa,IAAI8B,MAAM,CAAC8B,mBAAmB,CAAC;EAC3E;AACF,CAAC,CAAC,CAACzE,SAAS,CAAC0E,IAAA;EAAA,IAAC;IACZC;EACF,CAAC,GAAAD,IAAA;EAAA,OAAM;IACLE,MAAM,EAAE,CAACD,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEC,MAAM,CAACE,KAAK;IAC1ClB,QAAQ,EAAE,CAAC;MACTlB,KAAK,EAAE;QACL7B,aAAa,EAAE;MACjB,CAAC;MACDgD,KAAK,EAAE;QACLI,QAAQ,EAAE;MACZ;IACF,CAAC;EACH,CAAC;AAAA,CAAC,CAAC,CAAC;AACJ,MAAMc,iBAAiB,GAAGhF,MAAM,CAACT,KAAK,EAAE;EACtCiD,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE;AACR,CAAC,CAAC,CAACxC,SAAS,CAACgF,KAAA;EAAA,IAAC;IACZL;EACF,CAAC,GAAAK,KAAA;EAAA,OAAM;IACL,GAAGL,KAAK,CAACM,UAAU,CAACC,KAAK;IACzBC,QAAQ,EAAE;EACZ,CAAC;AAAA,CAAC,CAAC,CAAC;AACJ,MAAMC,mBAAmB,GAAGrF,MAAM,CAAC,KAAK,EAAE;EACxCwC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE;AACR,CAAC,CAAC,CAACxC,SAAS,CAACqF,KAAA;EAAA,IAAC;IACZV;EACF,CAAC,GAAAU,KAAA;EAAA,OAAM;IACLC,KAAK,EAAE,CAACX,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEY,OAAO,CAACC,IAAI,CAACC,SAAS;IACnDtC,OAAO,EAAE;EACX,CAAC;AAAA,CAAC,CAAC,CAAC;AACJ,MAAMuC,qBAAqB,GAAG3F,MAAM,CAAC,KAAK,EAAE;EAC1CwC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE;AACR,CAAC,CAAC,CAACxC,SAAS,CAAC2F,KAAA;EAAA,IAAC;IACZhB;EACF,CAAC,GAAAgB,KAAA;EAAA,OAAM;IACLL,KAAK,EAAE,CAACX,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEY,OAAO,CAACC,IAAI,CAACC,SAAS;IACnDtC,OAAO,EAAE;EACX,CAAC;AAAA,CAAC,CAAC,CAAC;AACJ,MAAMyC,mBAAmB,GAAG7F,MAAM,CAAC,IAAI,EAAE;EACvCwC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE;AACR,CAAC,CAAC,CAACxC,SAAS,CAAC6F,KAAA;EAAA,IAAC;IACZlB;EACF,CAAC,GAAAkB,KAAA;EAAA,OAAM;IACLC,SAAS,EAAE,MAAM;IACjBjD,MAAM,EAAE,CAAC;IACTM,OAAO,EAAE,OAAO;IAChB4C,SAAS,EAAE,MAAM;IACjBZ,QAAQ,EAAE,MAAM;IAChBlB,QAAQ,EAAE,UAAU;IACpB,CAAC,MAAM/D,mBAAmB,CAACiC,MAAM,EAAE,GAAG;MACpC6D,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE,MAAM;MACfd,QAAQ,EAAE,QAAQ;MAClBe,cAAc,EAAE,YAAY;MAC5BC,UAAU,EAAE,QAAQ;MACpBC,MAAM,EAAE,SAAS;MACjB9C,UAAU,EAAE,CAAC;MACb+C,SAAS,EAAE,YAAY;MACvBC,OAAO,EAAE,GAAG;MACZC,uBAAuB,EAAE,aAAa;MACtCrD,aAAa,EAAE,CAAC;MAChBK,WAAW,EAAE,EAAE;MACfR,YAAY,EAAE,EAAE;MAChB,CAAC4B,KAAK,CAAC6B,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,GAAG;QAC5BT,SAAS,EAAE;MACb,CAAC;MACD,CAAC,KAAK9F,mBAAmB,CAACa,OAAO,EAAE,GAAG;QACpC2F,eAAe,EAAE,CAAC/B,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEY,OAAO,CAACoB,MAAM,CAACC,KAAK;QAC3D;QACA,sBAAsB,EAAE;UACtBF,eAAe,EAAE;QACnB;MACF,CAAC;MACD,yBAAyB,EAAE;QACzB/C,OAAO,EAAE,CAACgB,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEY,OAAO,CAACoB,MAAM,CAACE,eAAe;QAC7DC,aAAa,EAAE;MACjB,CAAC;MACD,CAAC,KAAK5G,mBAAmB,CAAC6G,YAAY,EAAE,GAAG;QACzCL,eAAe,EAAE,CAAC/B,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEY,OAAO,CAACoB,MAAM,CAACK;MACxD,CAAC;MACD,yBAAyB,EAAE;QACzBN,eAAe,EAAE/B,KAAK,CAACE,IAAI,GAAG,QAAQF,KAAK,CAACE,IAAI,CAACU,OAAO,CAAC0B,OAAO,CAACC,WAAW,MAAMvC,KAAK,CAACE,IAAI,CAACU,OAAO,CAACoB,MAAM,CAACQ,eAAe,GAAG,GAAGlI,KAAK,CAAC0F,KAAK,CAACY,OAAO,CAAC0B,OAAO,CAACG,IAAI,EAAEzC,KAAK,CAACY,OAAO,CAACoB,MAAM,CAACQ,eAAe,CAAC;QACxM,CAAC,KAAKjH,mBAAmB,CAACa,OAAO,EAAE,GAAG;UACpC2F,eAAe,EAAE/B,KAAK,CAACE,IAAI,GAAG,QAAQF,KAAK,CAACE,IAAI,CAACU,OAAO,CAAC0B,OAAO,CAACC,WAAW,WAAWvC,KAAK,CAACE,IAAI,CAACU,OAAO,CAACoB,MAAM,CAACQ,eAAe,MAAMxC,KAAK,CAACE,IAAI,CAACU,OAAO,CAACoB,MAAM,CAACU,YAAY,IAAI,GAAGpI,KAAK,CAAC0F,KAAK,CAACY,OAAO,CAAC0B,OAAO,CAACG,IAAI,EAAEzC,KAAK,CAACY,OAAO,CAACoB,MAAM,CAACQ,eAAe,GAAGxC,KAAK,CAACY,OAAO,CAACoB,MAAM,CAACU,YAAY,CAAC;UAC9R;UACA,sBAAsB,EAAE;YACtBX,eAAe,EAAE,CAAC/B,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEY,OAAO,CAACoB,MAAM,CAACW;UACxD;QACF,CAAC;QACD,CAAC,KAAKpH,mBAAmB,CAAC6G,YAAY,EAAE,GAAG;UACzCL,eAAe,EAAE/B,KAAK,CAACE,IAAI,GAAG,QAAQF,KAAK,CAACE,IAAI,CAACU,OAAO,CAAC0B,OAAO,CAACC,WAAW,WAAWvC,KAAK,CAACE,IAAI,CAACU,OAAO,CAACoB,MAAM,CAACQ,eAAe,MAAMxC,KAAK,CAACE,IAAI,CAACU,OAAO,CAACoB,MAAM,CAACY,YAAY,IAAI,GAAGtI,KAAK,CAAC0F,KAAK,CAACY,OAAO,CAAC0B,OAAO,CAACG,IAAI,EAAEzC,KAAK,CAACY,OAAO,CAACoB,MAAM,CAACQ,eAAe,GAAGxC,KAAK,CAACY,OAAO,CAACoB,MAAM,CAACY,YAAY;QAC/R;MACF;IACF;EACF,CAAC;AAAA,CAAC,CAAC,CAAC;AACJ,MAAMC,sBAAsB,GAAGzH,MAAM,CAACV,aAAa,EAAE;EACnDkD,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE;AACR,CAAC,CAAC,CAACxC,SAAS,CAACyH,KAAA;EAAA,IAAC;IACZ9C;EACF,CAAC,GAAA8C,KAAA;EAAA,OAAM;IACLf,eAAe,EAAE,CAAC/B,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEY,OAAO,CAACmC,UAAU,CAAC3F,KAAK;IAC/DmC,GAAG,EAAE,CAAC;EACR,CAAC;AAAA,CAAC,CAAC,CAAC;AACJ,MAAMyD,mBAAmB,GAAG5H,MAAM,CAAC,IAAI,EAAE;EACvCwC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;EACDW,OAAO,EAAE,CAAC;EACV,CAAC,MAAMjD,mBAAmB,CAACiC,MAAM,EAAE,GAAG;IACpCoB,WAAW,EAAE;EACf;AACF,CAAC,CAAC;AACF,SAASpE,mBAAmB;AAC5B,MAAMyI,YAAY,GAAG,aAAajJ,KAAK,CAACkJ,UAAU,CAAC,SAASD,YAAYA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACrF,MAAMrF,KAAK,GAAGzC,eAAe,CAAC;IAC5ByC,KAAK,EAAEoF,OAAO;IACdvF,IAAI,EAAE;EACR,CAAC,CAAC;;EAEF;EACA,MAAM;IACJyF,YAAY,GAAG,KAAK;IACpBC,aAAa,GAAG,KAAK;IACrBC,UAAU,GAAG,KAAK;IAClBC,YAAY,GAAG,KAAK;IACpBC,SAAS,EAAEC,aAAa;IACxBC,SAAS;IACTC,SAAS,GAAG9J,UAAU,KAAKA,UAAU,GAAG,aAAa8B,IAAI,CAACV,SAAS,EAAE;MACnE2I,QAAQ,EAAE;IACZ,CAAC,CAAC,CAAC;IACHC,WAAW,GAAG,CAAC/F,KAAK,CAACgG,QAAQ;IAC7BC,aAAa,GAAG,KAAK;IACrBC,SAAS,GAAG,OAAO;IACnBC,SAAS,GAAG,OAAO;IACnBC,eAAe;IACfC,YAAY,GAAGrG,KAAK,CAACoB,QAAQ,GAAG,EAAE,GAAG,IAAI;IACzCkF,gBAAgB,GAAG,KAAK;IACxBC,oBAAoB,GAAG,KAAK;IAC5BC,QAAQ,GAAG,KAAK;IAChBC,sBAAsB,GAAG,KAAK;IAC9BC,eAAe,GAAG,KAAK;IACvBvI,aAAa,GAAG,KAAK;IACrBwI,aAAa;IACbC,qBAAqB,GAAG,KAAK;IAC7BC,cAAc,GAAG,MAAM;IACvBb,QAAQ,GAAG,KAAK;IAChB1H,SAAS,GAAG,KAAK;IACjBwI,gBAAgB,GAAGC,IAAI,IAAI,IAAIA,IAAI,EAAE;IACrCC,iBAAiB;IACjBC,YAAY;IACZC,cAAc,EAAEC,kBAAkB;IAClCC,oBAAoB;IACpBC,OAAO;IACPC,iBAAiB,GAAG,CAACtH,KAAK,CAACgG,QAAQ;IACnCuB,EAAE,EAAEC,MAAM;IACVC,kBAAkB,GAAG,KAAK;IAC1BC,UAAU,EAAEC,cAAc;IAC1BC,SAAS,GAAG,CAAC,CAAC;IACdC,gBAAgB,EAAEC,oBAAoB;IACtCC,YAAY,EAAEC,gBAAgB;IAC9BzI,OAAO,GAAG,KAAK;IACf0I,WAAW,GAAG,UAAU;IACxB7G,QAAQ,GAAG,KAAK;IAChB8G,aAAa,GAAG,YAAY;IAC5BC,QAAQ;IACRC,OAAO;IACPC,iBAAiB;IACjBC,aAAa;IACbC,MAAM;IACNC,IAAI;IACJC,WAAW,GAAG,KAAK;IACnBC,QAAQ,GAAG,MAAM;IACjBC,OAAO;IACPC,cAAc,EAAEC,kBAAkB;IAClCC,eAAe,EAAEC,mBAAmB;IACpCC,SAAS,GAAGhN,kBAAkB,KAAKA,kBAAkB,GAAG,aAAa6B,IAAI,CAACT,iBAAiB,EAAE,CAAC,CAAC,CAAC,CAAC;IACjG6L,QAAQ,GAAG,KAAK;IAChBC,WAAW,EAAEC,eAAe;IAC5BC,WAAW;IACXC,YAAY,EAAEC,gBAAgB;IAC9BC,UAAU;IACVC,WAAW;IACXC,aAAa,GAAG,CAACzJ,KAAK,CAACgG,QAAQ;IAC/BrH,IAAI,GAAG,QAAQ;IACfC,KAAK,GAAG,CAAC,CAAC;IACV8K,SAAS,GAAG,CAAC,CAAC;IACdC,KAAK,EAAEC,SAAS;IAChB,GAAGC;EACL,CAAC,GAAG7J,KAAK;EACT;;EAEA,MAAM;IACJ8J,YAAY;IACZC,aAAa;IACbC,kBAAkB;IAClBC,sBAAsB;IACtBC,aAAa;IACbC,YAAY;IACZC,eAAe;IACfC,cAAc;IACdV,KAAK;IACLW,KAAK;IACLlM,QAAQ;IACRmJ,EAAE;IACF7I,SAAS;IACTL,OAAO;IACPkM,WAAW;IACXC,QAAQ;IACRC,WAAW;IACX/C,UAAU;IACVgD;EACF,CAAC,GAAGlO,eAAe,CAAC;IAClB,GAAGwD,KAAK;IACR2K,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAMpM,YAAY,GAAG,CAAC+H,gBAAgB,IAAI,CAACE,QAAQ,IAAI8D,KAAK,IAAI,CAACrB,QAAQ;EACzE,MAAMzK,YAAY,GAAG,CAAC,CAACwH,QAAQ,IAAIa,cAAc,KAAK,IAAI,KAAKA,cAAc,KAAK,KAAK;EACvF,MAAM;IACJ+D,WAAW,EAAEC;EACf,CAAC,GAAGd,aAAa,CAAC,CAAC;EACnB,MAAM;IACJ1E,GAAG,EAAEyF,UAAU;IACf,GAAGC;EACL,CAAC,GAAGX,eAAe,CAAC,CAAC;EACrB,MAAMY,qBAAqB,GAAGvL,MAAM,IAAIA,MAAM,CAACwL,KAAK,IAAIxL,MAAM;EAC9D,MAAMyH,cAAc,GAAGC,kBAAkB,IAAI6D,qBAAqB;;EAElE;EACA,MAAM/M,UAAU,GAAG;IACjB,GAAG+B,KAAK;IACR7B,aAAa;IACbC,QAAQ;IACRC,OAAO;IACPC,SAAS;IACT4I,cAAc;IACd3I,YAAY;IACZC,YAAY;IACZC,YAAY,EAAE8L,WAAW,KAAK,CAAC,CAAC;IAChC7L,SAAS;IACTC;EACF,CAAC;EACD,MAAMT,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMiN,sBAAsB,GAAG;IAC7BtM,KAAK,EAAE;MACLS,KAAK,EAAEwJ,kBAAkB;MACzBzJ,MAAM,EAAE2J,mBAAmB;MAC3B,GAAGnK;IACL,CAAC;IACD8K,SAAS,EAAE;MACTyB,IAAI,EAAExF,aAAa;MACnBrG,OAAO,EAAE0I,gBAAgB;MACzB,GAAG5B,eAAe;MAClB,GAAGsD;IACL;EACF,CAAC;EACD,MAAM,CAAC0B,WAAW,EAAEC,YAAY,CAAC,GAAG1N,OAAO,CAAC,SAAS,EAAE;IACrD2N,WAAW,EAAEpI,mBAAmB;IAChCgI,sBAAsB;IACtBjN,UAAU;IACV2H,SAAS,EAAE1H,OAAO,CAACoB,OAAO;IAC1BiM,eAAe,EAAER,iBAAiB;IAClC1F,GAAG,EAAEyF;EACP,CAAC,CAAC;EACF,MAAM,CAACU,SAAS,EAAEC,UAAU,CAAC,GAAG9N,OAAO,CAAC,OAAO,EAAE;IAC/C2N,WAAW,EAAE1O,KAAK;IAClBsO,sBAAsB;IACtBjN,UAAU;IACV2H,SAAS,EAAE1H,OAAO,CAACmB;EACrB,CAAC,CAAC;EACF,MAAM,CAACqM,UAAU,EAAEC,WAAW,CAAC,GAAGhO,OAAO,CAAC,QAAQ,EAAE;IAClD2N,WAAW,EAAE5O,MAAM;IACnBwO,sBAAsB;IACtBjN,UAAU;IACV2H,SAAS,EAAE1H,OAAO,CAACkB,MAAM;IACzBmM,eAAe,EAAE;MACfpN,aAAa;MACbgD,KAAK,EAAE;QACLb,KAAK,EAAEkK,QAAQ,GAAGA,QAAQ,CAACoB,WAAW,GAAG;MAC3C,CAAC;MACDC,IAAI,EAAE,cAAc;MACpBrB,QAAQ;MACRhC,IAAI,EAAE9J;IACR;EACF,CAAC,CAAC;EACF,IAAIoN,cAAc;EAClB,MAAMC,sBAAsB,GAAGC,MAAM,KAAK;IACxCpG,SAAS,EAAE1H,OAAO,CAACc,GAAG;IACtBwH,QAAQ;IACR,GAAG2D,YAAY,CAAC6B,MAAM;EACxB,CAAC,CAAC;EACF,IAAI5K,QAAQ,EAAE;IACZ,IAAIuI,KAAK,CAACsC,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI1C,UAAU,EAAE;QACduC,cAAc,GAAGvC,UAAU,CAACI,KAAK,EAAEoC,sBAAsB,EAAE9N,UAAU,CAAC;MACxE,CAAC,MAAM,IAAIuL,WAAW,EAAE;QACtBsC,cAAc,GAAGtC,WAAW,CAACG,KAAK,EAAEoC,sBAAsB,EAAE9N,UAAU,CAAC;MACzE,CAAC,MAAM;QACL6N,cAAc,GAAGnC,KAAK,CAACuC,GAAG,CAAC,CAACzM,MAAM,EAAE0M,KAAK,KAAK;UAC5C,MAAM;YACJC,GAAG;YACH,GAAGC;UACL,CAAC,GAAGN,sBAAsB,CAAC;YACzBI;UACF,CAAC,CAAC;UACF,OAAO,aAAatO,IAAI,CAACf,IAAI,EAAE;YAC7BmO,KAAK,EAAE/D,cAAc,CAACzH,MAAM,CAAC;YAC7Bd,IAAI,EAAEA,IAAI;YACV,GAAG0N,eAAe;YAClB,GAAGnB,sBAAsB,CAACxB,SAAS,CAACyB;UACtC,CAAC,EAAEiB,GAAG,CAAC;QACT,CAAC,CAAC;MACJ;IACF;EACF,CAAC,MAAM,IAAI5C,WAAW,IAAIG,KAAK,IAAI,IAAI,EAAE;IACvCmC,cAAc,GAAGtC,WAAW,CAACG,KAAK,EAAEoC,sBAAsB,EAAE9N,UAAU,CAAC;EACzE;EACA,IAAI2J,SAAS,GAAG,CAAC,CAAC,IAAI0E,KAAK,CAACC,OAAO,CAACT,cAAc,CAAC,EAAE;IACnD,MAAM/E,IAAI,GAAG+E,cAAc,CAACG,MAAM,GAAGrE,SAAS;IAC9C,IAAI,CAACvJ,OAAO,IAAI0I,IAAI,GAAG,CAAC,EAAE;MACxB+E,cAAc,GAAGA,cAAc,CAACU,MAAM,CAAC,CAAC,EAAE5E,SAAS,CAAC;MACpDkE,cAAc,CAACW,IAAI,CAAC,aAAa5O,IAAI,CAAC,MAAM,EAAE;QAC5C+H,SAAS,EAAE1H,OAAO,CAACc,GAAG;QACtB0N,QAAQ,EAAE5F,gBAAgB,CAACC,IAAI;MACjC,CAAC,EAAE+E,cAAc,CAACG,MAAM,CAAC,CAAC;IAC5B;EACF;EACA,MAAMU,kBAAkB,GAAGX,MAAM,IAAI,aAAajO,KAAK,CAAC,IAAI,EAAE;IAC5D2O,QAAQ,EAAE,CAAC,aAAa7O,IAAI,CAACiH,sBAAsB,EAAE;MACnDc,SAAS,EAAE1H,OAAO,CAACwB,UAAU;MAC7BzB,UAAU,EAAEA,UAAU;MACtB2O,SAAS,EAAE,KAAK;MAChBF,QAAQ,EAAEV,MAAM,CAACa;IACnB,CAAC,CAAC,EAAE,aAAahP,IAAI,CAACoH,mBAAmB,EAAE;MACzCW,SAAS,EAAE1H,OAAO,CAACyB,OAAO;MAC1B1B,UAAU,EAAEA,UAAU;MACtByO,QAAQ,EAAEV,MAAM,CAACU;IACnB,CAAC,CAAC;EACJ,CAAC,EAAEV,MAAM,CAACI,GAAG,CAAC;EACd,MAAMlD,WAAW,GAAGC,eAAe,IAAIwD,kBAAkB;EACzD,MAAMG,mBAAmB,GAAGA,CAACC,MAAM,EAAEtN,MAAM,KAAK;IAC9C;IACA,MAAM;MACJ2M,GAAG;MACH,GAAGY;IACL,CAAC,GAAGD,MAAM;IACV,OAAO,aAAalP,IAAI,CAAC,IAAI,EAAE;MAC7B,GAAGmP,UAAU;MACbN,QAAQ,EAAExF,cAAc,CAACzH,MAAM;IACjC,CAAC,EAAE2M,GAAG,CAAC;EACT,CAAC;EACD,MAAM/C,YAAY,GAAGC,gBAAgB,IAAIwD,mBAAmB;EAC5D,MAAMG,gBAAgB,GAAGA,CAACxN,MAAM,EAAE0M,KAAK,KAAK;IAC1C,MAAMe,WAAW,GAAG7C,cAAc,CAAC;MACjC5K,MAAM;MACN0M;IACF,CAAC,CAAC;IACF,OAAO9C,YAAY,CAAC;MAClB,GAAG6D,WAAW;MACdtH,SAAS,EAAE1H,OAAO,CAACuB;IACrB,CAAC,EAAEA,MAAM,EAAE;MACTmF,QAAQ,EAAEsI,WAAW,CAAC,eAAe,CAAC;MACtCf,KAAK;MACLzE;IACF,CAAC,EAAEzJ,UAAU,CAAC;EAChB,CAAC;EACD,MAAMkP,uBAAuB,GAAGjC,sBAAsB,CAACxB,SAAS,CAACxK,cAAc;EAC/E,MAAMkO,uBAAuB,GAAGlC,sBAAsB,CAACxB,SAAS,CAACvK,cAAc;EAC/E,OAAO,aAAapB,KAAK,CAAC9B,KAAK,CAACoR,QAAQ,EAAE;IACxCX,QAAQ,EAAE,CAAC,aAAa7O,IAAI,CAAC+B,gBAAgB,EAAE;MAC7CyF,GAAG,EAAEA,GAAG;MACRO,SAAS,EAAEzJ,IAAI,CAAC+B,OAAO,CAACW,IAAI,EAAE+G,SAAS,CAAC;MACxC3H,UAAU,EAAEA,UAAU;MACtB,GAAG6L,YAAY,CAACD,KAAK,CAAC;MACtB6C,QAAQ,EAAEtD,WAAW,CAAC;QACpB7B,EAAE;QACFf,QAAQ;QACRlI,SAAS,EAAE,IAAI;QACfK,IAAI,EAAEA,IAAI,KAAK,OAAO,GAAG,OAAO,GAAG2O,SAAS;QAC5CC,eAAe,EAAEvD,kBAAkB,CAAC,CAAC;QACrCwD,UAAU,EAAE;UACVnI,GAAG,EAAEoF,WAAW;UAChB7E,SAAS,EAAE1H,OAAO,CAACY,SAAS;UAC5BgN,cAAc;UACdlB,WAAW,EAAE6C,KAAK,IAAI;YACpB,IAAIA,KAAK,CAACC,MAAM,KAAKD,KAAK,CAACE,aAAa,EAAE;cACxC9C,oBAAoB,CAAC4C,KAAK,CAAC;YAC7B;UACF,CAAC;UACD,IAAI,CAAClP,YAAY,IAAIC,YAAY,KAAK;YACpCS,YAAY,EAAE,aAAalB,KAAK,CAACuD,wBAAwB,EAAE;cACzDsE,SAAS,EAAE1H,OAAO,CAACe,YAAY;cAC/BhB,UAAU,EAAEA,UAAU;cACtByO,QAAQ,EAAE,CAACnO,YAAY,GAAG,aAAaV,IAAI,CAAC6D,0BAA0B,EAAE;gBACtE,GAAGwI,aAAa,CAAC,CAAC;gBAClB,YAAY,EAAEhE,SAAS;gBACvB0H,KAAK,EAAE1H,SAAS;gBAChBjI,UAAU,EAAEA,UAAU;gBACtB,GAAGkP,uBAAuB;gBAC1BvH,SAAS,EAAEzJ,IAAI,CAAC+B,OAAO,CAACgB,cAAc,EAAEiO,uBAAuB,EAAEvH,SAAS,CAAC;gBAC3E8G,QAAQ,EAAE7G;cACZ,CAAC,CAAC,GAAG,IAAI,EAAErH,YAAY,GAAG,aAAaX,IAAI,CAAC+D,0BAA0B,EAAE;gBACtE,GAAGqI,sBAAsB,CAAC,CAAC;gBAC3BzD,QAAQ,EAAEA,QAAQ;gBAClB,YAAY,EAAE9H,SAAS,GAAGyH,SAAS,GAAGuC,QAAQ;gBAC9CkF,KAAK,EAAElP,SAAS,GAAGyH,SAAS,GAAGuC,QAAQ;gBACvCzK,UAAU,EAAEA,UAAU;gBACtB,GAAGmP,uBAAuB;gBAC1BxH,SAAS,EAAEzJ,IAAI,CAAC+B,OAAO,CAACiB,cAAc,EAAEiO,uBAAuB,EAAExH,SAAS,CAAC;gBAC3E8G,QAAQ,EAAE1D;cACZ,CAAC,CAAC,GAAG,IAAI;YACX,CAAC;UACH,CAAC;QACH,CAAC;QACD6E,UAAU,EAAE;UACVjI,SAAS,EAAE1H,OAAO,CAACa,KAAK;UACxByH,QAAQ;UACRyC,QAAQ;UACR,GAAGc,aAAa,CAAC;QACnB;MACF,CAAC;IACH,CAAC,CAAC,EAAES,QAAQ,GAAG,aAAa3M,IAAI,CAACiE,kBAAkB,EAAE;MACnDgM,EAAE,EAAEpC,UAAU;MACd,GAAGC,WAAW;MACde,QAAQ,EAAE,aAAa3O,KAAK,CAACsE,iBAAiB,EAAE;QAC9CyL,EAAE,EAAEtC,SAAS;QACb,GAAGC,UAAU;QACbiB,QAAQ,EAAE,CAACnN,OAAO,IAAImL,cAAc,CAACuB,MAAM,KAAK,CAAC,GAAG,aAAapO,IAAI,CAAC6E,mBAAmB,EAAE;UACzFkD,SAAS,EAAE1H,OAAO,CAACqB,OAAO;UAC1BtB,UAAU,EAAEA,UAAU;UACtByO,QAAQ,EAAEzE;QACZ,CAAC,CAAC,GAAG,IAAI,EAAEyC,cAAc,CAACuB,MAAM,KAAK,CAAC,IAAI,CAACjG,QAAQ,IAAI,CAACzG,OAAO,GAAG,aAAa1B,IAAI,CAACmF,qBAAqB,EAAE;UACzG4C,SAAS,EAAE1H,OAAO,CAACsB,SAAS;UAC5BvB,UAAU,EAAEA,UAAU;UACtB4N,IAAI,EAAE,cAAc;UACpBjB,WAAW,EAAE6C,KAAK,IAAI;YACpB;YACAA,KAAK,CAACM,cAAc,CAAC,CAAC;UACxB,CAAC;UACDrB,QAAQ,EAAExE;QACZ,CAAC,CAAC,GAAG,IAAI,EAAEwC,cAAc,CAACuB,MAAM,GAAG,CAAC,GAAG,aAAapO,IAAI,CAACuN,WAAW,EAAE;UACpE0C,EAAE,EAAEhG,oBAAoB;UACxB,GAAGuD,YAAY;UACfqB,QAAQ,EAAEhC,cAAc,CAACwB,GAAG,CAAC,CAACzM,MAAM,EAAE0M,KAAK,KAAK;YAC9C,IAAI9E,OAAO,EAAE;cACX,OAAO6B,WAAW,CAAC;gBACjBkD,GAAG,EAAE3M,MAAM,CAAC2M,GAAG;gBACfS,KAAK,EAAEpN,MAAM,CAACoN,KAAK;gBACnBH,QAAQ,EAAEjN,MAAM,CAACkJ,OAAO,CAACuD,GAAG,CAAC,CAAC8B,OAAO,EAAEC,MAAM,KAAKhB,gBAAgB,CAACe,OAAO,EAAEvO,MAAM,CAAC0M,KAAK,GAAG8B,MAAM,CAAC;cACpG,CAAC,CAAC;YACJ;YACA,OAAOhB,gBAAgB,CAACxN,MAAM,EAAE0M,KAAK,CAAC;UACxC,CAAC;QACH,CAAC,CAAC,GAAG,IAAI;MACX,CAAC;IACH,CAAC,CAAC,GAAG,IAAI;EACX,CAAC,CAAC;AACJ,CAAC,CAAC;AACF+B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGlJ,YAAY,CAACmJ,SAAS,CAAC,yBAAyB;EACtF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;AACA;EACE/I,YAAY,EAAEpJ,SAAS,CAACoS,IAAI;EAC5B;AACF;AACA;AACA;EACE/I,aAAa,EAAErJ,SAAS,CAACoS,IAAI;EAC7B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE9I,UAAU,EAAEtJ,SAAS,CAACoS,IAAI;EAC1B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE7I,YAAY,EAAEvJ,SAAS,CAACqS,SAAS,CAAC,CAACrS,SAAS,CAACsS,KAAK,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,EAAEtS,SAAS,CAACoS,IAAI,CAAC,CAAC;EACxF;AACF;AACA;AACA;EACE5I,SAAS,EAAExJ,SAAS,CAACuS,MAAM;EAC3B;AACF;AACA;EACEvQ,OAAO,EAAEhC,SAAS,CAACuS,MAAM;EACzB;AACF;AACA;EACE7I,SAAS,EAAE1J,SAAS,CAACwS,MAAM;EAC3B;AACF;AACA;AACA;EACE7I,SAAS,EAAE3J,SAAS,CAACyS,IAAI;EACzB;AACF;AACA;AACA;AACA;AACA;AACA;EACE5I,WAAW,EAAE7J,SAAS,CAACoS,IAAI;EAC3B;AACF;AACA;AACA;EACErI,aAAa,EAAE/J,SAAS,CAACoS,IAAI;EAC7B;AACF;AACA;AACA;AACA;AACA;EACEpI,SAAS,EAAEhK,SAAS,CAACwS,MAAM;EAC3B;AACF;AACA;AACA;AACA;AACA;EACEvI,SAAS,EAAEjK,SAAS,CAACwS,MAAM;EAC3B;AACF;AACA;AACA;EACEtI,eAAe,EAAElK,SAAS,CAAC0S,KAAK,CAAC;IAC/B1P,cAAc,EAAEhD,SAAS,CAACuS,MAAM;IAChCpP,KAAK,EAAEnD,SAAS,CAACuS,MAAM;IACvBrP,MAAM,EAAElD,SAAS,CAACuS,MAAM;IACxBtP,cAAc,EAAEjD,SAAS,CAACuS;EAC5B,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEpI,YAAY,EAAEhK,cAAc,CAACH,SAAS,CAAC2S,GAAG,EAAE7O,KAAK,IAAI;IACnD,IAAIA,KAAK,CAACoB,QAAQ,IAAIpB,KAAK,CAACqG,YAAY,KAAKiH,SAAS,IAAI,CAAChB,KAAK,CAACC,OAAO,CAACvM,KAAK,CAACqG,YAAY,CAAC,EAAE;MAC5F,OAAO,IAAIyI,KAAK,CAAC,CAAC,2GAA2G,EAAE,YAAY9O,KAAK,CAACqG,YAAY,gBAAgB,CAAC,CAAC0I,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5L;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEzI,gBAAgB,EAAEpK,SAAS,CAACoS,IAAI;EAChC;AACF;AACA;AACA;EACE/H,oBAAoB,EAAErK,SAAS,CAACoS,IAAI;EACpC;AACF;AACA;AACA;EACE9H,QAAQ,EAAEtK,SAAS,CAACoS,IAAI;EACxB;AACF;AACA;AACA;EACE7H,sBAAsB,EAAEvK,SAAS,CAACoS,IAAI;EACtC;AACF;AACA;AACA;EACE5H,eAAe,EAAExK,SAAS,CAACoS,IAAI;EAC/B;AACF;AACA;AACA;EACEnQ,aAAa,EAAEjC,SAAS,CAACoS,IAAI;EAC7B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE3H,aAAa,EAAEzK,SAAS,CAAC8S,IAAI;EAC7B;AACF;AACA;AACA;EACEpI,qBAAqB,EAAE1K,SAAS,CAACoS,IAAI;EACrC;AACF;AACA;AACA;EACEzH,cAAc,EAAE3K,SAAS,CAACqS,SAAS,CAAC,CAACrS,SAAS,CAACsS,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,EAAEtS,SAAS,CAACoS,IAAI,CAAC,CAAC;EAChF;AACF;AACA;AACA;EACEtI,QAAQ,EAAE9J,SAAS,CAACoS,IAAI;EACxB;AACF;AACA;AACA;EACEhQ,SAAS,EAAEpC,SAAS,CAACoS,IAAI;EACzB;AACF;AACA;AACA;AACA;AACA;AACA;EACExH,gBAAgB,EAAE5K,SAAS,CAAC8S,IAAI;EAChC;AACF;AACA;AACA;AACA;AACA;EACEhI,iBAAiB,EAAE9K,SAAS,CAAC8S,IAAI;EACjC;AACF;AACA;AACA;AACA;AACA;AACA;EACE/H,YAAY,EAAE/K,SAAS,CAAC8S,IAAI;EAC5B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE9H,cAAc,EAAEhL,SAAS,CAAC8S,IAAI;EAC9B;AACF;AACA;AACA;AACA;AACA;AACA;EACE3H,OAAO,EAAEnL,SAAS,CAAC8S,IAAI;EACvB;AACF;AACA;AACA;AACA;EACE1H,iBAAiB,EAAEpL,SAAS,CAACoS,IAAI;EACjC;AACF;AACA;AACA;EACE/G,EAAE,EAAErL,SAAS,CAACwS,MAAM;EACpB;AACF;AACA;AACA;EACEjH,kBAAkB,EAAEvL,SAAS,CAACoS,IAAI;EAClC;AACF;AACA;EACE5G,UAAU,EAAExL,SAAS,CAACwS,MAAM;EAC5B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEtH,oBAAoB,EAAElL,SAAS,CAAC8S,IAAI;EACpC;AACF;AACA;AACA;AACA;EACEpH,SAAS,EAAExL,eAAe;EAC1B;AACF;AACA;AACA;AACA;EACEyL,gBAAgB,EAAE3L,SAAS,CAACoP,WAAW;EACvC;AACF;AACA;AACA;EACEvD,YAAY,EAAE7L,SAAS,CAACuS,MAAM;EAC9B;AACF;AACA;AACA;AACA;EACElP,OAAO,EAAErD,SAAS,CAACoS,IAAI;EACvB;AACF;AACA;AACA;AACA;AACA;EACErG,WAAW,EAAE/L,SAAS,CAACyS,IAAI;EAC3B;AACF;AACA;AACA;EACEvN,QAAQ,EAAElF,SAAS,CAACoS,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;EACEpG,aAAa,EAAEhM,SAAS,CAACyS,IAAI;EAC7B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACExG,QAAQ,EAAEjM,SAAS,CAAC8S,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;AACA;EACE5G,OAAO,EAAElM,SAAS,CAAC8S,IAAI;EACvB;AACF;AACA;AACA;AACA;AACA;AACA;EACE3G,iBAAiB,EAAEnM,SAAS,CAAC8S,IAAI;EACjC;AACF;AACA;AACA;AACA;AACA;AACA;EACE1G,aAAa,EAAEpM,SAAS,CAAC8S,IAAI;EAC7B;AACF;AACA;EACEC,SAAS,EAAE/S,SAAS,CAAC8S,IAAI;EACzB;AACF;AACA;AACA;AACA;AACA;EACEzG,MAAM,EAAErM,SAAS,CAAC8S,IAAI;EACtB;AACF;AACA;EACExG,IAAI,EAAEtM,SAAS,CAACoS,IAAI;EACpB;AACF;AACA;AACA;EACE7F,WAAW,EAAEvM,SAAS,CAACoS,IAAI;EAC3B;AACF;AACA;AACA;AACA;AACA;EACE5F,QAAQ,EAAExM,SAAS,CAACwS,MAAM;EAC1B;AACF;AACA;EACE/F,OAAO,EAAEzM,SAAS,CAACgT,KAAK,CAACC,UAAU;EACnC;AACF;AACA;AACA;AACA;EACEvG,cAAc,EAAE1M,SAAS,CAACoP,WAAW;EACrC;AACF;AACA;AACA;AACA;EACExC,eAAe,EAAE5M,SAAS,CAACoP,WAAW;EACtC;AACF;AACA;AACA;EACEtC,SAAS,EAAE9M,SAAS,CAACyS,IAAI;EACzB;AACF;AACA;AACA;EACE1F,QAAQ,EAAE/M,SAAS,CAACoS,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;EACEpF,WAAW,EAAEhN,SAAS,CAAC8S,IAAI;EAC3B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE5F,WAAW,EAAElN,SAAS,CAAC8S,IAAI,CAACG,UAAU;EACtC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE9F,YAAY,EAAEnN,SAAS,CAAC8S,IAAI;EAC5B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEzF,UAAU,EAAErN,SAAS,CAAC8S,IAAI;EAC1B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACExF,WAAW,EAAEtN,SAAS,CAAC8S,IAAI;EAC3B;AACF;AACA;AACA;AACA;EACEvF,aAAa,EAAEvN,SAAS,CAACoS,IAAI;EAC7B;AACF;AACA;AACA;EACE3P,IAAI,EAAEzC,SAAS,CAAC,sCAAsCqS,SAAS,CAAC,CAACrS,SAAS,CAACsS,KAAK,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,EAAEtS,SAAS,CAACwS,MAAM,CAAC,CAAC;EACzH;AACF;AACA;AACA;EACEhF,SAAS,EAAExN,SAAS,CAAC,sCAAsC0S,KAAK,CAAC;IAC/DzD,IAAI,EAAEjP,SAAS,CAACqS,SAAS,CAAC,CAACrS,SAAS,CAAC8S,IAAI,EAAE9S,SAAS,CAACuS,MAAM,CAAC,CAAC;IAC7DvP,cAAc,EAAEhD,SAAS,CAACqS,SAAS,CAAC,CAACrS,SAAS,CAAC8S,IAAI,EAAE9S,SAAS,CAACuS,MAAM,CAAC,CAAC;IACvEnP,OAAO,EAAEpD,SAAS,CAACqS,SAAS,CAAC,CAACrS,SAAS,CAAC8S,IAAI,EAAE9S,SAAS,CAACuS,MAAM,CAAC,CAAC;IAChEpP,KAAK,EAAEnD,SAAS,CAACqS,SAAS,CAAC,CAACrS,SAAS,CAAC8S,IAAI,EAAE9S,SAAS,CAACuS,MAAM,CAAC,CAAC;IAC9DrP,MAAM,EAAElD,SAAS,CAACqS,SAAS,CAAC,CAACrS,SAAS,CAAC8S,IAAI,EAAE9S,SAAS,CAACuS,MAAM,CAAC,CAAC;IAC/DtP,cAAc,EAAEjD,SAAS,CAACqS,SAAS,CAAC,CAACrS,SAAS,CAAC8S,IAAI,EAAE9S,SAAS,CAACuS,MAAM,CAAC;EACxE,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE7P,KAAK,EAAE1C,SAAS,CAAC0S,KAAK,CAAC;IACrBtP,OAAO,EAAEpD,SAAS,CAACoP,WAAW;IAC9BjM,KAAK,EAAEnD,SAAS,CAACoP,WAAW;IAC5BlM,MAAM,EAAElD,SAAS,CAACoP;EACpB,CAAC,CAAC;EACF;AACF;AACA;EACE8D,EAAE,EAAElT,SAAS,CAACqS,SAAS,CAAC,CAACrS,SAAS,CAACmT,OAAO,CAACnT,SAAS,CAACqS,SAAS,CAAC,CAACrS,SAAS,CAAC8S,IAAI,EAAE9S,SAAS,CAACuS,MAAM,EAAEvS,SAAS,CAACoS,IAAI,CAAC,CAAC,CAAC,EAAEpS,SAAS,CAAC8S,IAAI,EAAE9S,SAAS,CAACuS,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;AACA;EACE9E,KAAK,EAAEtN,cAAc,CAACH,SAAS,CAAC2S,GAAG,EAAE7O,KAAK,IAAI;IAC5C,IAAIA,KAAK,CAACoB,QAAQ,IAAIpB,KAAK,CAAC2J,KAAK,KAAK2D,SAAS,IAAI,CAAChB,KAAK,CAACC,OAAO,CAACvM,KAAK,CAAC2J,KAAK,CAAC,EAAE;MAC9E,OAAO,IAAImF,KAAK,CAAC,CAAC,oGAAoG,EAAE,YAAY9O,KAAK,CAAC2J,KAAK,gBAAgB,CAAC,CAACoF,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9K;IACA,OAAO,IAAI;EACb,CAAC;AACH,CAAC,GAAG,KAAK,CAAC;AACV,eAAe7J,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}