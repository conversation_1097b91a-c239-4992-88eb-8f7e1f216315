<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the credit_packages table as it's no longer needed
        // The wallet system no longer uses credit packages
        Schema::dropIfExists('credit_packages');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Recreate the credit_packages table if needed for rollback
        Schema::create('credit_packages', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->decimal('price', 10, 2);
            $table->integer('credit_amount');
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->json('features')->nullable();
            $table->timestamps();

            $table->index(['is_active', 'sort_order'], 'credit_packages_is_active_sort_index');
            $table->index('price', 'credit_packages_price_index');
        });
    }
};
