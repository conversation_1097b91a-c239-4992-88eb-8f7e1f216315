<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class GeneralSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'description',
        'is_encrypted',
    ];

    protected function casts(): array
    {
        return [
            'is_encrypted' => 'boolean',
        ];
    }

    /**
     * Get the value attribute with automatic decryption if needed
     */
    public function getValueAttribute($value)
    {
        if ($this->is_encrypted && $value) {
            try {
                return Crypt::decryptString($value);
            } catch (\Exception $e) {
                return $value;
            }
        }

        // Cast value based on type
        return match ($this->type) {
            'boolean' => (bool) $value,
            'integer' => (int) $value,
            'decimal' => (float) $value,
            'json' => $this->decodeJsonValue($value),
            default => $value,
        };
    }

    /**
     * Set the value attribute with automatic encryption if needed
     */
    public function setValueAttribute($value)
    {
        if ($this->is_encrypted && $value) {
            $this->attributes['value'] = Crypt::encryptString($value);
        } else {
            // Handle different data types
            $this->attributes['value'] = match ($this->type) {
                'json' => is_string($value) ? $value : json_encode($value),
                'boolean' => $value ? '1' : '0',
                default => (string) $value,
            };
        }
    }

    /**
     * Decode JSON value safely
     */
    private function decodeJsonValue($value)
    {
        if (is_null($value) || $value === '') {
            return null;
        }

        $decoded = json_decode($value, true);
        return json_last_error() === JSON_ERROR_NONE ? $decoded : $value;
    }

    /**
     * Get a setting value by key
     */
    public static function get(string $key, $default = null)
    {
        $setting = static::where('key', $key)->first();

        if (!$setting) {
            return $default;
        }

        // Get the raw value from database to avoid the accessor
        $rawValue = $setting->getAttributes()['value'] ?? null;

        // Only return default if value is actually null or empty string
        // Don't return default for '0' which is valid for boolean false
        if ($rawValue === null || $rawValue === '') {
            return $default;
        }

        // Handle decryption if needed
        if ($setting->is_encrypted) {
            try {
                $decryptedValue = Crypt::decryptString($rawValue);
            } catch (\Exception $e) {
                $decryptedValue = $rawValue;
            }
        } else {
            $decryptedValue = $rawValue;
        }

        // Cast value based on type
        return match ($setting->type) {
            'boolean' => (bool) $decryptedValue,
            'integer' => (int) $decryptedValue,
            'decimal' => (float) $decryptedValue,
            'json' => $setting->decodeJsonValue($decryptedValue),
            default => $decryptedValue,
        };
    }

    /**
     * Get a typed setting value (ensures proper type casting)
     */
    public static function getTyped(string $key, $default = null)
    {
        return static::get($key, $default);
    }

    /**
     * Set a setting value by key
     */
    public static function set(string $key, $value, string $type = 'string', bool $isEncrypted = false): self
    {
        // Auto-detect type if not explicitly set and value is an array
        if ($type === 'string' && (is_array($value) || is_object($value))) {
            $type = 'json';
        }

        return static::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'type' => $type,
                'is_encrypted' => $isEncrypted,
            ]
        );
    }

    /**
     * Get default general settings
     */
    public static function getDefaults(): array
    {
        return [
            'site_name' => 'My Website',
            'site_meta_title' => 'My Website - Welcome',
            'site_meta_description' => 'Welcome to our website. We provide excellent services and products.',
            'site_meta_keywords' => 'website, services, products, business',
            'site_logo' => null,
            'site_favicon' => null,
        ];
    }
}
