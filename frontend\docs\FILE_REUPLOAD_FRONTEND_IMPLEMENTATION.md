# File Re-upload Frontend Implementation Guide

## Overview

This document provides a comprehensive guide for the frontend implementation of the file re-upload feature in the My Orders page. The implementation includes React/TypeScript components that integrate with the backend API to provide a seamless file re-upload experience.

## Components Implemented

### 1. FileReUpload Component (`/src/components/dashboard/FileReUpload.tsx`)

**Purpose**: Handles the file re-upload process with drag-and-drop functionality, reason input, and progress tracking.

**Key Features**:
- Drag-and-drop file selection with visual feedback
- File validation and preview
- Required reason input field
- Upload progress indicator
- Error handling and user feedback
- File type restrictions matching backend validation

**Props**:
```typescript
interface FileReUploadProps {
  open: boolean;
  onClose: () => void;
  orderId: number;
  file: OrderFile;
  onSuccess: (updatedFile: OrderFile) => void;
  onError: (error: string) => void;
}
```

**Usage Example**:
```tsx
<FileReUpload
  open={reUploadDialogOpen}
  onClose={() => setReUploadDialogOpen(false)}
  orderId={selectedOrder.id}
  file={selectedFile}
  onSuccess={handleReUploadSuccess}
  onError={handleReUploadError}
/>
```

### 2. FileHistory Component (`/src/components/dashboard/FileHistory.tsx`)

**Purpose**: Displays the complete audit trail and version history for a file.

**Key Features**:
- Timeline-based history display
- Action type icons and color coding
- File metadata comparison (before/after)
- Re-upload reasons and notes
- User attribution for all actions
- Responsive design with loading states

**Props**:
```typescript
interface FileHistoryProps {
  open: boolean;
  onClose: () => void;
  orderId: number;
  file: OrderFile;
}
```

### 3. Toast Component (`/src/components/common/Toast.tsx`)

**Purpose**: Provides non-intrusive notifications for user feedback.

**Features**:
- Configurable severity levels (success, error, warning, info)
- Auto-hide functionality
- Positioned at top-right for better UX
- Material-UI Alert integration

## Updated Components

### 1. Orders.tsx Enhancements

**File Display Section Updates**:
- Added version indicators for each file
- "Current" badge for active file versions
- Re-upload date display for modified files
- Action buttons (History, Re-upload) with tooltips
- Status-based visibility for re-upload options

**New State Management**:
```typescript
// File re-upload states
const [reUploadDialogOpen, setReUploadDialogOpen] = useState(false);
const [selectedFile, setSelectedFile] = useState<OrderFile | null>(null);
const [fileHistoryDialogOpen, setFileHistoryDialogOpen] = useState(false);
const [toastOpen, setToastOpen] = useState(false);
const [toastMessage, setToastMessage] = useState('');
const [toastSeverity, setToastSeverity] = useState<'success' | 'error' | 'warning' | 'info'>('success');
```

**New Helper Functions**:
- `canReUploadFiles(order)`: Checks if order status allows re-uploads
- `handleReUploadFile(file)`: Opens re-upload dialog
- `handleViewFileHistory(file)`: Opens history dialog
- `handleReUploadSuccess(updatedFile)`: Handles successful re-upload
- `handleReUploadError(errorMessage)`: Handles re-upload errors

### 2. PrintingService API Extensions

**New API Methods**:
```typescript
// File re-upload
async reUploadFile(orderId: number, fileId: number, file: File, reason?: string): Promise<FileReUploadResponse>

// Get file history
async getFileHistory(orderId: number, fileId: number): Promise<OrderFileHistory[]>

// Check re-upload permissions
async checkReUploadPermissions(orderId: number, fileId: number): Promise<FileReUploadPermissions>
```

**Updated TypeScript Interfaces**:
```typescript
export interface OrderFile {
  // ... existing fields
  previous_file_id?: number;
  re_upload_reason?: string;
  re_uploaded_at?: string;
  version: number;
  is_current_version: boolean;
}

export interface OrderFileHistory {
  id: number;
  action_type: 'uploaded' | 're_uploaded' | 'deleted' | 'approved' | 'rejected';
  action_type_label: string;
  // ... complete audit trail fields
}
```

## User Interface Features

### 1. Status-Based Visibility

**Allowed Order Statuses for Re-upload**:
- `pending`: Full re-upload access
- `confirmed`: Full re-upload access
- `in_production`: Full re-upload access
- `quality_check`: Full re-upload access

**Restricted Statuses**:
- `completed`: No re-upload (unless admin)
- `shipped`: No re-upload (unless admin)
- `delivered`: No re-upload (unless admin)
- `cancelled`: No re-upload

**Visual Indicators**:
- "Re-upload Available" chip in file section header
- Re-upload button only visible for eligible orders
- Disabled states with explanatory tooltips

### 2. File Version Management

**Version Display**:
- Version number badges (v1, v2, v3, etc.)
- "Current" indicator for active version
- Re-upload date display
- File size and type information

**History Timeline**:
- Chronological action display
- Before/after file comparisons
- User attribution for all actions
- Reason documentation for re-uploads

### 3. User Experience Enhancements

**Drag-and-Drop Interface**:
- Visual feedback during drag operations
- File type validation with clear error messages
- File preview before upload
- Progress indicators during upload

**Error Handling**:
- Comprehensive validation messages
- Network error handling
- Permission-based error messages
- Graceful failure recovery

**Notifications**:
- Success toast for completed re-uploads
- Error toast for failed operations
- Auto-dismissing alerts
- Non-intrusive positioning

## Integration Points

### 1. API Integration

**Endpoint Usage**:
```typescript
// Re-upload file
POST /api/orders/{orderId}/files/{fileId}/reupload
Content-Type: multipart/form-data
Body: { file: File, reason?: string }

// Get file history (future enhancement)
GET /api/orders/{orderId}/files/{fileId}/history

// Check permissions (future enhancement)
GET /api/orders/{orderId}/files/{fileId}/reupload-permissions
```

### 2. State Management

**Order State Updates**:
- Real-time file list updates after re-upload
- Version number increments
- Status change reflections
- History synchronization

**Error State Handling**:
- Network connectivity issues
- File validation failures
- Permission denied scenarios
- Server-side errors

## Security Considerations

### 1. Client-Side Validation

**File Validation**:
- File type restrictions
- File size limits
- Required reason validation
- Order status verification

**Permission Checks**:
- User ownership validation
- Order status restrictions
- File version verification

### 2. Error Information

**Secure Error Messages**:
- No sensitive information exposure
- User-friendly error descriptions
- Appropriate error codes
- Logging for debugging

## Testing Recommendations

### 1. Component Testing

**FileReUpload Component**:
```typescript
// Test file selection
// Test reason validation
// Test upload progress
// Test error handling
// Test success callback
```

**FileHistory Component**:
```typescript
// Test history loading
// Test timeline display
// Test action type rendering
// Test error states
```

### 2. Integration Testing

**Orders Page Integration**:
```typescript
// Test re-upload button visibility
// Test dialog opening/closing
// Test state updates after re-upload
// Test error handling
```

### 3. User Experience Testing

**Workflow Testing**:
- Complete re-upload workflow
- Error scenario handling
- Permission-based restrictions
- Mobile responsiveness

## Performance Considerations

### 1. File Upload Optimization

**Progress Tracking**:
- Simulated progress for better UX
- Real upload progress when available
- Cancellation support (future enhancement)

**Memory Management**:
- File object cleanup after upload
- Component unmounting cleanup
- Event listener removal

### 2. State Management

**Efficient Updates**:
- Minimal re-renders during upload
- Optimized file list updates
- Debounced user interactions

## Future Enhancements

### 1. Advanced Features

**Bulk Re-upload**:
- Multiple file selection
- Batch processing
- Progress aggregation

**File Comparison**:
- Side-by-side file comparison
- Difference highlighting
- Preview generation

### 2. User Experience

**Enhanced Notifications**:
- Email notification preferences
- In-app notification center
- Real-time status updates

**Advanced History**:
- Filterable history view
- Export history functionality
- Advanced search capabilities

## Troubleshooting

### Common Issues

1. **Re-upload button not visible**
   - Check order status
   - Verify user permissions
   - Confirm file is current version

2. **Upload fails with validation error**
   - Check file type restrictions
   - Verify file size limits
   - Ensure reason is provided

3. **History not loading**
   - Check network connectivity
   - Verify API endpoint availability
   - Check user permissions

### Debug Information

**Console Logging**:
- API request/response logging
- State change tracking
- Error condition logging
- Performance metrics

This implementation provides a comprehensive file re-upload system that maintains security, provides excellent user experience, and integrates seamlessly with the existing order management system.
