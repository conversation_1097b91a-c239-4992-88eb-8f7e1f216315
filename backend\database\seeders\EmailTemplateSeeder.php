<?php

namespace Database\Seeders;

use App\Models\EmailTemplate;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class EmailTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $templates = [
            [
                'name' => 'User Registration',
                'type' => 'registration',
                'subject' => 'Welcome to {{app_name}}!',
                'body_html' => '
                    <h1>Welcome {{user_name}}!</h1>
                    <p>Thank you for registering with {{app_name}}.</p>
                    <p>Please verify your email address by clicking the link below:</p>
                    <a href="{{verification_url}}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Verify Email</a>
                    <p>If you did not create this account, please ignore this email.</p>
                    <p>Best regards,<br>{{app_name}} Team</p>
                ',
                'body_text' => '
                    Welcome {{user_name}}!

                    Thank you for registering with {{app_name}}.

                    Please verify your email address by visiting: {{verification_url}}

                    If you did not create this account, please ignore this email.

                    Best regards,
                    {{app_name}} Team
                ',
                'variables' => ['user_name', 'app_name', 'verification_url'],
                'is_active' => true,
            ],
            [
                'name' => 'Password Reset',
                'type' => 'password_reset',
                'subject' => 'Reset Your Password - {{app_name}}',
                'body_html' => '
                    <h1>Password Reset Request</h1>
                    <p>Hello {{user_name}},</p>
                    <p>You have requested to reset your password for {{app_name}}.</p>
                    <p>Click the button below to reset your password:</p>
                    <a href="{{reset_url}}" style="background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a>
                    <p>This link will expire in 60 minutes.</p>
                    <p>If you did not request this password reset, please ignore this email.</p>
                    <p>Best regards,<br>{{app_name}} Team</p>
                ',
                'body_text' => '
                    Password Reset Request

                    Hello {{user_name}},

                    You have requested to reset your password for {{app_name}}.

                    Visit this link to reset your password: {{reset_url}}

                    This link will expire in 60 minutes.

                    If you did not request this password reset, please ignore this email.

                    Best regards,
                    {{app_name}} Team
                ',
                'variables' => ['user_name', 'app_name', 'reset_url'],
                'is_active' => true,
            ],
            [
                'name' => 'Welcome Email',
                'type' => 'welcome',
                'subject' => 'Welcome to {{app_name}} - Get Started!',
                'body_html' => '
                    <h1>Welcome to {{app_name}}, {{user_name}}!</h1>
                    <p>Your email has been verified and your account is now active.</p>
                    <p>Here are some things you can do:</p>
                    <ul>
                        <li>Complete your profile</li>
                        <li>Explore our features</li>
                        <li>Connect with other users</li>
                    </ul>
                    <a href="{{app_url}}" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Get Started</a>
                    <p>If you have any questions, feel free to contact our support team.</p>
                    <p>Best regards,<br>{{app_name}} Team</p>
                ',
                'body_text' => '
                    Welcome to {{app_name}}, {{user_name}}!

                    Your email has been verified and your account is now active.

                    Here are some things you can do:
                    - Complete your profile
                    - Explore our features
                    - Connect with other users

                    Visit {{app_url}} to get started.

                    If you have any questions, feel free to contact our support team.

                    Best regards,
                    {{app_name}} Team
                ',
                'variables' => ['user_name', 'app_name', 'app_url'],
                'is_active' => true,
            ],
        ];

        foreach ($templates as $template) {
            EmailTemplate::firstOrCreate(
                ['type' => $template['type']],
                $template
            );
        }
    }
}
