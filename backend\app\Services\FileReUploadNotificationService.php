<?php

namespace App\Services;

use App\Models\EmailNotification;
use App\Models\OrderFile;
use App\Models\PrintingOrder;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class FileReUploadNotificationService
{
    /**
     * Send notifications when a file is re-uploaded
     */
    public function sendFileReUploadNotifications(
        OrderFile $newFile, 
        OrderFile $previousFile, 
        User $uploader, 
        string $reason = null
    ): void {
        try {
            $order = $newFile->order;
            
            // Send notification to user (if re-uploaded by admin)
            if ($uploader->isAdmin() && $uploader->id !== $order->user_id) {
                $this->sendUserNotification($newFile, $previousFile, $order, $uploader, $reason);
            }
            
            // Send notification to admins (if re-uploaded by user)
            if (!$uploader->isAdmin()) {
                $this->sendAdminNotifications($newFile, $previousFile, $order, $uploader, $reason);
            }
            
            // Log the notification
            Log::info('File re-upload notifications sent', [
                'order_id' => $order->id,
                'file_id' => $newFile->id,
                'previous_file_id' => $previousFile->id,
                'uploader_id' => $uploader->id,
                'reason' => $reason
            ]);
            
        } catch (\Exception $e) {
            Log::error('Failed to send file re-upload notifications', [
                'error' => $e->getMessage(),
                'file_id' => $newFile->id ?? null,
                'order_id' => $newFile->printing_order_id ?? null
            ]);
        }
    }

    /**
     * Send notification to the order owner
     */
    private function sendUserNotification(
        OrderFile $newFile, 
        OrderFile $previousFile, 
        PrintingOrder $order, 
        User $uploader, 
        string $reason = null
    ): void {
        $subject = "File Re-uploaded for Order #{$order->order_number}";
        
        $body = $this->buildUserNotificationBody($newFile, $previousFile, $order, $uploader, $reason);
        
        EmailNotification::create([
            'user_id' => $order->user_id,
            'type' => 'file_reupload_user',
            'subject' => $subject,
            'body' => $body,
            'data' => [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'file_id' => $newFile->id,
                'previous_file_id' => $previousFile->id,
                'uploader_id' => $uploader->id,
                'uploader_name' => $uploader->name,
                'reason' => $reason,
                'file_name' => $newFile->original_name,
                'previous_file_name' => $previousFile->original_name,
            ],
            'status' => 'pending',
        ]);
    }

    /**
     * Send notifications to admin users
     */
    private function sendAdminNotifications(
        OrderFile $newFile, 
        OrderFile $previousFile, 
        PrintingOrder $order, 
        User $uploader, 
        string $reason = null
    ): void {
        $adminUsers = User::where('is_admin', true)->get();
        
        $subject = "File Re-uploaded by Customer - Order #{$order->order_number}";
        
        $body = $this->buildAdminNotificationBody($newFile, $previousFile, $order, $uploader, $reason);
        
        foreach ($adminUsers as $admin) {
            EmailNotification::create([
                'user_id' => $admin->id,
                'type' => 'file_reupload_admin',
                'subject' => $subject,
                'body' => $body,
                'data' => [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'file_id' => $newFile->id,
                    'previous_file_id' => $previousFile->id,
                    'uploader_id' => $uploader->id,
                    'uploader_name' => $uploader->name,
                    'customer_id' => $order->user_id,
                    'customer_name' => $order->user->name,
                    'reason' => $reason,
                    'file_name' => $newFile->original_name,
                    'previous_file_name' => $previousFile->original_name,
                ],
                'status' => 'pending',
            ]);
        }
    }

    /**
     * Build notification body for user
     */
    private function buildUserNotificationBody(
        OrderFile $newFile, 
        OrderFile $previousFile, 
        PrintingOrder $order, 
        User $uploader, 
        string $reason = null
    ): string {
        $body = "Hello {$order->user->name},\n\n";
        $body .= "A file has been re-uploaded for your order #{$order->order_number} by our admin team.\n\n";
        $body .= "File Details:\n";
        $body .= "- Previous File: {$previousFile->original_name}\n";
        $body .= "- New File: {$newFile->original_name}\n";
        $body .= "- File Type: {$newFile->file_type_label}\n";
        $body .= "- Re-uploaded by: {$uploader->name}\n";
        $body .= "- Re-upload Date: " . $newFile->re_uploaded_at->format('M j, Y g:i A') . "\n";
        
        if ($reason) {
            $body .= "- Reason: {$reason}\n";
        }
        
        $body .= "\nYou can view your order details and the updated file in your account dashboard.\n\n";
        $body .= "If you have any questions, please don't hesitate to contact us.\n\n";
        $body .= "Best regards,\n";
        $body .= config('app.name') . " Team";
        
        return $body;
    }

    /**
     * Build notification body for admin
     */
    private function buildAdminNotificationBody(
        OrderFile $newFile, 
        OrderFile $previousFile, 
        PrintingOrder $order, 
        User $uploader, 
        string $reason = null
    ): string {
        $body = "A customer has re-uploaded a file for their order.\n\n";
        $body .= "Order Details:\n";
        $body .= "- Order Number: #{$order->order_number}\n";
        $body .= "- Customer: {$order->user->name} ({$order->user->email})\n";
        $body .= "- Order Status: {$order->status_label}\n\n";
        
        $body .= "File Details:\n";
        $body .= "- Previous File: {$previousFile->original_name}\n";
        $body .= "- New File: {$newFile->original_name}\n";
        $body .= "- File Type: {$newFile->file_type_label}\n";
        $body .= "- Re-uploaded by: {$uploader->name}\n";
        $body .= "- Re-upload Date: " . $newFile->re_uploaded_at->format('M j, Y g:i A') . "\n";
        
        if ($reason) {
            $body .= "- Reason: {$reason}\n";
        }
        
        $body .= "\nPlease review the new file and update the order status if necessary.\n\n";
        $body .= "You can access the order and files through the admin panel.";
        
        return $body;
    }

    /**
     * Send notification when file re-upload fails
     */
    public function sendFileReUploadFailureNotification(
        User $user, 
        PrintingOrder $order, 
        string $fileName, 
        string $errorMessage
    ): void {
        try {
            $subject = "File Re-upload Failed - Order #{$order->order_number}";
            
            $body = "Hello {$user->name},\n\n";
            $body .= "We encountered an issue while trying to re-upload your file for order #{$order->order_number}.\n\n";
            $body .= "File: {$fileName}\n";
            $body .= "Error: {$errorMessage}\n\n";
            $body .= "Please try uploading the file again. If the problem persists, please contact our support team.\n\n";
            $body .= "Best regards,\n";
            $body .= config('app.name') . " Team";
            
            EmailNotification::create([
                'user_id' => $user->id,
                'type' => 'file_reupload_failure',
                'subject' => $subject,
                'body' => $body,
                'data' => [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'file_name' => $fileName,
                    'error_message' => $errorMessage,
                ],
                'status' => 'pending',
            ]);
            
        } catch (\Exception $e) {
            Log::error('Failed to send file re-upload failure notification', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
                'order_id' => $order->id
            ]);
        }
    }
}
